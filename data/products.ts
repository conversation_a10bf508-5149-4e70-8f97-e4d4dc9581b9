export type Product = {
  id: string;
  name: string;
  description: string;
  price: number;
  images: string[];
  category: string;
  rating: number;
  reviews: number;
  manufacturingTime: number; // in days
  merchant: {
    id: string;
    name: string;
    logo: string;
    rating: number;
  };
  specifications: {
    [key: string]: string;
  };
  shipping: {
    options: {
      name: string;
      price: number;
      estimatedDelivery: string;
    }[];
    countries: string[];
  };
};

export const sortOptions = [
  { label: "Price: Low to high", value: "price-asc" },
  { label: "Price: High to low", value: "price-desc" },
  { label: "Rating: High to low", value: "rating-desc" },
  { label: "Most popular", value: "popular" },
  { label: "Newest", value: "newest" },
  { label: "Manufacturing: Fastest", value: "manufacturing-asc" },
];

export const categoryOptions = [
  { label: "All categories", value: "all" },
  { label: "Electronics", value: "electronics" },
  { label: "Fashion", value: "fashion" },
  { label: "Home & Garden", value: "home" },
  { label: "Beauty", value: "beauty" },
  { label: "Sports", value: "sports" },
];

export const priceRangeOptions = [
  { label: "All prices", value: "all" },
  { label: "Under $50", value: "0-50" },
  { label: "$50 to $100", value: "50-100" },
  { label: "$100 to $200", value: "100-200" },
  { label: "$200 to $500", value: "200-500" },
  { label: "$500 & Above", value: "500-99999" },
];

export const manufacturingTimeOptions = [
  { label: "Any time", value: "all" },
  { label: "1-3 days", value: "1-3" },
  { label: "4-7 days", value: "4-7" },
  { label: "8-14 days", value: "8-14" },
  { label: "15-30 days", value: "15-30" },
  { label: "30+ days", value: "30-90" },
];

export const ratingOptions = [
  { label: "Any rating", value: "all" },
  { label: "4 stars & above", value: "4" },
  { label: "3 stars & above", value: "3" },
  { label: "2 stars & above", value: "2" },
];

export const products: Product[] = [
  {
    id: "p1",
    name: "Wireless Bluetooth Headphones",
    description:
      "Premium noise-cancelling wireless headphones with 30-hour battery life and superior sound quality. Perfect for music lovers and travelers.",
    price: 159.99,
    images: [
      "/images/products/headphones-1.jpg",
      "/images/products/headphones-2.jpg",
      "/images/products/headphones-3.jpg",
    ],
    category: "electronics",
    rating: 4.7,
    reviews: 342,
    manufacturingTime: 5,
    merchant: {
      id: "m1",
      name: "AudioTech Inc",
      logo: "/images/merchants/audiotech.png",
      rating: 4.8,
    },
    specifications: {
      "Battery Life": "30 hours",
      "Bluetooth Version": "5.0",
      "Noise Cancellation": "Active",
      Weight: "250g",
      Color: "Matte Black",
      "Water Resistant": "IPX4",
    },
    shipping: {
      options: [
        {
          name: "Standard",
          price: 5.99,
          estimatedDelivery: "5-7 business days",
        },
        {
          name: "Express",
          price: 12.99,
          estimatedDelivery: "2-3 business days",
        },
      ],
      countries: ["United States", "Canada", "Mexico"],
    },
  },
  {
    id: "p2",
    name: 'Ultra HD Smart TV 55"',
    description:
      "Experience stunning 4K resolution with this smart TV featuring HDR, built-in streaming apps, and voice control. Transform your living room into a home theater.",
    price: 599.99,
    images: ["/images/products/tv-1.jpg", "/images/products/tv-2.jpg"],
    category: "electronics",
    rating: 4.5,
    reviews: 215,
    manufacturingTime: 12,
    merchant: {
      id: "m2",
      name: "VisionPlus Electronics",
      logo: "/images/merchants/visionplus.png",
      rating: 4.6,
    },
    specifications: {
      "Screen Size": "55 inches",
      Resolution: "4K Ultra HD (3840 x 2160)",
      HDR: "Yes",
      "Smart TV": "Yes",
      "HDMI Ports": "3",
      "USB Ports": "2",
      "Voice Control": "Yes",
    },
    shipping: {
      options: [
        {
          name: "Standard",
          price: 29.99,
          estimatedDelivery: "7-10 business days",
        },
        {
          name: "Premium",
          price: 49.99,
          estimatedDelivery: "3-5 business days",
        },
      ],
      countries: ["United States", "Canada"],
    },
  },
  {
    id: "p3",
    name: "Leather Crossbody Bag",
    description:
      "Handcrafted genuine leather crossbody bag with adjustable strap and multiple compartments. Perfect blend of style and functionality for everyday use.",
    price: 89.99,
    images: [
      "/images/products/bag-1.jpg",
      "/images/products/bag-2.jpg",
      "/images/products/bag-3.jpg",
      "/images/products/bag-4.jpg",
    ],
    category: "fashion",
    rating: 4.8,
    reviews: 178,
    manufacturingTime: 3,
    merchant: {
      id: "m3",
      name: "Artisan Leather Goods",
      logo: "/images/merchants/artisan.png",
      rating: 4.9,
    },
    specifications: {
      Material: "Genuine Leather",
      Dimensions: '9.5" x 7" x 3"',
      "Strap Length": 'Adjustable, up to 24"',
      Closure: "Zipper and magnetic snap",
      "Color Options": "Brown, Black, Tan",
      Pockets: "3 internal, 2 external",
    },
    shipping: {
      options: [
        {
          name: "Standard",
          price: 4.99,
          estimatedDelivery: "4-6 business days",
        },
        {
          name: "Express",
          price: 9.99,
          estimatedDelivery: "2-3 business days",
        },
      ],
      countries: ["United States", "Canada", "United Kingdom", "Australia"],
    },
  },
  {
    id: "p4",
    name: "Ergonomic Office Chair",
    description:
      "Adjustable ergonomic office chair with breathable mesh back, lumbar support, and padded armrests for all-day comfort during work or study.",
    price: 199.99,
    images: ["/images/products/chair-1.jpg", "/images/products/chair-2.jpg"],
    category: "home",
    rating: 4.3,
    reviews: 103,
    manufacturingTime: 8,
    merchant: {
      id: "m4",
      name: "Comfort Office Solutions",
      logo: "/images/merchants/comfort.png",
      rating: 4.5,
    },
    specifications: {
      Material: "Mesh back, foam seat",
      "Adjustable Height": 'Yes, 17" to 21"',
      Armrests: "Padded, adjustable",
      "Lumbar Support": "Yes",
      "Tilt Function": "Yes",
      "Weight Capacity": "300 lbs",
      Color: "Black",
    },
    shipping: {
      options: [
        {
          name: "Standard",
          price: 19.99,
          estimatedDelivery: "7-10 business days",
        },
        {
          name: "Express",
          price: 39.99,
          estimatedDelivery: "3-5 business days",
        },
      ],
      countries: ["United States", "Canada"],
    },
  },
  {
    id: "p5",
    name: "Vitamin C Serum",
    description:
      "Brightening vitamin C serum with hyaluronic acid and vitamin E. Reduces fine lines, improves skin tone, and boosts collagen production for radiant skin.",
    price: 34.99,
    images: ["/images/products/serum-1.jpg", "/images/products/serum-2.jpg"],
    category: "beauty",
    rating: 4.6,
    reviews: 289,
    manufacturingTime: 2,
    merchant: {
      id: "m5",
      name: "Pure Glow Skincare",
      logo: "/images/merchants/pureglow.png",
      rating: 4.7,
    },
    specifications: {
      Volume: "1 fl oz (30 ml)",
      "Key Ingredients": "20% Vitamin C, Hyaluronic Acid, Vitamin E",
      "Skin Type": "All skin types",
      Formulation: "Oil-free, Non-comedogenic",
      "Cruelty-free": "Yes",
      "Paraben-free": "Yes",
    },
    shipping: {
      options: [
        {
          name: "Standard",
          price: 3.99,
          estimatedDelivery: "3-5 business days",
        },
        {
          name: "Express",
          price: 7.99,
          estimatedDelivery: "1-2 business days",
        },
      ],
      countries: [
        "United States",
        "Canada",
        "United Kingdom",
        "European Union",
        "Australia",
      ],
    },
  },
  {
    id: "p6",
    name: "Smart Fitness Watch",
    description:
      "Advanced fitness tracker with heart rate monitoring, GPS, sleep tracking, and 7-day battery life. Waterproof design makes it perfect for all activities.",
    price: 129.99,
    images: [
      "/images/products/watch-1.jpg",
      "/images/products/watch-2.jpg",
      "/images/products/watch-3.jpg",
    ],
    category: "electronics",
    rating: 4.4,
    reviews: 176,
    manufacturingTime: 7,
    merchant: {
      id: "m6",
      name: "FitTech Gadgets",
      logo: "/images/merchants/fittech.png",
      rating: 4.2,
    },
    specifications: {
      Display: '1.3" Color Touchscreen',
      "Battery Life": "Up to 7 days",
      "Water Resistance": "50m water resistant",
      Sensors: "Heart rate, Accelerometer, GPS",
      Compatibility: "iOS 10.0+, Android 5.0+",
      Connectivity: "Bluetooth 5.0",
    },
    shipping: {
      options: [
        {
          name: "Standard",
          price: 4.99,
          estimatedDelivery: "4-6 business days",
        },
        {
          name: "Express",
          price: 9.99,
          estimatedDelivery: "2-3 business days",
        },
      ],
      countries: [
        "United States",
        "Canada",
        "United Kingdom",
        "Australia",
        "Japan",
      ],
    },
  },
  {
    id: "p7",
    name: "Yoga Mat with Carrying Strap",
    description:
      "Non-slip yoga mat made from eco-friendly TPE material. Includes alignment lines and carrying strap for easy transport to your yoga or pilates class.",
    price: 39.99,
    images: ["/images/products/yoga-1.jpg", "/images/products/yoga-2.jpg"],
    category: "sports",
    rating: 4.5,
    reviews: 132,
    manufacturingTime: 4,
    merchant: {
      id: "m7",
      name: "ZenFit Lifestyle",
      logo: "/images/merchants/zenfit.png",
      rating: 4.7,
    },
    specifications: {
      Material: "Eco-friendly TPE",
      Thickness: "6mm",
      Dimensions: '72" x 24"',
      "Non-slip Surface": "Yes",
      "Alignment Lines": "Yes",
      "Carrying Strap": "Included",
      "Color Options": "Purple, Blue, Green, Black",
    },
    shipping: {
      options: [
        {
          name: "Standard",
          price: 5.99,
          estimatedDelivery: "4-6 business days",
        },
        {
          name: "Express",
          price: 10.99,
          estimatedDelivery: "2-3 business days",
        },
      ],
      countries: [
        "United States",
        "Canada",
        "United Kingdom",
        "Australia",
        "Germany",
        "France",
      ],
    },
  },
  {
    id: "p8",
    name: "Cast Iron Dutch Oven",
    description:
      "Premium enameled cast iron dutch oven perfect for slow cooking, braising and roasting. Retains heat exceptionally well and can be used on all cooking surfaces.",
    price: 79.99,
    images: [
      "/images/products/dutch-oven-1.jpg",
      "/images/products/dutch-oven-2.jpg",
    ],
    category: "home",
    rating: 4.9,
    reviews: 207,
    manufacturingTime: 15,
    merchant: {
      id: "m8",
      name: "Culinary Classics",
      logo: "/images/merchants/culinary.png",
      rating: 4.8,
    },
    specifications: {
      Material: "Enameled Cast Iron",
      Capacity: "6 Quart",
      "Dishwasher Safe": "Yes",
      "Oven Safe": "Up to 500°F",
      "Compatible Surfaces": "Gas, Electric, Induction, Ceramic, Halogen, Oven",
      "Color Options": "Red, Blue, Black, Green",
    },
    shipping: {
      options: [
        {
          name: "Standard",
          price: 8.99,
          estimatedDelivery: "5-7 business days",
        },
        {
          name: "Express",
          price: 14.99,
          estimatedDelivery: "2-4 business days",
        },
      ],
      countries: ["United States", "Canada"],
    },
  },
];
