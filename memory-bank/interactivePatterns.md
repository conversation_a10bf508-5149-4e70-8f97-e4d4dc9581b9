# Interactive UI Patterns & Best Practices

## Overview

Documentation of the interactive patterns and best practices established during the implementation of the discussion interface.

## State Management Patterns

### Local State for Real-time Feel

```typescript
// Pattern: Use local state for immediate UI updates
const [localMessages, setLocalMessages] = useState<GroupMessage[]>(initialData);
const [localProducts, setLocalProducts] =
  useState<ProductSuggestion[]>(initialData);

// Benefit: Immediate feedback without waiting for server responses
// Use case: Chat interfaces, real-time collaboration tools
```

### Immutable State Updates

```typescript
// Pattern: Always create new objects/arrays for state updates
setLocalMessages((prev) => [...prev, newMessage]);
setLocalProducts((prev) =>
  prev.map((product) =>
    product.id === targetId
      ? { ...product, reactions: [...product.reactions, newReaction] }
      : product
  )
);

// Benefit: Reliable React re-renders and easier debugging
```

### Controlled Components

```typescript
// Pattern: All inputs should be controlled with state
<input
  value={messageText}
  onChange={(e) => setMessageText(e.target.value)}
  onKeyDown={handleKeyDown}
/>

// Benefit: Predictable behavior and easy validation
```

## User Interaction Patterns

### One Action Per User Enforcement

```typescript
// Pattern: Check for existing user action before adding new one
const existingReaction = reactions?.find((r) => r.userId === currentUserId);
if (existingReaction) {
  // Replace existing action
  reactions = reactions.map((r) =>
    r.userId === currentUserId ? { ...r, emoji: newEmoji } : r
  );
} else {
  // Add new action
  reactions = [...reactions, newReaction];
}

// Benefit: Prevents spam and ensures fair representation
// Use case: Voting systems, reactions, ratings
```

### Visual Ownership Indicators

```typescript
// Pattern: Highlight user's own content differently
className={`reaction-button ${
  reaction.userId === currentUserId ? 'bg-blue-50 border-blue-200' : ''
}`}

// Benefit: Clear indication of what user can control
// Use case: Comments, reactions, posts
```

### Hover-to-Reveal Actions

```typescript
// Pattern: Show destructive actions only on hover
<Button className="opacity-0 group-hover:opacity-100 transition-opacity">
  <Trash2 className="h-3 w-3" />
</Button>

// Benefit: Clean interface with accessible actions
// Use case: Delete buttons, secondary actions
```

## Mobile-First Design Patterns

### Floating Action Interface

```typescript
// Pattern: Fixed positioning for primary actions
<div className="fixed bottom-20 left-4 right-4 z-50">
  <div className="flex items-center gap-2">
    {/* Action buttons */}
    <div className="flex-1 relative">
      <input className="w-full rounded-full pr-12" />
      <Button className="absolute right-1 top-1">
        <Send />
      </Button>
    </div>
  </div>
</div>

// Benefit: Always accessible, thumb-friendly positioning
// Use case: Chat inputs, compose buttons, primary actions
```

### Touch-Friendly Sizing

```typescript
// Pattern: Minimum 44px touch targets
className = "h-10 w-10"; // 40px minimum
className = "h-12 w-12"; // 48px preferred for primary actions

// Benefit: Accessible on all devices and hand sizes
```

### Safe Area Considerations

```typescript
// Pattern: Account for system UI elements
className = "fixed bottom-20"; // Above bottom navigation
className = "pb-36"; // Content padding for floating elements

// Benefit: No overlap with system or app navigation
```

### Contextual Interface Display

```typescript
// Pattern: Show interface elements only when contextually relevant
const [activeTab, setActiveTab] = useState(defaultTab);

{
  activeTab === "discussion" && <FloatingMessageInput />;
}

// Benefit: Clean, focused interface per context
// Use case: Tab-specific tools, contextual actions, mode-dependent UI
```

### Modern Tab Navigation Design

```typescript
// Pattern: Round icons with labels below for mobile-first navigation
<TabsTrigger className="flex flex-col items-center gap-2 p-2">
  <div
    className={`w-10 h-10 rounded-full flex items-center justify-center ${
      isActive
        ? "bg-primary-foreground text-primary"
        : "bg-muted text-muted-foreground"
    }`}
  >
    <Icon className="h-5 w-5" />
  </div>
  <span className="text-xs font-medium">Label</span>
</TabsTrigger>

// Benefit: Modern app-like navigation with clear visual hierarchy
// Use case: Mobile apps, dashboard navigation, category selection
```

### Simplified Information Display

```typescript
// Pattern: Focus on core information, remove financial clutter
const MembersTab = () => (
  <div className="space-y-3">
    {members.map((member) => (
      <div className="flex items-center p-3 bg-muted rounded-lg">
        <Avatar>{member.name.charAt(0)}</Avatar>
        <div className="ml-3 flex-1">
          <p className="font-medium">{member.name}</p>
          <p className="text-sm text-muted-foreground">
            {member.isAdmin ? "Group Administrator" : "Member"}
          </p>
        </div>
      </div>
    ))}
  </div>
);

// Benefit: Clean, focused interface without information overload
// Use case: Member directories, user lists, role-based displays
```

## Real-time Feedback Patterns

### Immediate State Updates

```typescript
// Pattern: Update UI immediately, sync with server later
const handleAction = async () => {
  // 1. Update local state immediately
  setLocalState(newState);

  // 2. Sync with server (optional)
  try {
    await api.updateServer(newState);
  } catch (error) {
    // 3. Revert on error
    setLocalState(previousState);
  }
};

// Benefit: Responsive feel even with slow networks
```

### Progressive Enhancement

```typescript
// Pattern: Start with basic functionality, add enhancements
const [isEnhanced, setIsEnhanced] = useState(false);

useEffect(() => {
  // Enable enhanced features after initial render
  setIsEnhanced(true);
}, []);

// Benefit: Fast initial load with rich interactions
```

## Consensus & Voting Patterns

### Weighted Scoring Algorithm

```typescript
// Pattern: Categorize reactions and apply weights
const calculateConsensus = (reactions: Reaction[]) => {
  const positiveEmojis = ["👍", "❤️", "😍", "🎉", "🔥"];
  const negativeEmojis = ["👎", "😕", "❌", "💸", "⚠️"];

  const weights = reactions.map((r) => {
    if (positiveEmojis.includes(r.emoji)) return 1.0;
    if (negativeEmojis.includes(r.emoji)) return 0.0;
    return 0.5; // neutral
  });

  return Math.round(
    (weights.reduce((a, b) => a + b, 0) / weights.length) * 100
  );
};

// Benefit: Nuanced sentiment analysis beyond simple counts
```

### Visual Consensus Indicators

```typescript
// Pattern: Color-coded progress bars for quick assessment
const getConsensusColor = (score: number) => {
  if (score >= 80) return "bg-green-500";
  if (score >= 60) return "bg-yellow-500";
  return "bg-red-500";
};

// Benefit: Instant visual feedback on group sentiment
```

## Threading & Conversation Patterns

### Auto-Expanding Threads

```typescript
// Pattern: Automatically show new content
const handleSendReply = (parentId: number) => {
  // Add reply to messages
  setMessages((prev) => [...prev, newReply]);

  // Auto-expand thread to show new reply
  setExpandedThreads((prev) => {
    const newSet = new Set(prev);
    newSet.add(`thread-${parentId}`);
    return newSet;
  });
};

// Benefit: Users immediately see their contribution
```

### Visual Thread Hierarchy

```typescript
// Pattern: Clear visual distinction for nested content
<div className="ml-4 pl-4 border-l-2 border-gray-200">
  {threadMessages.map((message) => (
    <div className="bg-gray-50 rounded-lg p-2">
      {/* Thread message content */}
    </div>
  ))}
</div>

// Benefit: Clear conversation structure
```

## Error Prevention Patterns

### Disabled State Management

```typescript
// Pattern: Disable actions when invalid
<Button disabled={!inputText.trim()} onClick={handleSubmit}>
  Send
</Button>

// Benefit: Prevents invalid submissions
```

### Optimistic Updates with Rollback

```typescript
// Pattern: Show success immediately, handle failures gracefully
const handleOptimisticAction = async () => {
  const previousState = currentState;

  // Show success immediately
  setState(newState);

  try {
    await api.performAction();
  } catch (error) {
    // Rollback on failure
    setState(previousState);
    showErrorMessage();
  }
};

// Benefit: Fast UI with reliable error handling
```

## Performance Patterns

### Efficient Re-renders

```typescript
// Pattern: Memoize expensive calculations
const consensusScore = useMemo(
  () => calculateConsensusScore(reactions),
  [reactions]
);

// Pattern: Optimize list rendering
const messageList = useMemo(
  () => messages.filter((msg) => !msg.parentMessageId),
  [messages]
);

// Benefit: Smooth interactions even with large datasets
```

### Lazy Loading

```typescript
// Pattern: Load content only when needed
{
  expandedThreads.has(threadId) && <ThreadMessages threadId={threadId} />;
}

// Benefit: Faster initial render and lower memory usage
```

## Accessibility Patterns

### Keyboard Navigation

```typescript
// Pattern: Support keyboard shortcuts
onKeyDown={(e) => {
  if (e.key === "Enter" && !e.shiftKey) {
    e.preventDefault()
    handleSend()
  }
}}

// Benefit: Accessible to keyboard-only users
```

### Screen Reader Support

```typescript
// Pattern: Meaningful labels and descriptions
<Button
  aria-label={`Delete message from ${message.user}`}
  title="Delete message"
>
  <Trash2 />
</Button>

// Benefit: Accessible to screen reader users
```

## Testing Patterns

### State Testing

```typescript
// Pattern: Test state changes, not implementation
test("should add reaction when user clicks emoji", () => {
  render(<MessageComponent />);

  fireEvent.click(screen.getByLabelText("Add reaction"));
  fireEvent.click(screen.getByText("👍"));

  expect(screen.getByText("👍 1")).toBeInTheDocument();
});

// Benefit: Tests user behavior, not internal code
```

### Integration Testing

```typescript
// Pattern: Test complete user workflows
test("should allow user to reply to message", () => {
  render(<DiscussionInterface />);

  // Click reply button
  fireEvent.click(screen.getByLabelText("Reply to message"));

  // Type reply
  fireEvent.change(screen.getByPlaceholderText("Type your reply"), {
    target: { value: "Test reply" },
  });

  // Send reply
  fireEvent.click(screen.getByText("Send"));

  // Verify reply appears
  expect(screen.getByText("Test reply")).toBeInTheDocument();
});

// Benefit: Ensures complete features work end-to-end
```

## Key Learnings

### User Experience

1. **Immediate Feedback**: Users expect instant responses to their actions
2. **Visual Ownership**: Clear indication of what users can control
3. **Fair Systems**: Prevent manipulation while allowing expression
4. **Mobile-First**: Touch-friendly design improves all experiences
5. **Contextual Interfaces**: Show relevant tools only when needed
6. **Information Hierarchy**: Focus on core information, avoid clutter
7. **Modern Navigation**: Round icons with labels provide intuitive navigation

### Technical Implementation

1. **Local State**: Sufficient for real-time feel in most cases
2. **Immutable Updates**: Essential for reliable React behavior
3. **Progressive Enhancement**: Start simple, add complexity gradually
4. **Error Handling**: Always plan for failure scenarios

### Performance

1. **Memoization**: Critical for smooth interactions with large datasets
2. **Lazy Loading**: Load only what's needed when it's needed
3. **Efficient Rendering**: Minimize unnecessary re-renders
4. **Optimistic Updates**: Show success immediately, handle errors gracefully
