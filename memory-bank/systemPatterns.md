# System Patterns

## Architecture Overview

~~The system follows a modern web application architecture using Next.js 15 with App Router, which combines server and client
components. The database layer uses PostgreSQL via Neon with Drizzle ORM. Authentication is handled by Clerk, and authorization
by Permit.io. The system follows a mobile-first design approach with responsive components.~~

The Khenesis platform is architected as a modern web application with the following key components:

- **Frontend**: Next.js App Router with React components
- **Backend**: Next.js API routes for server-side logic
- **Database**: PostgreSQL (via cloud provider)
- **Authentication**: Email/password and social login
- **State Management**: React Context API and React Query
- **UI Components**: Custom components built on shadcn/ui
- **Styling**: TailwindCSS for utility-first styling

## Key Process Flows

### Collaborative Group Buying Flow

```mermaid
flowchart TD
    Start([Start]) --> CreateGroup[Create shopping group]
    CreateGroup --> InviteMembers[Invite members]
    InviteMembers --> GroupChat[Group chat begins]

    %% Collaborative Decision Making Process
    GroupChat --> ProductSuggestion[Members suggest products]

    ProductSuggestion --> ProductSource{Product source?}
    ProductSource -->|Internal catalog| SearchSystem[Search system catalog]
    ProductSource -->|External| UploadImage[Upload product image]

    SearchSystem --> AddToDiscussion[Add to group discussion]
    UploadImage --> AddToDiscussion

    AddToDiscussion --> Compare[Compare products]
    Compare --> Discussion[Group discussion/opinions]

    Discussion --> Voting[Voting on products]
    Voting --> DecisionMade{Decision made?}

    DecisionMade -->|No| ProductSuggestion
    DecisionMade -->|Yes| ProductLocation{Product location?}

    %% Quote Request Process (Consolidated)
    ProductLocation -->|In system| RequestQuote[Request quote from merchant]
    ProductLocation -->|External product| CustomQuoteRequest[Request custom quote with image]

    RequestQuote --> MerchantSelection[Select merchants to request from]
    CustomQuoteRequest --> MerchantSelection

    MerchantSelection --> SpecifyRequirements[Specify requirements and timeline]
    SpecifyRequirements --> SubmitRequest[Submit quote request]

    SubmitRequest --> MerchantResponse[Merchant provides quote]

    MerchantResponse --> MultipleQuotes{Multiple quotes?}
    MultipleQuotes -->|Yes| CompareQuotes[Compare quotes]
    MultipleQuotes -->|No| QuoteAccept

    CompareQuotes --> QuoteAccept{Accept quote?}

    QuoteAccept -->|No| Discussion
    QuoteAccept -->|Yes| GroupPayment[Group payment process]

    %% Payment and Fulfillment Process
    GroupPayment --> PaymentComplete{Payment complete?}
    PaymentComplete -->|No| GroupPayment
    PaymentComplete -->|Yes| OrderConfirm[Order confirmed]

    OrderConfirm --> ManufactureDemand{Requires manufacturing?}
    ManufactureDemand -->|Yes| Manufacturing[Manufacturing process]
    ManufactureDemand -->|No| Shipping[Product shipped]

    Manufacturing --> Shipping
    Shipping --> Delivery[Product delivered]
    Delivery --> DocumentGenerate[Generate receipt/invoice]
    DocumentGenerate --> End([End])
```

### Quote Request Flow

```mermaid
flowchart TD
    Start([Start Quote Request]) --> SelectProduct[Select product from suggestions]
    SelectProduct --> SelectMerchants[Select merchants to request quotes from]
    SelectMerchants --> SpecifyDetails[Specify delivery timeline and budget]
    SpecifyDetails --> AddDetails[Add additional information]
    AddDetails --> SubmitRequest[Submit quote request]
    SubmitRequest --> NotifyMerchants[Notify selected merchants]
    NotifyMerchants --> WaitResponse[Wait for merchant responses]
    WaitResponse --> QuotesReceived[Quotes received]
    QuotesReceived --> CompareQuotes[Compare quotes in comparison view]
    CompareQuotes --> Decision{Accept quote?}
    Decision -->|Yes| AcceptQuote[Accept selected quote]
    Decision -->|No| RequestMore[Request more quotes]
    RequestMore --> SelectMerchants
    AcceptQuote --> NotifyGroup[Notify group members]
    NotifyGroup --> ProceedPayment[Proceed to payment]
    ProceedPayment --> End([End])
```

### Payment Process Flow

```mermaid
flowchart TD
    Start([Start Payment]) --> SelectMethod[Member selects payment method]
    SelectMethod --> PayMethod{Payment\nmethod?}

    PayMethod -->|Cash| RecordCash[Record cash payment]
    PayMethod -->|EFT| ProcessEFT[Record EFT details]
    PayMethod -->|Debit/Credit| ProcessCard[Process card payment]
    PayMethod -->|Recurring| SetupRecurring[Setup recurring payment]

    RecordCash --> MerchantVerify[Merchant verifies payment]
    ProcessEFT --> MerchantVerify
    ProcessCard --> PaymentGateway[Payment gateway processing]
    SetupRecurring --> SchedulePayment[Schedule recurring payments]

    PaymentGateway --> PaymentStatus{Payment\nsuccessful?}
    SchedulePayment --> FirstPayment[Process first payment]
    FirstPayment --> PaymentGateway

    PaymentStatus -->|No| FailureNotify[Notify payment failure]
    PaymentStatus -->|Yes| UpdateBalance[Update group balance]

    MerchantVerify --> VerifyStatus{Verified?}
    VerifyStatus -->|No| FailureNotify
    VerifyStatus -->|Yes| UpdateBalance


## Current Implementation
[x] The system follows a modern web application architecture using Next.js with App Router, which combines server and client
components. The styling is implemented using TailwindCSS with mobile-first principles. The UI component library is built on
shadcn/ui, which provides accessible and customizable components based on Radix UI primitives.
    FailureNotify --> RetryPrompt[Prompt to retry]
    RetryPrompt --> RetryDecision{Retry?}
    RetryDecision -->|Yes| SelectMethod
    RetryDecision -->|No| CancelPayment[Cancel payment attempt]
    CancelPayment --> End([End])

 ### Component Architecture
    UpdateBalance --> GenerateReceipt[Generate receipt]
    GenerateReceipt --> CheckComplete{Group payment\ncomplete?}


- [x] **MobileLayout**: Container component that handles mobile viewport and safe areas
- [x] **MobileNav**: Bottom navigation component for primary navigation
- [x] **Card**: Container for structured content blocks
- [x] **Button**: Interactive elements with various styles and states
- [x] **Tabs**: Content organization for switching between views
- [x] **Input**: Form input elements for user data entry
- [x] **ProductCard**: Component for displaying product information in catalog
- [x] **ImageCarousel**: Swipeable image carousel for product photos
- [x] **FilterSheet**: Bottom sheet component for mobile filtering
- [x] **SortSelector**: Dropdown for sorting product results

### UI Patterns
    CheckComplete -->|No| NotifyProgress[Notify group of progress]
    NotifyProgress --> End

    CheckComplete -->|Yes| OrderConfirm[Confirm order with merchant]
    OrderConfirm --> NotifyComplete[Notify group completion]
    NotifyComplete --> End
```

### Manufacturing and Shipping Flow

```mermaid
flowchart TD
    A[MobileLayout] --> B[Main Content]
    A --> C[MobileNav]
    B --> D[Card Components]
    D --> E[Content Blocks]
    D --> F[Interactive Elements]
    E --> G[Typography]
    E --> H[Information Display]
    F --> I[Buttons]
    F --> J[Tabs]
    F --> K[Form Inputs]
    Start([Order Confirmed]) --> CheckManufacturing{Requires\nmanufacturing?}
    B --> L[Product Catalog]
    L --> M[ProductCard]
    M --> N[ImageCarousel]
    M --> O[Product Info]
    M --> P[Action Buttons]
    CheckManufacturing -->|No| PrepareShipping[Prepare for shipping]
    L --> Q[Filter Components]
    Q --> R[FilterSheet]
    Q --> S[SortSelector]
    Q --> T[CategorySelector]


```

    CheckManufacturing -->|Yes| StartManufacturing[Start manufacturing process]
    StartManufacturing --> SetEstimate[Set estimated completion]
    SetEstimate --> NotifyTimeline[Notify customers of timeline]

## Design Patterns

    NotifyTimeline --> UpdateProgress[Update manufacturing progress]
    UpdateProgress --> UploadPhotos[Upload progress photos]
    UploadPhotos --> ManufacturingComplete{Manufacturing\ncomplete?}

    ManufacturingComplete -->|No| UpdateProgress
    ManufacturingComplete -->|Yes| NotifyComplete[Notify manufacturing completion]

    NotifyComplete --> PrepareShipping
    PrepareShipping --> CreateShipment[Create shipment]

    CreateShipment --> GenerateTracking[Generate tracking number]
    GenerateTracking --> NotifyShipping[Notify customers of shipping]

    NotifyShipping --> TrackingUpdates[Regular tracking updates]
    TrackingUpdates --> DeliveryComplete{Delivery\ncomplete?}

    DeliveryComplete -->|No| TrackingUpdates
    DeliveryComplete -->|Yes| DeliveryConfirmation[Delivery confirmation]

    DeliveryConfirmation --> GenerateInvoice[Generate final invoice]
    GenerateInvoice --> CloseOrder[Close order]
    CloseOrder --> End([End])

```

## Design Patterns

- ~~**Server Components**: For data fetching and initial rendering~~
- ~~**Client Components**: For interactive elements and state management~~
- ~~**Repository Pattern**: For database access via Drizzle ORM~~
- ~~**Context Providers**: For shared state across components~~
- ~~**Custom Hooks**: For reusable logic and data fetching~~
- [x] **Mobile-First Design**: All components designed for mobile viewport first
- [x] **Responsive UI**: Layout adjusts based on viewport size
- [x] **Safe Area Management**: Components respect device safe areas (notches, etc.)
- [x] **Touch-Friendly Targets**: Minimum 44px touch targets for interactive elements
- [x] **Visual Hierarchy**: Clear organization of information importance
- [x] **Component Composition**: UI built from composable, reusable components
- [x] **Progressive Disclosure**: Complex features revealed progressively
- [x] **Bottom Sheet Pattern**: Mobile-friendly alternative to modals for filters
- [x] **Swipe Gestures**: Touch interactions for image carousels and actions
- [x] **Skeleton Loading**: Loading state placeholders matching content shape

## Data Models
## Component Relationships
### Core Entities

- **User**: Represents a customer, merchant, or administrator
- **Product**: Catalog items available for purchase
- **BuyingGroup**: Collaborative purchasing group
- **GroupMember**: Users who are part of a buying group
- **ProductSuggestion**: Products suggested by group members
- **Vote**: Member votes on product suggestions
- **Quote**: Price quotes from merchants for a specific product/group
- **Payment**: Individual payments made by group members
- **ManufacturingUpdate**: Updates on product manufacturing status
- **ShippingDetail**: Shipping information and tracking
- **GroupMessage**: Messages in the group discussion
- **Document**: Generated documents (quotes, invoices, receipts)

### Key Relationships

- A **User** can be a member of multiple **BuyingGroups**
- A **BuyingGroup** contains multiple **GroupMembers**
- **GroupMembers** can suggest multiple **ProductSuggestions**
- **GroupMembers** can cast **Votes** on **ProductSuggestions**
- A **BuyingGroup** can request multiple **Quotes**
- A **BuyingGroup** is associated with a selected **Product** or custom request
- **GroupMembers** make **Payments** toward the group purchase
- A **Quote** once accepted leads to **ManufacturingUpdates** and **ShippingDetails**
- A **BuyingGroup** generates various **Documents** throughout the process

## Component Architecture

### UI Component Hierarchy

- **Layouts**

  - [x] `MobileLayout`: Mobile-optimized layout with bottom navigation
  - [ ] `DesktopLayout`: Desktop layout with sidebar navigation

- **Product Components**

  - [x] `ProductCard`: Display product in grid/list view
  - [x] `ProductGrid`: Display product catalog with filtering
  - [x] `ProductDetail`: Detailed product view with specs

- **Group Components**
## Component Relationships
  - [x] `GroupCreationForm`: Multi-step form for creating groups
  - [x] `MemberInvitation`: Interface for inviting members
  - [ ] `ProductSuggestion`: Interface for suggesting products
  - [ ] `VotingInterface`: UI for voting on products
  - [ ] `QuoteComparison`: UI for comparing multiple quotes
  - [ ] `GroupChat`: Messaging interface for group discussion
  - [ ] `GroupProgress`: Visual indicator of group progress

- **Payment Components**

  - [x] `PaymentMethodSelector`: Interface for selecting payment method
  - [x] `PaymentForm`: Form for making payments
  - [ ] `PaymentSummary`: Summary of group payments
  - [ ] `PaymentStatusIndicator`: Visual indicator of payment status

- **Manufacturing & Shipping Components**
  - [ ] `ManufacturingProgress`: Visual indicator of manufacturing progress
  - [ ] `ShippingTracker`: Interface for tracking shipments
  - [ ] `TimelineVisualization`: Visual timeline of order process

## API Patterns

- [x] **Layouts → Pages**: Pages are wrapped in appropriate layout components
- [x] **Navigation → App**: Navigation component connected to app routing
- [x] **Cards → Content**: Structured content displayed in card containers
- [x] **Tabs → Sections**: Related content grouped in tab interfaces
- [x] **Buttons → Actions**: User interactions triggered via button components
- [x] **Catalog → ProductCards**: Product listings composed of product cards
- [x] **ProductCard → Detail**: Product cards link to detailed product pages
- [x] **Filters → Results**: Filter components manipulate product catalog display

### RESTful Endpoints
## Data Flow
- `/api/groups`: Group management endpoints
- `/api/groups/:id/members`: Group membership endpoints
- `/api/groups/:id/suggestions`: Product suggestion endpoints
- `/api/groups/:id/votes`: Voting endpoints
- `/api/groups/:id/chat`: Group chat endpoints
- `/api/quotes`: Quote management endpoints
- `/api/payments`: Payment processing endpoints
- `/api/manufacturing`: Manufacturing updates endpoints
- `/api/shipping`: Shipping tracking endpoints
- `/api/documents`: Document generation endpoints
~~The application follows a unidirectional data flow pattern. Server components fetch data from the database and pass it to
client components. Interactive components use React Query for client-side state management and data mutations. Real-time updates
use a combination of polling and webhook-triggered invalidations.~~
### Authentication and Authorization
## Data Flow
- JWT-based authentication
- Role-based access control (Admin, Merchant, Customer)
- Group-level permissions (Creator, Member)
- Resource-level permissions based on group membership
~~The application follows a unidirectional data flow pattern. Server components fetch data from the database and pass it to
client components. Interactive components use React Query for client-side state management and data mutations. Real-time updates
use a combination of polling and webhook-triggered invalidations.~~
## State Management
~~The application follows a unidirectional data flow pattern. Server components fetch data from the database and pass it to
client components. Interactive components use React Query for client-side state management and data mutations. Real-time updates
use a combination of polling and webhook-triggered invalidations.~~

[x] For static prototyping, pages currently use mock data objects directly in components. When connected to a backend, the
application will follow a unidirectional data flow pattern with server components fetching data and passing it to client
components.

- React Context for global UI state
- React Query for server state management
- Local component state for UI-specific state
## Key Technical Decisions
1. ~~**Next.js App Router**: Provides optimal combination of SSR, CSR, and static generation~~
2. ~~**Drizzle ORM**: Type-safe database access with schema definitions~~
3. ~~**Clerk Authentication**: Simplified auth with multi-provider support~~
4. ~~**Mobile-First Design**: Ensuring optimal experience on mobile devices first~~
5. ~~**Component-Based Architecture**: Modular, reusable UI components~~
6. [x] **Mobile-First Design**: Ensuring optimal experience on mobile devices first
7. [x] **Component-Based Architecture**: Modular, reusable UI components
8. [x] **CSS Variables for Theming**: Using CSS variables for consistent styling
9. [x] **shadcn/ui Component Library**: Leveraging established UI patterns
10. [x] **Responsive Layout System**: Adapting to different viewport sizes
11. [x] **Touch-First Interactions**: Designing primarily for touch rather than mouse
12. [x] **Visual Feedback Patterns**: Clear indication of interactive states

## Responsive Design Patterns

- Mobile-first approach with responsive breakpoints
- Bottom navigation on mobile, sidebar on desktop
- Touch-friendly UI elements on mobile
- Optimized layouts for different screen sizes

## Code Organization

- ~~**Feature-Based Structure**: Components organized by feature area~~
- ~~**Shared UI Component Library**: Reusable UI elements~~
- ~~**Custom Hooks Layer**: Encapsulated data fetching and business logic~~
- ~~**API Routes by Resource**: API endpoints organized by resource type~~
- ~~**Service Layer Pattern**: Separating business logic from data access~~
- [x] **app/**: Page components organized by route
- [x] **components/**: Reusable UI components
  - [x] **components/ui/**: Basic UI elements (buttons, inputs, etc.)
  - [x] **components/layouts/**: Layout components (MobileLayout, etc.)
  - [x] **components/product/**: Product-related components
  - [x] **components/merchant/**: Merchant dashboard components
- [x] **lib/**: Utility functions and shared logic

## Error Handling

- Global error boundary for React component errors
- API error handling with consistent response format
- Retry logic for transient failures
- Friendly error messages with recovery options

## Performance Considerations

- ~~**Server component usage for reduced client-side JavaScript**~~
- ~~**Image optimization for mobile devices**~~
- ~~**Lazy loading of components not in viewport**~~
- ~~**Optimistic UI updates for responsive interactions**~~
- ~~**Prefetching critical routes for faster navigation**~~
- [x] **Optimized TailwindCSS**: Only including used styles in the final bundle
- [x] **CSS Variables**: Efficient theme application without unnecessary re-renders
- [x] **Mobile Optimization**: Prioritizing performance on mobile devices
- [x] **Touch-Friendly Interactions**: Ensuring smooth touch interaction patterns
- [x] **Safe Area Insets**: Proper handling of device notches and system UI
- [x] **Responsive Images**: Proper sizing of product images for different viewports
- [x] **Virtual Lists**: Efficient rendering of large product catalogs
- [x] **Intersection Observer**: Loading content as it comes into viewport

## UI/UX Patterns

- [x] **Card Grids**: Responsive grid layouts for product catalogs
- [x] **Bottom Sheets**: Mobile-friendly alternative to modal dialogs
- [x] **Empty States**: Helpful guidance when lists or search results are empty
- [x] **Filter Chips**: Visual indicators of active filters with easy removal
- [x] **Persistent Filters**: Filter state maintained during navigation
- [x] **Sticky Headers**: Key navigation elements remain accessible during scroll
- [x] **Pull-to-Refresh**: Mobile pattern for refreshing product listings
- Optimistic UI updates for improved perceived performance
- Efficient data fetching with React Query (caching, deduplication)
- Image optimization for product photos
- Pagination for large data sets
- Progressive loading for complex UI components
```

## March 23, 2024 Updates

- [x] Added Quote Request Flow diagram to document the detailed process for requesting quotes
- [x] Added Quote Requests as a core entity in the Data Models section
- [x] Updated the Collaborative Group Buying Flow to include more detailed quote request steps
- [x] Implemented the QuoteRequest component in the UI Component Hierarchy for requesting quotes from merchants

## March 24, 2024 Updates

- [x] Relocated QuoteRequest component from the product suggestions tab to the quotes tab
- [x] Improved UI organization by consolidating quote-related functionality in one location
- [x] Enhanced user experience by grouping together related quote functionality
- [x] Updated the Quote Request Flow to align with the new UI placement

## March 25, 2024 Updates

- [x] Enhanced the Discussion Tab with image attachment and file upload functionality
- [x] Updated the Collaborative Group Buying Flow to emphasize media sharing in discussions
- [x] Added file attachments as part of the Message entity in the data model
- [x] Implemented integration between product suggestions and discussion messaging
