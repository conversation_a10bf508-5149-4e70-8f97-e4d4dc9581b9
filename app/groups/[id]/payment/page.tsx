import { MobileLayout } from "@/components/layouts/mobile-layout";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  Card<PERSON>ontent,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  CreditCard,
  ChevronLeft,
  Landmark,
  Wallet,
  Calendar,
  ArrowRight,
  Banknote,
} from "lucide-react";
import Link from "next/link";

export default function PaymentPage({ params }: { params: { id: string } }) {
  return (
    <MobileLayout showNav={false}>
      <div className="relative bg-primary text-primary-foreground p-4">
        <Link href={`/groups/${params.id}`} className="flex items-center mb-4">
          <ChevronLeft size={20} />
          <span className="ml-1">Back to Group</span>
        </Link>
        <h1 className="text-fluid-xl font-bold">Make a Payment</h1>
      </div>

      <div className="px-4 py-6">
        <Card className="mb-6">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Payment Amount</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span>Your share:</span>
                <span className="font-medium">$700.00</span>
              </div>
              <div className="flex items-center justify-between">
                <span>Amount paid:</span>
                <span className="font-medium">$350.00</span>
              </div>
              <div className="flex items-center justify-between text-primary">
                <span>Remaining:</span>
                <span className="font-medium">$350.00</span>
              </div>
              <div className="pt-4">
                <label className="block text-sm font-medium mb-2">
                  Payment amount:
                </label>
                <div className="relative">
                  <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                    $
                  </span>
                  <input
                    type="number"
                    defaultValue="100.00"
                    min="10"
                    max="350"
                    step="10"
                    aria-label="Payment amount in dollars"
                    className="flex h-10 w-full rounded-md border border-input bg-background pl-8 pr-3 py-2 text-xl font-medium ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Tabs defaultValue="card" className="mb-6">
          <TabsList className="w-full grid grid-cols-4">
            <TabsTrigger value="card">
              <CreditCard className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Card</span>
            </TabsTrigger>
            <TabsTrigger value="bank">
              <Landmark className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Bank</span>
            </TabsTrigger>
            <TabsTrigger value="wallet">
              <Wallet className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Wallet</span>
            </TabsTrigger>
            <TabsTrigger value="cash">
              <Banknote className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Cash</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="card" className="mt-4 space-y-4">
            <div className="space-y-4">
              <div>
                <label
                  htmlFor="cardNumber"
                  className="block text-sm font-medium mb-1"
                >
                  Card Number
                </label>
                <input
                  id="cardNumber"
                  type="text"
                  placeholder="1234 5678 9012 3456"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label
                    htmlFor="expiry"
                    className="block text-sm font-medium mb-1"
                  >
                    Expiry Date
                  </label>
                  <div className="relative">
                    <input
                      id="expiry"
                      type="text"
                      placeholder="MM/YY"
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    />
                    <Calendar className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  </div>
                </div>

                <div>
                  <label
                    htmlFor="cvc"
                    className="block text-sm font-medium mb-1"
                  >
                    CVC
                  </label>
                  <input
                    id="cvc"
                    type="text"
                    placeholder="123"
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  />
                </div>
              </div>

              <div>
                <label
                  htmlFor="name"
                  className="block text-sm font-medium mb-1"
                >
                  Cardholder Name
                </label>
                <input
                  id="name"
                  type="text"
                  placeholder="John Smith"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                />
              </div>

              <div className="flex items-center">
                <input
                  id="saveCard"
                  type="checkbox"
                  className="h-4 w-4 border border-primary rounded bg-background"
                />
                <label htmlFor="saveCard" className="ml-2 text-sm">
                  Save card for future payments
                </label>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="bank" className="mt-4 space-y-4">
            <div className="bg-muted p-4 rounded-md space-y-2">
              <p className="text-sm">Bank transfer information:</p>
              <div className="grid grid-cols-3 text-sm">
                <span className="text-muted-foreground">Bank:</span>
                <span className="col-span-2 font-medium">Universal Bank</span>
              </div>
              <div className="grid grid-cols-3 text-sm">
                <span className="text-muted-foreground">Account:</span>
                <span className="col-span-2 font-medium">**********</span>
              </div>
              <div className="grid grid-cols-3 text-sm">
                <span className="text-muted-foreground">Reference:</span>
                <span className="col-span-2 font-medium">
                  GRP{params.id}-USER123
                </span>
              </div>
              <p className="text-xs text-muted-foreground mt-2">
                Once you've made the transfer, click the button below to record
                your payment.
              </p>
            </div>
          </TabsContent>

          <TabsContent value="wallet" className="mt-4 space-y-2">
            <div className="space-y-2">
              <div className="border rounded-md p-3 flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-primary-foreground">
                    <Wallet className="h-4 w-4" />
                  </div>
                  <div className="ml-3">
                    <p className="font-medium">PayWallet</p>
                    <p className="text-xs text-muted-foreground">
                      Balance: $280.00
                    </p>
                  </div>
                </div>
                <input
                  type="radio"
                  name="wallet"
                  aria-label="Select PayWallet payment method"
                  className="h-4 w-4"
                  defaultChecked
                />
              </div>

              <div className="border rounded-md p-3 flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-secondary rounded-full flex items-center justify-center text-secondary-foreground">
                    <Wallet className="h-4 w-4" />
                  </div>
                  <div className="ml-3">
                    <p className="font-medium">CryptoWallet</p>
                    <p className="text-xs text-muted-foreground">
                      Connect wallet
                    </p>
                  </div>
                </div>
                <input
                  type="radio"
                  name="wallet"
                  aria-label="Select CryptoWallet payment method"
                  className="h-4 w-4"
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="cash" className="mt-4 space-y-4">
            <div className="bg-muted p-4 rounded-md space-y-2">
              <p className="text-sm">Cash payment instructions:</p>
              <p className="text-sm">
                You can make a cash payment to the group administrator. Once
                recorded, it will appear in your payment history.
              </p>
              <div className="mt-2">
                <label
                  htmlFor="cashDate"
                  className="block text-sm font-medium mb-1"
                >
                  Expected payment date
                </label>
                <input
                  id="cashDate"
                  type="date"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                />
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <div className="flex justify-between items-center mb-6 bg-muted p-3 rounded-md">
          <div>
            <p className="text-sm font-medium">Total Payment</p>
            <p className="text-lg font-bold">$100.00</p>
          </div>
          <Button size="lg" className="gap-2">
            <span>Continue</span>
            <ArrowRight className="h-4 w-4" />
          </Button>
        </div>

        <div className="text-center text-xs text-muted-foreground">
          <p>
            Payments are processed securely and contribute directly to your
            group purchase.
          </p>
          <p className="mt-1">
            By proceeding, you agree to the terms and conditions of the group
            buy.
          </p>
        </div>
      </div>
    </MobileLayout>
  );
}
