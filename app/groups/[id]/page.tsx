"use client";

import { useState } from "react";
import { MobileLayout } from "@/components/layouts/mobile-layout";
import { Button } from "@/components/ui/button";
import {
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON>er,
  Card<PERSON><PERSON>er,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import {
  Users,
  MessageSquare,
  Clock,
  ChevronLeft,
  CreditCard,
  TrendingUp,
  Truck,
  ShoppingBag,
  FileText,
  Upload,
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { GroupSuggestionForm } from "@/app/components/group-suggestion-form";
// Interfaces moved here after removing voting component
export interface ProductSuggestion {
  id: number;
  name: string;
  description: string;
  price: number;
  image: string;
  source: "internal" | "external";
  merchant?: string | null;
}

export interface GroupMember {
  id: number;
  name: string;
  isAdmin: boolean;
  amountPaid: number;
}
import { QuoteComparison } from "@/app/components/quote-comparison";
import { QuoteRequest } from "@/app/components/quote-request";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

// Mock data - would be fetched based on [id] in real app
const groupData = {
  id: 1,
  name: "Living Room Remodel Group",
  description:
    "A collective purchase for premium living room furniture at wholesale prices.",
  stage: "suggestion", // suggestion, discussion, quote, payment, manufacturing, shipping
  suggestedProducts: [
    {
      id: 1,
      name: "Premium Leather Sofa Set",
      price: 3500,
      image: "/images/placeholder.png",
      description: "Genuine leather sofa set with matching ottoman",
      merchant: "Luxury Furniture Co.",
      source: "internal" as const, // internal or external
    },
    {
      id: 2,
      name: "Modern Fabric Sectional",
      price: 2800,
      image: "/images/placeholder.png",
      description: "L-shaped sectional with chaise lounge in premium fabric",
      merchant: "Contemporary Home",
      source: "internal" as const,
    },
    {
      id: 3,
      name: "Custom Wood Frame Sofa",
      price: 0, // price unknown for external suggestions
      image: "/images/placeholder.png",
      description: "Hand-crafted wooden frame sofa with custom upholstery",
      merchant: null, // no merchant yet for external suggestions
      source: "external" as const,
    },
  ] as ProductSuggestion[],
  quotes: [
    {
      id: 1,
      productId: 1,
      merchantName: "Luxury Furniture Co.",
      amount: 3200,
      deliveryEstimate: "3-4 weeks",
      description: "Premium leather sofa set with 5-year warranty",
      isAccepted: false,
    },
    {
      id: 2,
      productId: 2,
      merchantName: "Contemporary Home",
      amount: 2600,
      deliveryEstimate: "2-3 weeks",
      description: "Modern fabric sectional with free delivery and assembly",
      isAccepted: false,
    },
  ],
  selectedProduct: null, // Will be populated when a product is selected
  product: {
    name: "Premium Leather Sofa Set",
    price: 3500,
    image: "/images/placeholder.png",
  },
  members: [
    {
      id: 1,
      name: "Jane Smith",
      isAdmin: true,
      amountPaid: 850,
    },
    {
      id: 2,
      name: "John Doe",
      isAdmin: false,
      amountPaid: 700,
    },
    {
      id: 3,
      name: "Alice Johnson",
      isAdmin: false,
      amountPaid: 600,
    },
    {
      id: 4,
      name: "Bob Williams",
      isAdmin: false,
      amountPaid: 0,
    },
    {
      id: 5,
      name: "Carol Davis",
      isAdmin: false,
      amountPaid: 0,
    },
  ],
  amountPaid: 2150,
  totalAmount: 3500,
  expiresIn: "5 days",
  status: "manufacturing",
  manufacturingProgress: 65,
  manufacturingUpdates: [
    {
      id: 1,
      date: "2023-03-15",
      title: "Production Started",
      description: "Materials sourced and production has begun.",
      images: ["/images/placeholder.png"],
    },
    {
      id: 2,
      date: "2023-03-18",
      title: "Frame Assembly",
      description: "Wooden frames are assembled and ready for upholstery.",
      images: ["/images/placeholder.png"],
    },
    {
      id: 3,
      date: "2023-03-21",
      title: "Upholstery Progress",
      description: "Leather upholstery is being applied to the frames.",
      images: ["/images/placeholder.png"],
    },
  ],
  messages: [
    {
      id: 1,
      user: "Jane Smith",
      content: "Welcome everyone to our group buy!",
      timestamp: "2023-03-10 10:23",
    },
    {
      id: 2,
      user: "John Doe",
      content: "Thanks for organizing this!",
      timestamp: "2023-03-10 10:45",
    },
    {
      id: 3,
      user: "Alice Johnson",
      content:
        "I added a leather sofa set to our product suggestions. What do you all think?",
      timestamp: "2023-03-10 11:15",
      productRef: 1,
    },
    {
      id: 4,
      user: "Jane Smith",
      content:
        "I like it but it's a bit pricey. I found this fabric sectional that might be more budget-friendly.",
      timestamp: "2023-03-10 11:30",
      productRef: 2,
    },
    {
      id: 5,
      user: "Bob Williams",
      content:
        "I saw this custom sofa at a local craftsman's shop. Uploading a photo I took.",
      timestamp: "2023-03-10 12:15",
      productRef: 3,
      attachment: "/images/placeholder.png",
    },
  ],
};

// Helper function to get the appropriate default tab based on group stage
const getDefaultTab = (stage: string) => {
  switch (stage) {
    case "suggestion":
      return "suggestions";
    case "discussion":
      return "discussion";
    case "quote":
      return "quotes";
    case "payment":
      return "payment";
    case "manufacturing":
      return "manufacturing";
    case "shipping":
      return "shipping";
    default:
      return "discussion";
  }
};

export default function GroupDetail({ params }: { params: { id: string } }) {
  // In a real app, you would fetch the group data based on params.id
  const defaultTab = getDefaultTab(groupData.stage);
  const [selectedProductRef, setSelectedProductRef] = useState<number | null>(
    null
  );
  const [showProductSelector, setShowProductSelector] = useState(false);
  const [messageText, setMessageText] = useState("");

  const handleSendMessage = () => {
    // In a real app, this would send the message to the API
    console.log("Sending message:", {
      content: messageText,
      productRef: selectedProductRef,
    });

    // Reset the form
    setMessageText("");
    setSelectedProductRef(null);
  };

  const handleSelectProduct = (productId: number) => {
    setSelectedProductRef(productId);
    setShowProductSelector(false);
  };

  const renderStageIndicator = () => {
    const stages = [
      { id: "suggestion", label: "Suggestions" },
      { id: "discussion", label: "Discussion" },
      { id: "quote", label: "Quotes" },
      { id: "payment", label: "Payment" },
      { id: "manufacturing", label: "Manufacturing" },
      { id: "shipping", label: "Shipping" },
    ];

    const currentIndex = stages.findIndex((s) => s.id === groupData.stage);

    return (
      <div className="mb-4">
        <h3 className="text-sm font-medium mb-2">Current Stage</h3>
        <div className="w-full bg-secondary rounded-full h-2">
          <div
            className="bg-primary h-2 rounded-full transition-all duration-500 ease-in-out"
            style={{ width: `${((currentIndex + 1) / stages.length) * 100}%` }}
          ></div>
        </div>
        <div className="flex justify-between mt-1 text-xs">
          <span className="text-primary font-medium">
            {stages[currentIndex].label}
          </span>
          <span className="text-muted-foreground">
            Step {currentIndex + 1} of {stages.length}
          </span>
        </div>
      </div>
    );
  };

  return (
    <MobileLayout>
      <div className="relative bg-primary text-primary-foreground p-4 pb-6">
        <Link href="/groups" className="flex items-center mb-4">
          <ChevronLeft size={20} />
          <span className="ml-1">Back to Groups</span>
        </Link>
        <h1 className="text-fluid-xl font-bold">{groupData.name}</h1>
        <div className="flex items-center mt-2">
          <Users size={16} className="mr-2" />
          <span>{groupData.members.length} members</span>
          <span className="mx-2">•</span>
          <Clock size={16} className="mr-2" />
          <span>Expires in {groupData.expiresIn}</span>
        </div>
      </div>

      <div className="px-4 py-4">
        <Card className="-mt-6 mb-6 relative z-10">
          <CardContent className="p-4">{renderStageIndicator()}</CardContent>
        </Card>

        <Tabs defaultValue={defaultTab} className="mb-6">
          <TabsList className="w-full grid grid-cols-3 mb-2">
            <TabsTrigger value="suggestions">
              <ShoppingBag className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Suggestions</span>
            </TabsTrigger>
            <TabsTrigger value="discussion">
              <MessageSquare className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Discussion</span>
            </TabsTrigger>
            <TabsTrigger value="quotes">
              <FileText className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Quotes</span>
            </TabsTrigger>
          </TabsList>

          <TabsList className="w-full grid grid-cols-3">
            <TabsTrigger value="manufacturing">
              <TrendingUp className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Manufacturing</span>
            </TabsTrigger>
            <TabsTrigger value="members">
              <Users className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Members</span>
            </TabsTrigger>
            <TabsTrigger value="payment">
              <CreditCard className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Payment</span>
            </TabsTrigger>
          </TabsList>

          {/* Product Suggestions Tab */}
          <TabsContent value="suggestions" className="mt-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="font-medium">Product Suggestions</h3>
              <div className="flex space-x-2">
                <GroupSuggestionForm groupId={params.id} />
              </div>
            </div>

            <div className="space-y-4">
              {groupData.suggestedProducts.map((product) => (
                <Card key={product.id} className="overflow-hidden">
                  <div className="flex">
                    <div className="w-1/3 bg-muted">
                      <div className="w-full h-full relative">
                        <div className="h-24 bg-muted flex items-center justify-center rounded">
                          <span className="text-muted-foreground">
                            Product Image
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="w-2/3 p-3">
                      <div className="flex justify-between items-start">
                        <h4 className="font-medium">{product.name}</h4>
                        <span
                          className={`text-xs px-1.5 py-0.5 rounded ${
                            product.source === "internal"
                              ? "bg-primary/10 text-primary"
                              : "bg-secondary text-secondary-foreground"
                          }`}
                        >
                          {product.source === "internal"
                            ? "Catalog"
                            : "External"}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground mb-1">
                        {product.description}
                      </p>
                      <div className="flex justify-between items-center mt-1">
                        <div>
                          {product.price > 0 ? (
                            <span className="font-medium">
                              ${product.price}
                            </span>
                          ) : (
                            <span className="text-xs text-muted-foreground">
                              Price not available
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Discussion Tab */}
          <TabsContent value="discussion" className="mt-4">
            <div className="space-y-4">
              {groupData.messages.map((message) => (
                <div key={message.id} className="bg-muted rounded-lg p-3">
                  <div className="flex justify-between items-start mb-1">
                    <span className="font-medium">{message.user}</span>
                    <span className="text-xs text-muted-foreground">
                      {message.timestamp}
                    </span>
                  </div>
                  <p className="text-sm">{message.content}</p>

                  {message.productRef && (
                    <div className="mt-2 p-2 bg-background rounded border border-border flex items-center">
                      <div className="w-16 h-16 bg-muted rounded overflow-hidden flex items-center justify-center mr-3">
                        {groupData.suggestedProducts.find(
                          (p) => p.id === message.productRef
                        )?.image ? (
                          <div className="relative w-full h-full">
                            <Image
                              src={
                                groupData.suggestedProducts.find(
                                  (p) => p.id === message.productRef
                                )?.image || "/images/placeholder.png"
                              }
                              alt={
                                groupData.suggestedProducts.find(
                                  (p) => p.id === message.productRef
                                )?.name || "Product"
                              }
                              fill
                              className="object-cover"
                            />
                          </div>
                        ) : (
                          <ShoppingBag className="h-6 w-6 text-muted-foreground" />
                        )}
                      </div>
                      <div className="flex-1 text-sm">
                        <p className="font-medium">
                          {
                            groupData.suggestedProducts.find(
                              (p) => p.id === message.productRef
                            )?.name
                          }
                        </p>
                        <p className="text-muted-foreground font-medium">
                          $
                          {groupData.suggestedProducts.find(
                            (p) => p.id === message.productRef
                          )?.price || "Price unavailable"}
                        </p>
                      </div>
                    </div>
                  )}

                  {message.attachment && (
                    <div className="mt-2">
                      <div className="w-full max-w-[200px] h-[150px] bg-muted rounded-md overflow-hidden relative">
                        <div className="absolute inset-0 flex items-center justify-center text-muted-foreground">
                          Image Attachment
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>

            <div className="mt-4">
              <div className="flex flex-col gap-2">
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="rounded-full"
                    type="button"
                  >
                    <label htmlFor="file-upload" className="cursor-pointer">
                      <input
                        id="file-upload"
                        type="file"
                        accept="image/*"
                        className="sr-only"
                        aria-label="Upload image"
                      />
                      <Upload className="h-4 w-4" />
                    </label>
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="rounded-full"
                    type="button"
                    onClick={() => setShowProductSelector(true)}
                  >
                    <ShoppingBag className="h-4 w-4" />
                  </Button>

                  {selectedProductRef && (
                    <div className="flex-1">
                      <div className="bg-background rounded border border-border flex items-center p-2">
                        <div className="w-12 h-12 bg-muted rounded overflow-hidden flex items-center justify-center mr-3">
                          {groupData.suggestedProducts.find(
                            (p) => p.id === selectedProductRef
                          )?.image ? (
                            <div className="relative w-full h-full">
                              <Image
                                src={
                                  groupData.suggestedProducts.find(
                                    (p) => p.id === selectedProductRef
                                  )?.image || "/images/placeholder.png"
                                }
                                alt={
                                  groupData.suggestedProducts.find(
                                    (p) => p.id === selectedProductRef
                                  )?.name || "Product"
                                }
                                fill
                                className="object-cover"
                              />
                            </div>
                          ) : (
                            <ShoppingBag className="h-5 w-5 text-muted-foreground" />
                          )}
                        </div>
                        <div className="flex-1 text-sm">
                          <p className="font-medium">
                            {
                              groupData.suggestedProducts.find(
                                (p) => p.id === selectedProductRef
                              )?.name
                            }
                          </p>
                          <p className="text-muted-foreground">
                            $
                            {groupData.suggestedProducts.find(
                              (p) => p.id === selectedProductRef
                            )?.price || "Price unavailable"}
                          </p>
                        </div>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6 rounded-full ml-1"
                          type="button"
                          onClick={() => setSelectedProductRef(null)}
                        >
                          ×
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
                <div className="flex">
                  <input
                    type="text"
                    placeholder="Type your message..."
                    className="flex-1 h-10 rounded-l-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    value={messageText}
                    onChange={(e) => setMessageText(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === "Enter" && !e.shiftKey) {
                        e.preventDefault();
                        handleSendMessage();
                      }
                    }}
                  />
                  <Button
                    className="rounded-l-none"
                    onClick={handleSendMessage}
                  >
                    Send
                  </Button>
                </div>
              </div>
            </div>

            <Dialog
              open={showProductSelector}
              onOpenChange={setShowProductSelector}
            >
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>Select a Product</DialogTitle>
                  <DialogDescription>
                    Choose a product to reference in your message
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-3 mt-4 max-h-[300px] overflow-y-auto">
                  {groupData.suggestedProducts.map((product) => (
                    <div
                      key={product.id}
                      className="p-3 border rounded-md cursor-pointer hover:bg-muted flex items-center"
                      onClick={() => handleSelectProduct(product.id)}
                    >
                      <div className="w-16 h-16 bg-muted rounded overflow-hidden mr-3 flex items-center justify-center">
                        {product.image ? (
                          <div className="relative w-full h-full">
                            <Image
                              src={product.image || "/images/placeholder.png"}
                              alt={product.name}
                              fill
                              className="object-cover"
                            />
                          </div>
                        ) : (
                          <ShoppingBag className="h-6 w-6 text-muted-foreground" />
                        )}
                      </div>
                      <div>
                        <h4 className="font-medium">{product.name}</h4>
                        <p className="text-sm text-muted-foreground">
                          {product.price > 0
                            ? `$${product.price}`
                            : "Price unavailable"}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </DialogContent>
            </Dialog>
          </TabsContent>

          {/* Voting functionality has been removed */}

          {/* Quotes Tab */}
          <TabsContent value="quotes" className="mt-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="font-medium">Merchant Quotes</h3>
              <QuoteRequest
                groupId={params.id}
                products={groupData.suggestedProducts}
              />
            </div>
            <QuoteComparison
              groupId={params.id}
              productQuotes={[
                {
                  productName: "Premium Leather Sofa Set",
                  productId: 1,
                  quotes: groupData.quotes.filter((q) => q.productId === 1),
                },
                {
                  productName: "Modern Fabric Sectional",
                  productId: 2,
                  quotes: groupData.quotes.filter((q) => q.productId === 2),
                },
              ]}
            />
          </TabsContent>

          {/* Payment Tab */}
          <TabsContent value="payment" className="mt-4">
            <Card className="mb-6">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Payment Progress</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="w-full bg-secondary rounded-full h-3 mb-3">
                  <div
                    className="bg-primary h-3 rounded-full"
                    style={{
                      width: `${
                        (groupData.amountPaid / groupData.totalAmount) * 100
                      }%`,
                    }}
                  ></div>
                </div>
                <div className="flex justify-between text-sm mb-1">
                  <span>${groupData.amountPaid} raised</span>
                  <span>${groupData.totalAmount} goal</span>
                </div>
                <div className="text-xs text-muted-foreground text-center">
                  ${groupData.totalAmount - groupData.amountPaid} remaining
                </div>
              </CardContent>
              <CardFooter>
                <Link href={`/groups/${params.id}/payment`} className="w-full">
                  <Button className="w-full">
                    <CreditCard className="h-4 w-4 mr-2" />
                    Make a Payment
                  </Button>
                </Link>
              </CardFooter>
            </Card>

            <h3 className="font-medium mb-2">Payment History</h3>
            <div className="space-y-2">
              {groupData.members
                .filter((member) => member.amountPaid > 0)
                .map((member) => (
                  <div key={member.id} className="bg-muted p-3 rounded-lg">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <div className="w-8 h-8 rounded-full bg-secondary flex items-center justify-center">
                          {member.name.charAt(0)}
                        </div>
                        <div className="ml-2">
                          <p className="font-medium">{member.name}</p>
                          <p className="text-xs text-muted-foreground">
                            {new Date().toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      <span className="font-medium">${member.amountPaid}</span>
                    </div>
                  </div>
                ))}
            </div>
          </TabsContent>

          <TabsContent value="manufacturing" className="mt-4">
            <div className="mb-4">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-medium">Manufacturing Status</h3>
                <span className="text-sm text-primary font-medium capitalize">
                  {groupData.status}
                </span>
              </div>
              <div className="w-full bg-secondary rounded-full h-2">
                <div
                  className="bg-primary h-2 rounded-full"
                  style={{ width: `${groupData.manufacturingProgress}%` }}
                ></div>
              </div>
              <div className="flex items-center justify-between mt-1 text-xs text-muted-foreground">
                <span>Production Started</span>
                <span>Ready for Shipping</span>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="font-medium">Latest Updates</h3>
              {groupData.manufacturingUpdates.map((update) => (
                <Card key={update.id} className="overflow-hidden">
                  <div className="p-3">
                    <div className="flex justify-between items-center mb-1">
                      <h4 className="font-medium">{update.title}</h4>
                      <span className="text-xs text-muted-foreground">
                        {update.date}
                      </span>
                    </div>
                    <p className="text-sm mb-2">{update.description}</p>
                    <div className="h-24 bg-muted flex items-center justify-center rounded">
                      <span className="text-muted-foreground">
                        Update Image
                      </span>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="members" className="mt-4">
            <div className="space-y-3">
              {groupData.members.map((member) => (
                <div
                  key={member.id}
                  className="flex items-center justify-between"
                >
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-secondary flex items-center justify-center">
                      {member.name.charAt(0)}
                    </div>
                    <div className="ml-3">
                      <div className="flex items-center">
                        <p className="font-medium">{member.name}</p>
                        {member.isAdmin && (
                          <span className="ml-2 px-1.5 py-0.5 bg-primary/10 text-primary text-xs rounded">
                            Admin
                          </span>
                        )}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {member.amountPaid > 0
                          ? `Paid $${member.amountPaid}`
                          : "No payment yet"}
                      </p>
                    </div>
                  </div>
                  <div className="w-8 h-8 bg-secondary text-secondary-foreground rounded-full flex items-center justify-center">
                    {Math.round(
                      (member.amountPaid /
                        (groupData.totalAmount / groupData.members.length)) *
                        100
                    )}
                    %
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </MobileLayout>
  );
}
