"use client";

import { useState } from "react";
import { MobileLayout } from "@/components/layouts/mobile-layout";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  Users,
  MessageSquare,
  Clock,
  CreditCard,
  TrendingUp,
  ShoppingBag,
  Upload,
  MessageCircle,
  Reply,
  Smile,
  Trash2,
  Send,
  Check,
  X,
  AlertCircle,
  UserMinus,
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";
// Enhanced interfaces for Phase 2 features
export interface ProductSuggestion {
  id: number;
  name: string;
  description: string;
  price: number;
  image: string;
  source: "internal" | "external";
  merchant?: string | null;
  reactions?: ProductReaction[];
  consensusScore?: number;
  threadCount?: number;
}

export interface GroupMember {
  id: number;
  name: string;
  isAdmin: boolean;
  amountPaid: number;
  status: "active" | "inactive" | "pending" | "removed";
}

export interface ProductReaction {
  id: number;
  userId: number;
  userName: string;
  emoji: string;
  timestamp: string;
}

export interface GroupMessage {
  id: number;
  user: string;
  userId: number;
  content: string;
  timestamp: string;
  type?: "text" | "product-suggestion" | "system";
  productRef?: number;
  attachment?: string;
  parentMessageId?: number;
  threadId?: string;
  reactions?: MessageReaction[];
  detectedProducts?: DetectedProduct[];
}

export interface MessageReaction {
  id: number;
  userId: number;
  userName: string;
  emoji: string;
  timestamp: string;
}

export interface DetectedProduct {
  text: string;
  startIndex: number;
  endIndex: number;
  suggestedProductId?: number;
  confidence: number;
}
// Removed quote components - quotes functionality has been removed
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

// Mock data - would be fetched based on [id] in real app
const groupData = {
  id: 1,
  name: "Living Room Remodel Group",
  description:
    "A collective purchase for premium living room furniture at wholesale prices.",
  stage: "suggestion", // suggestion, discussion, payment, manufacturing, shipping
  suggestedProducts: [
    {
      id: 1,
      name: "Premium Leather Sofa Set",
      price: 3500,
      image: "/images/placeholder.png",
      description: "Genuine leather sofa set with matching ottoman",
      merchant: "Luxury Furniture Co.",
      source: "internal" as const,
      reactions: [
        {
          id: 1,
          userId: 1,
          userName: "Jane Smith",
          emoji: "👍",
          timestamp: "2023-03-10 11:20",
        },
        {
          id: 2,
          userId: 2,
          userName: "John Doe",
          emoji: "❤️",
          timestamp: "2023-03-10 11:25",
        },
        {
          id: 3,
          userId: 3,
          userName: "Alice Johnson",
          emoji: "👍",
          timestamp: "2023-03-10 11:30",
        },
      ],
      consensusScore: 85,
      threadCount: 3,
    },
    {
      id: 2,
      name: "Modern Fabric Sectional",
      price: 2800,
      image: "/images/placeholder.png",
      description: "L-shaped sectional with chaise lounge in premium fabric",
      merchant: "Contemporary Home",
      source: "internal" as const,
      reactions: [
        {
          id: 4,
          userId: 1,
          userName: "Jane Smith",
          emoji: "👍",
          timestamp: "2023-03-10 11:35",
        },
        {
          id: 5,
          userId: 4,
          userName: "Bob Williams",
          emoji: "🤔",
          timestamp: "2023-03-10 11:40",
        },
        {
          id: 8,
          userId: 5,
          userName: "Carol Davis",
          emoji: "👎",
          timestamp: "2023-03-10 11:45",
        },
        {
          id: 9,
          userId: 6,
          userName: "Mike Johnson",
          emoji: "💸",
          timestamp: "2023-03-10 11:50",
        },
      ],
      consensusScore: 45,
      threadCount: 2,
    },
    {
      id: 3,
      name: "Custom Wood Frame Sofa",
      price: 0,
      image: "/images/placeholder.png",
      description: "Hand-crafted wooden frame sofa with custom upholstery",
      merchant: null,
      source: "external" as const,
      reactions: [
        {
          id: 6,
          userId: 5,
          userName: "Carol Davis",
          emoji: "😍",
          timestamp: "2023-03-10 12:20",
        },
        {
          id: 7,
          userId: 2,
          userName: "John Doe",
          emoji: "👍",
          timestamp: "2023-03-10 12:25",
        },
      ],
      consensusScore: 70,
      threadCount: 1,
    },
  ] as ProductSuggestion[],
  selectedProduct: null, // Will be populated when a product is selected
  product: {
    name: "Premium Leather Sofa Set",
    price: 3500,
    image: "/images/placeholder.png",
  },
  members: [
    {
      id: 1,
      name: "Jane Smith",
      isAdmin: true,
      amountPaid: 850,
      status: "active" as const,
    },
    {
      id: 2,
      name: "John Doe",
      isAdmin: false,
      amountPaid: 700,
      status: "active" as const,
    },
    {
      id: 3,
      name: "Alice Johnson",
      isAdmin: false,
      amountPaid: 600,
      status: "active" as const,
    },
    {
      id: 4,
      name: "Bob Williams",
      isAdmin: false,
      amountPaid: 0,
      status: "pending" as const,
    },
    {
      id: 5,
      name: "Carol Davis",
      isAdmin: false,
      amountPaid: 0,
      status: "inactive" as const,
    },
  ],
  amountPaid: 2150,
  totalAmount: 3500,
  createdDate: "2023-03-05",
  isActive: true,
  status: "manufacturing",
  manufacturingProgress: 65,
  manufacturingUpdates: [
    {
      id: 1,
      date: "2023-03-15",
      title: "Production Started",
      description: "Materials sourced and production has begun.",
      images: ["/images/placeholder.png"],
    },
    {
      id: 2,
      date: "2023-03-18",
      title: "Frame Assembly",
      description: "Wooden frames are assembled and ready for upholstery.",
      images: ["/images/placeholder.png"],
    },
    {
      id: 3,
      date: "2023-03-21",
      title: "Upholstery Progress",
      description: "Leather upholstery is being applied to the frames.",
      images: ["/images/placeholder.png"],
    },
  ],
  messages: [
    {
      id: 1,
      user: "Jane Smith",
      userId: 1,
      content: "Welcome everyone to our group buy!",
      timestamp: "2023-03-10 10:23",
      type: "text" as const,
      reactions: [
        {
          id: 1,
          userId: 2,
          userName: "John Doe",
          emoji: "👋",
          timestamp: "2023-03-10 10:25",
        },
        {
          id: 2,
          userId: 3,
          userName: "Alice Johnson",
          emoji: "🎉",
          timestamp: "2023-03-10 10:26",
        },
      ],
    },
    {
      id: 2,
      user: "John Doe",
      userId: 2,
      content: "Thanks for organizing this!",
      timestamp: "2023-03-10 10:45",
      type: "text" as const,
      reactions: [
        {
          id: 3,
          userId: 1,
          userName: "Jane Smith",
          emoji: "❤️",
          timestamp: "2023-03-10 10:46",
        },
      ],
    },
    {
      id: 3,
      user: "Alice Johnson",
      userId: 3,
      content:
        "I added a Premium Leather Sofa Set to our product suggestions. What do you all think?",
      timestamp: "2023-03-10 11:15",
      type: "product-suggestion" as const,
      productRef: 1,
      detectedProducts: [
        {
          text: "Premium Leather Sofa Set",
          startIndex: 9,
          endIndex: 33,
          suggestedProductId: 1,
          confidence: 0.95,
        },
      ],
      reactions: [
        {
          id: 4,
          userId: 1,
          userName: "Jane Smith",
          emoji: "👍",
          timestamp: "2023-03-10 11:20",
        },
        {
          id: 5,
          userId: 2,
          userName: "John Doe",
          emoji: "🤔",
          timestamp: "2023-03-10 11:22",
        },
      ],
    },
    {
      id: 4,
      user: "Jane Smith",
      userId: 1,
      content:
        "I like it but it's a bit pricey. I found this fabric sectional that might be more budget-friendly.",
      timestamp: "2023-03-10 11:30",
      type: "product-suggestion" as const,
      productRef: 2,
      parentMessageId: 3,
      threadId: "product-1-discussion",
      detectedProducts: [
        {
          text: "fabric sectional",
          startIndex: 55,
          endIndex: 70,
          suggestedProductId: 2,
          confidence: 0.88,
        },
      ],
      reactions: [
        {
          id: 6,
          userId: 4,
          userName: "Bob Williams",
          emoji: "💰",
          timestamp: "2023-03-10 11:35",
        },
        {
          id: 10,
          userId: 5,
          userName: "Carol Davis",
          emoji: "👎",
          timestamp: "2023-03-10 11:37",
        },
      ],
    },
    {
      id: 5,
      user: "Bob Williams",
      userId: 4,
      content:
        "I saw this custom sofa at a local craftsman's shop. Uploading a photo I took.",
      timestamp: "2023-03-10 12:15",
      type: "product-suggestion" as const,
      productRef: 3,
      attachment: "/images/placeholder.png",
      detectedProducts: [
        {
          text: "custom sofa",
          startIndex: 12,
          endIndex: 23,
          suggestedProductId: 3,
          confidence: 0.92,
        },
      ],
      reactions: [
        {
          id: 7,
          userId: 5,
          userName: "Carol Davis",
          emoji: "📸",
          timestamp: "2023-03-10 12:20",
        },
        {
          id: 8,
          userId: 1,
          userName: "Jane Smith",
          emoji: "👀",
          timestamp: "2023-03-10 12:22",
        },
      ],
    },
    {
      id: 6,
      user: "John Doe",
      userId: 2,
      content: "The leather sofa looks amazing! How's the delivery time?",
      timestamp: "2023-03-10 12:30",
      type: "text" as const,
      parentMessageId: 3,
      threadId: "product-1-discussion",
      detectedProducts: [
        {
          text: "leather sofa",
          startIndex: 4,
          endIndex: 16,
          suggestedProductId: 1,
          confidence: 0.9,
        },
      ],
    },
    {
      id: 7,
      user: "Carol Davis",
      userId: 5,
      content:
        "I'm really interested in the custom wood frame option. Can we get more details?",
      timestamp: "2023-03-10 13:00",
      type: "text" as const,
      parentMessageId: 5,
      threadId: "product-3-discussion",
      detectedProducts: [
        {
          text: "custom wood frame",
          startIndex: 30,
          endIndex: 47,
          suggestedProductId: 3,
          confidence: 0.85,
        },
      ],
    },
  ] as GroupMessage[],
};

// Helper function to get the appropriate default tab based on group stage
const getDefaultTab = (stage: string) => {
  switch (stage) {
    case "suggestion":
      return "discussion"; // suggestions are now part of discussion
    case "discussion":
      return "discussion";
    case "payment":
      return "payment";
    case "manufacturing":
      return "manufacturing";
    case "shipping":
      return "shipping";
    default:
      return "discussion";
  }
};

export default function GroupDetail({ params }: { params: { id: string } }) {
  // In a real app, you would fetch the group data based on params.id
  const defaultTab = getDefaultTab(groupData.stage);
  const [selectedProductRef, setSelectedProductRef] = useState<number | null>(
    null
  );
  const [showProductSelector, setShowProductSelector] = useState(false);
  const [showProductsOverview, setShowProductsOverview] = useState(false);
  const [messageText, setMessageText] = useState("");

  // Phase 2 state management
  const [expandedThreads, setExpandedThreads] = useState<Set<string>>(
    new Set()
  );
  const [showEmojiPicker, setShowEmojiPicker] = useState<{
    messageId?: number;
    productId?: number;
  } | null>(null);
  const [replyingTo, setReplyingTo] = useState<number | null>(null);
  const [currentUserId] = useState(1); // Mock current user - Jane Smith

  // Local state for dynamic updates (in a real app, this would be managed by a state management system)
  const [localMessages, setLocalMessages] = useState<GroupMessage[]>(
    groupData.messages
  );
  const [localProducts, setLocalProducts] = useState<ProductSuggestion[]>(
    groupData.suggestedProducts
  );
  const [replyText, setReplyText] = useState("");
  const [activeTab, setActiveTab] = useState(defaultTab);

  // Local state for member management
  const [localMembers, setLocalMembers] = useState<GroupMember[]>(
    groupData.members
  );

  const handleSendMessage = () => {
    if (!messageText.trim()) return;

    const newMessage: GroupMessage = {
      id: Date.now(),
      user: "Jane Smith", // Current user
      userId: currentUserId,
      content: messageText,
      timestamp: new Date().toLocaleString(),
      type: selectedProductRef ? "product-suggestion" : "text",
      productRef: selectedProductRef || undefined,
      reactions: [],
    };

    setLocalMessages((prev) => [...prev, newMessage]);
    setMessageText("");
    setSelectedProductRef(null);
  };

  const handleSendReply = (parentMessageId: number) => {
    if (!replyText.trim()) return;

    const parentMessage = localMessages.find((m) => m.id === parentMessageId);

    const newReply: GroupMessage = {
      id: Date.now(),
      user: "Jane Smith",
      userId: currentUserId,
      content: replyText,
      timestamp: new Date().toLocaleString(),
      type: "text",
      parentMessageId: parentMessageId,
      threadId: parentMessage?.threadId || `thread-${parentMessageId}`,
      reactions: [],
    };

    console.log("Creating reply:", newReply);
    console.log("Current messages before:", localMessages.length);

    setLocalMessages((prev) => {
      const updated = [...prev, newReply];
      console.log("Updated messages after:", updated.length);
      return updated;
    });

    // Auto-expand the thread to show the new reply
    setExpandedThreads((prev) => {
      const newSet = new Set(prev);
      newSet.add(`thread-${parentMessageId}`);
      return newSet;
    });

    setReplyText("");
    setReplyingTo(null);
  };

  const handleSelectProduct = (productId: number) => {
    setSelectedProductRef(productId);
    setShowProductSelector(false);
  };

  // Member status management (admin only)
  const handleUpdateMemberStatus = (
    memberId: number,
    newStatus: GroupMember["status"]
  ) => {
    // Only admins can update member status
    const currentUser = localMembers.find((m) => m.id === currentUserId);
    if (!currentUser?.isAdmin) return;

    setLocalMembers((prev) =>
      prev.map((member) =>
        member.id === memberId ? { ...member, status: newStatus } : member
      )
    );
  };

  // Helper function to get status styling
  const getStatusStyling = (status: GroupMember["status"]) => {
    switch (status) {
      case "active":
        return "bg-green-500/20 text-green-700 border-green-200";
      case "inactive":
        return "bg-gray-500/20 text-gray-700 border-gray-200";
      case "pending":
        return "bg-yellow-500/20 text-yellow-700 border-yellow-200";
      case "removed":
        return "bg-red-500/20 text-red-700 border-red-200";
      default:
        return "bg-gray-500/20 text-gray-700 border-gray-200";
    }
  };

  // Phase 2 helper functions
  const handleAddReaction = (
    emoji: string,
    messageId?: number,
    productId?: number
  ) => {
    if (messageId) {
      // Check if user already has a reaction on this message
      const message = localMessages.find((m) => m.id === messageId);
      const existingReaction = message?.reactions?.find(
        (r) => r.userId === currentUserId
      );

      setLocalMessages((prev) =>
        prev.map((msg) => {
          if (msg.id === messageId) {
            let newReactions = msg.reactions || [];

            if (existingReaction) {
              // Replace existing reaction
              newReactions = newReactions.map((r) =>
                r.userId === currentUserId
                  ? { ...r, emoji, timestamp: new Date().toLocaleString() }
                  : r
              );
            } else {
              // Add new reaction
              const newReaction = {
                id: Date.now(),
                userId: currentUserId,
                userName: "Jane Smith",
                emoji,
                timestamp: new Date().toLocaleString(),
              };
              newReactions = [...newReactions, newReaction];
            }

            return { ...msg, reactions: newReactions };
          }
          return msg;
        })
      );
    }

    if (productId) {
      // Check if user already has a reaction on this product
      const product = localProducts.find((p) => p.id === productId);
      const existingReaction = product?.reactions?.find(
        (r) => r.userId === currentUserId
      );

      setLocalProducts((prev) =>
        prev.map((product) => {
          if (product.id === productId) {
            let newReactions = product.reactions || [];

            if (existingReaction) {
              // Replace existing reaction
              newReactions = newReactions.map((r) =>
                r.userId === currentUserId
                  ? { ...r, emoji, timestamp: new Date().toLocaleString() }
                  : r
              );
            } else {
              // Add new reaction
              const newReaction = {
                id: Date.now(),
                userId: currentUserId,
                userName: "Jane Smith",
                emoji,
                timestamp: new Date().toLocaleString(),
              };
              newReactions = [...newReactions, newReaction];
            }

            return {
              ...product,
              reactions: newReactions,
              consensusScore: calculateConsensusScore(newReactions),
            };
          }
          return product;
        })
      );
    }

    setShowEmojiPicker(null);
  };

  const handleDeleteMessage = (messageId: number) => {
    // Remove the message and any replies to it
    setLocalMessages((prev) =>
      prev.filter(
        (msg) => msg.id !== messageId && msg.parentMessageId !== messageId
      )
    );
  };

  const handleDeleteReaction = (
    reactionId: number,
    messageId?: number,
    productId?: number
  ) => {
    if (messageId) {
      setLocalMessages((prev) =>
        prev.map((msg) =>
          msg.id === messageId
            ? {
                ...msg,
                reactions:
                  msg.reactions?.filter((r) => r.id !== reactionId) || [],
              }
            : msg
        )
      );
    }

    if (productId) {
      setLocalProducts((prev) =>
        prev.map((product) => {
          if (product.id === productId) {
            const newReactions =
              product.reactions?.filter((r) => r.id !== reactionId) || [];
            return {
              ...product,
              reactions: newReactions,
              consensusScore: calculateConsensusScore(newReactions),
            };
          }
          return product;
        })
      );
    }
  };

  const toggleThread = (threadId: string) => {
    const newExpanded = new Set(expandedThreads);
    if (newExpanded.has(threadId)) {
      newExpanded.delete(threadId);
    } else {
      newExpanded.add(threadId);
    }
    setExpandedThreads(newExpanded);
  };

  const getThreadMessages = (parentMessageId: number) => {
    return localMessages.filter(
      (msg) => msg.parentMessageId === parentMessageId
    );
  };

  const getMainMessages = () => {
    return localMessages.filter((msg) => !msg.parentMessageId);
  };

  const getConsensusColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const calculateConsensusScore = (reactions: ProductReaction[]) => {
    if (!reactions || reactions.length === 0) return 0;

    const positiveEmojis = ["👍", "❤️", "😍", "🎉", "🔥", "💡", "✨"];
    const negativeEmojis = ["👎", "😕", "❌", "💸", "⚠️", "🚫"];
    // neutralEmojis: ["🤔", "👀", "📸", "👋", "💰"] - treated as neutral (0.5 weight)

    let positiveCount = 0;
    let negativeCount = 0;
    let neutralCount = 0;

    reactions.forEach((reaction) => {
      if (positiveEmojis.includes(reaction.emoji)) {
        positiveCount++;
      } else if (negativeEmojis.includes(reaction.emoji)) {
        negativeCount++;
      } else {
        neutralCount++;
      }
    });

    const totalReactions = reactions.length;
    const positiveWeight = positiveCount * 1.0;
    const neutralWeight = neutralCount * 0.5;
    const negativeWeight = negativeCount * 0.0;

    const weightedScore =
      (positiveWeight + neutralWeight + negativeWeight) / totalReactions;
    return Math.round(weightedScore * 100);
  };

  const renderDetectedProducts = (
    content: string,
    detectedProducts?: DetectedProduct[]
  ) => {
    if (!detectedProducts || detectedProducts.length === 0) {
      return content;
    }

    let result = content;
    let offset = 0;

    detectedProducts
      .sort((a, b) => a.startIndex - b.startIndex)
      .forEach((detected) => {
        const start = detected.startIndex + offset;
        const end = detected.endIndex + offset;
        const productName = result.substring(start, end);
        const replacement = `<span class="bg-blue-100 text-blue-800 px-1 rounded cursor-pointer hover:bg-blue-200" data-product-id="${detected.suggestedProductId}">${productName}</span>`;

        result =
          result.substring(0, start) + replacement + result.substring(end);
        offset += replacement.length - productName.length;
      });

    return result;
  };

  const renderStageIndicator = () => {
    const stages = [
      { id: "suggestion", label: "Suggestions" },
      { id: "discussion", label: "Discussion" },
      { id: "payment", label: "Payment" },
      { id: "manufacturing", label: "Manufacturing" },
      { id: "shipping", label: "Shipping" },
    ];

    const currentIndex = stages.findIndex((s) => s.id === groupData.stage);

    return (
      <div className="mb-4">
        <h3 className="text-sm font-medium mb-2">Current Stage</h3>
        <div className="w-full bg-secondary rounded-full h-2">
          <div
            className="bg-primary h-2 rounded-full transition-all duration-500 ease-in-out"
            style={{ width: `${((currentIndex + 1) / stages.length) * 100}%` }}
          ></div>
        </div>
        <div className="flex justify-between mt-1 text-xs">
          <span className="text-primary font-medium">
            {stages[currentIndex].label}
          </span>
          <span className="text-muted-foreground">
            Step {currentIndex + 1} of {stages.length}
          </span>
        </div>
      </div>
    );
  };

  return (
    <MobileLayout>
      <div className="relative bg-primary text-primary-foreground p-4 pb-6">
        <h1 className="text-fluid-xl font-bold">{groupData.name}</h1>
        <div className="flex items-center mt-2">
          <Users size={16} className="mr-2" />
          <span>{groupData.members.length} members</span>
          <span className="mx-2">•</span>
          <Clock size={16} className="mr-2" />
          <span>
            Created{" "}
            {new Date(groupData.createdDate).toLocaleDateString("en-US", {
              year: "numeric",
              month: "short",
              day: "numeric",
            })}
          </span>
          <span className="mx-2">•</span>
          <span
            className={`px-2 py-1 rounded-full text-xs ${
              groupData.isActive
                ? "bg-green-500/20 text-green-300"
                : "bg-gray-500/20 text-gray-300"
            }`}
          >
            {groupData.isActive ? "Active" : "Inactive"}
          </span>
        </div>
      </div>

      <div className="px-4 py-4 pb-36">
        <Card className="mb-6">
          <CardContent className="p-4">{renderStageIndicator()}</CardContent>
        </Card>

        <Tabs
          defaultValue={defaultTab}
          value={activeTab}
          onValueChange={setActiveTab}
          className="mb-6"
        >
          <TabsList className="w-full grid grid-cols-4 gap-1 bg-transparent p-1 h-auto">
            <TabsTrigger
              value="discussion"
              className="flex flex-col items-center gap-2 p-2 h-auto bg-transparent hover:bg-muted/50 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground rounded-lg transition-colors"
            >
              <div
                className={`w-10 h-10 rounded-full flex items-center justify-center transition-colors ${
                  activeTab === "discussion"
                    ? "bg-primary-foreground text-primary"
                    : "bg-muted text-muted-foreground"
                }`}
              >
                <MessageSquare className="h-5 w-5" />
              </div>
              <span className="text-xs font-medium">Discussion</span>
            </TabsTrigger>
            <TabsTrigger
              value="manufacturing"
              className="flex flex-col items-center gap-2 p-2 h-auto bg-transparent hover:bg-muted/50 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground rounded-lg transition-colors"
            >
              <div
                className={`w-10 h-10 rounded-full flex items-center justify-center transition-colors ${
                  activeTab === "manufacturing"
                    ? "bg-primary-foreground text-primary"
                    : "bg-muted text-muted-foreground"
                }`}
              >
                <TrendingUp className="h-5 w-5" />
              </div>
              <span className="text-xs font-medium">Manufacturing</span>
            </TabsTrigger>
            <TabsTrigger
              value="members"
              className="flex flex-col items-center gap-2 p-2 h-auto bg-transparent hover:bg-muted/50 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground rounded-lg transition-colors"
            >
              <div
                className={`w-10 h-10 rounded-full flex items-center justify-center transition-colors ${
                  activeTab === "members"
                    ? "bg-primary-foreground text-primary"
                    : "bg-muted text-muted-foreground"
                }`}
              >
                <Users className="h-5 w-5" />
              </div>
              <span className="text-xs font-medium">Members</span>
            </TabsTrigger>
            <TabsTrigger
              value="payment"
              className="flex flex-col items-center gap-2 p-2 h-auto bg-transparent hover:bg-muted/50 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground rounded-lg transition-colors"
            >
              <div
                className={`w-10 h-10 rounded-full flex items-center justify-center transition-colors ${
                  activeTab === "payment"
                    ? "bg-primary-foreground text-primary"
                    : "bg-muted text-muted-foreground"
                }`}
              >
                <CreditCard className="h-5 w-5" />
              </div>
              <span className="text-xs font-medium">Payment</span>
            </TabsTrigger>
          </TabsList>

          {/* Product suggestions are now integrated into the discussion */}

          {/* Discussion Tab */}
          <TabsContent value="discussion" className="mt-4">
            <div className="space-y-4">
              {getMainMessages().map((message) => (
                <div key={message.id} className="space-y-2">
                  {/* Main Message */}
                  <div
                    className={`rounded-lg p-3 ${
                      message.type === "product-suggestion"
                        ? "bg-blue-50 border border-blue-200"
                        : "bg-muted"
                    }`}
                  >
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{message.user}</span>
                        {message.type === "product-suggestion" && (
                          <Badge variant="secondary" className="text-xs">
                            <ShoppingBag className="h-3 w-3 mr-1" />
                            Product
                          </Badge>
                        )}
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-xs text-muted-foreground">
                          {message.timestamp}
                        </span>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6"
                          onClick={() =>
                            setShowEmojiPicker({ messageId: message.id })
                          }
                        >
                          <Smile className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6"
                          onClick={() => setReplyingTo(message.id)}
                        >
                          <Reply className="h-3 w-3" />
                        </Button>
                        {message.userId === currentUserId && (
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6 text-red-500 hover:text-red-700"
                            onClick={() => handleDeleteMessage(message.id)}
                            title="Delete message"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        )}
                      </div>
                    </div>

                    <div
                      className="text-sm mb-2"
                      dangerouslySetInnerHTML={{
                        __html: renderDetectedProducts(
                          message.content,
                          message.detectedProducts
                        ),
                      }}
                    />

                    {/* Message Reactions */}
                    {message.reactions && message.reactions.length > 0 && (
                      <div className="flex flex-wrap gap-1 mb-2">
                        {/* Group reactions by emoji */}
                        {Object.entries(
                          message.reactions.reduce((acc, reaction) => {
                            if (!acc[reaction.emoji]) {
                              acc[reaction.emoji] = [];
                            }
                            acc[reaction.emoji].push(reaction);
                            return acc;
                          }, {} as Record<string, typeof message.reactions>)
                        ).map(([emoji, reactions]) => {
                          const userReaction = reactions.find(
                            (r) => r.userId === currentUserId
                          );
                          return (
                            <Button
                              key={emoji}
                              variant="outline"
                              size="sm"
                              className={`h-6 px-2 text-xs relative group ${
                                userReaction ? "bg-blue-50 border-blue-200" : ""
                              }`}
                              title={`${reactions
                                .map((r) => r.userName)
                                .join(", ")} reacted with ${emoji}`}
                              onClick={() => {
                                if (userReaction) {
                                  handleDeleteReaction(
                                    userReaction.id,
                                    message.id
                                  );
                                }
                              }}
                            >
                              {emoji} {reactions.length}
                              {userReaction && (
                                <span className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-3 h-3 text-xs flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                                  ×
                                </span>
                              )}
                            </Button>
                          );
                        })}
                      </div>
                    )}

                    {/* Thread indicator */}
                    {getThreadMessages(message.id).length > 0 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-xs text-blue-600 hover:text-blue-800 p-0 h-auto"
                        onClick={() => toggleThread(`thread-${message.id}`)}
                      >
                        <MessageCircle className="h-3 w-3 mr-1" />
                        {getThreadMessages(message.id).length} replies
                        {expandedThreads.has(`thread-${message.id}`)
                          ? " ▼"
                          : " ▶"}
                      </Button>
                    )}

                    {/* Enhanced Product Card */}
                    {message.productRef && (
                      <div className="mt-2 p-3 bg-background rounded border border-border">
                        <div className="flex items-center mb-2">
                          <div className="w-16 h-16 bg-muted rounded overflow-hidden flex items-center justify-center mr-3">
                            {localProducts.find(
                              (p) => p.id === message.productRef
                            )?.image ? (
                              <div className="relative w-full h-full">
                                <Image
                                  src={
                                    localProducts.find(
                                      (p) => p.id === message.productRef
                                    )?.image || "/images/placeholder.png"
                                  }
                                  alt={
                                    localProducts.find(
                                      (p) => p.id === message.productRef
                                    )?.name || "Product"
                                  }
                                  fill
                                  className="object-cover"
                                />
                              </div>
                            ) : (
                              <ShoppingBag className="h-6 w-6 text-muted-foreground" />
                            )}
                          </div>
                          <div className="flex-1">
                            <div className="flex justify-between items-start mb-1">
                              <p className="font-medium text-sm">
                                {
                                  localProducts.find(
                                    (p) => p.id === message.productRef
                                  )?.name
                                }
                              </p>
                              <div className="flex items-center gap-1">
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-6 w-6"
                                  onClick={() =>
                                    setShowEmojiPicker({
                                      productId: message.productRef,
                                    })
                                  }
                                >
                                  <Smile className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                            <p className="text-muted-foreground text-sm mb-2">
                              $
                              {localProducts.find(
                                (p) => p.id === message.productRef
                              )?.price || "Price unavailable"}
                            </p>

                            {/* Product Reactions */}
                            {(() => {
                              const product = localProducts.find(
                                (p) => p.id === message.productRef
                              );
                              return (
                                product?.reactions &&
                                product.reactions.length > 0 && (
                                  <div className="flex flex-wrap gap-1 mb-2">
                                    {/* Group reactions by emoji */}
                                    {Object.entries(
                                      product.reactions.reduce(
                                        (acc, reaction) => {
                                          if (!acc[reaction.emoji]) {
                                            acc[reaction.emoji] = [];
                                          }
                                          acc[reaction.emoji].push(reaction);
                                          return acc;
                                        },
                                        {} as Record<
                                          string,
                                          typeof product.reactions
                                        >
                                      )
                                    ).map(([emoji, reactions]) => {
                                      const userReaction = reactions.find(
                                        (r) => r.userId === currentUserId
                                      );
                                      return (
                                        <Button
                                          key={emoji}
                                          variant="outline"
                                          size="sm"
                                          className={`h-6 px-2 text-xs relative group ${
                                            userReaction
                                              ? "bg-blue-50 border-blue-200"
                                              : ""
                                          }`}
                                          title={`${reactions
                                            .map((r) => r.userName)
                                            .join(", ")} reacted with ${emoji}`}
                                          onClick={() => {
                                            if (userReaction) {
                                              handleDeleteReaction(
                                                userReaction.id,
                                                undefined,
                                                product.id
                                              );
                                            }
                                          }}
                                        >
                                          {emoji} {reactions.length}
                                          {userReaction && (
                                            <span className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-3 h-3 text-xs flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                                              ×
                                            </span>
                                          )}
                                        </Button>
                                      );
                                    })}
                                  </div>
                                )
                              );
                            })()}

                            {/* Consensus Indicator */}
                            {(() => {
                              const product = localProducts.find(
                                (p) => p.id === message.productRef
                              );
                              return (
                                product?.consensusScore && (
                                  <div className="flex items-center gap-2">
                                    <div className="flex-1 bg-gray-200 rounded-full h-2">
                                      <div
                                        className={`h-2 rounded-full ${
                                          product.consensusScore >= 80
                                            ? "bg-green-500"
                                            : product.consensusScore >= 60
                                            ? "bg-yellow-500"
                                            : "bg-red-500"
                                        }`}
                                        style={{
                                          width: `${product.consensusScore}%`,
                                        }}
                                      />
                                    </div>
                                    <span
                                      className={`text-xs font-medium ${getConsensusColor(
                                        product.consensusScore
                                      )}`}
                                    >
                                      {product.consensusScore}% consensus
                                    </span>
                                  </div>
                                )
                              );
                            })()}
                          </div>
                        </div>
                      </div>
                    )}

                    {message.attachment && (
                      <div className="mt-2">
                        <div className="w-full max-w-[200px] h-[150px] bg-muted rounded-md overflow-hidden relative">
                          <div className="absolute inset-0 flex items-center justify-center text-muted-foreground">
                            Image Attachment
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Thread Display */}
                  {expandedThreads.has(`thread-${message.id}`) && (
                    <div className="ml-4 pl-4 border-l-2 border-gray-200 space-y-2">
                      {getThreadMessages(message.id).map((threadMessage) => (
                        <div
                          key={threadMessage.id}
                          className="bg-gray-50 rounded-lg p-2"
                        >
                          <div className="flex justify-between items-start mb-1">
                            <span className="font-medium text-sm">
                              {threadMessage.user}
                            </span>
                            <div className="flex items-center gap-1">
                              <span className="text-xs text-muted-foreground">
                                {threadMessage.timestamp}
                              </span>
                              {threadMessage.userId === currentUserId && (
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-4 w-4 text-red-500 hover:text-red-700"
                                  onClick={() =>
                                    handleDeleteMessage(threadMessage.id)
                                  }
                                  title="Delete reply"
                                >
                                  <Trash2 className="h-2 w-2" />
                                </Button>
                              )}
                            </div>
                          </div>
                          <div
                            className="text-sm"
                            dangerouslySetInnerHTML={{
                              __html: renderDetectedProducts(
                                threadMessage.content,
                                threadMessage.detectedProducts
                              ),
                            }}
                          />
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Reply Input */}
                  {replyingTo === message.id && (
                    <div className="mt-2 p-2 bg-gray-50 rounded border">
                      <div className="text-xs text-muted-foreground mb-2">
                        Replying to {message.user}
                      </div>
                      <div className="flex gap-2">
                        <input
                          type="text"
                          placeholder="Type your reply..."
                          className="flex-1 h-8 rounded-l border border-input bg-background px-2 text-sm"
                          value={replyText}
                          onChange={(e) => setReplyText(e.target.value)}
                          onKeyDown={(e) => {
                            if (e.key === "Enter" && !e.shiftKey) {
                              e.preventDefault();
                              handleSendReply(message.id);
                            }
                          }}
                        />
                        <Button
                          size="sm"
                          onClick={() => handleSendReply(message.id)}
                          disabled={!replyText.trim()}
                          className="rounded-l-none"
                        >
                          <Send className="h-3 w-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            setReplyingTo(null);
                            setReplyText("");
                          }}
                        >
                          Cancel
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* Message input moved to floating position */}
          </TabsContent>

          {/* Voting functionality has been removed */}

          {/* Quotes functionality has been removed */}

          {/* Payment Tab */}
          <TabsContent value="payment" className="mt-4">
            <Card className="mb-6">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Payment Progress</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="w-full bg-secondary rounded-full h-3 mb-3">
                  <div
                    className="bg-primary h-3 rounded-full"
                    style={{
                      width: `${
                        (groupData.amountPaid / groupData.totalAmount) * 100
                      }%`,
                    }}
                  ></div>
                </div>
                <div className="flex justify-between text-sm mb-1">
                  <span>${groupData.amountPaid} raised</span>
                  <span>${groupData.totalAmount} goal</span>
                </div>
                <div className="text-xs text-muted-foreground text-center">
                  ${groupData.totalAmount - groupData.amountPaid} remaining
                </div>
              </CardContent>
              <CardFooter>
                <Link href={`/groups/${params.id}/payment`} className="w-full">
                  <Button className="w-full">
                    <CreditCard className="h-4 w-4 mr-2" />
                    Make a Payment
                  </Button>
                </Link>
              </CardFooter>
            </Card>

            <h3 className="font-medium mb-2">Payment History</h3>
            <div className="space-y-2">
              {groupData.members
                .filter((member) => member.amountPaid > 0)
                .map((member) => (
                  <div key={member.id} className="bg-muted p-3 rounded-lg">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <div className="w-8 h-8 rounded-full bg-secondary flex items-center justify-center">
                          {member.name.charAt(0)}
                        </div>
                        <div className="ml-2">
                          <p className="font-medium">{member.name}</p>
                          <p className="text-xs text-muted-foreground">
                            {new Date().toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      <span className="font-medium">${member.amountPaid}</span>
                    </div>
                  </div>
                ))}
            </div>
          </TabsContent>

          <TabsContent value="manufacturing" className="mt-4">
            <div className="mb-4">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-medium">Manufacturing Status</h3>
                <span className="text-sm text-primary font-medium capitalize">
                  {groupData.status}
                </span>
              </div>
              <div className="w-full bg-secondary rounded-full h-2">
                <div
                  className="bg-primary h-2 rounded-full"
                  style={{ width: `${groupData.manufacturingProgress}%` }}
                ></div>
              </div>
              <div className="flex items-center justify-between mt-1 text-xs text-muted-foreground">
                <span>Production Started</span>
                <span>Ready for Shipping</span>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="font-medium">Latest Updates</h3>
              {groupData.manufacturingUpdates.map((update) => (
                <Card key={update.id} className="overflow-hidden">
                  <div className="p-3">
                    <div className="flex justify-between items-center mb-1">
                      <h4 className="font-medium">{update.title}</h4>
                      <span className="text-xs text-muted-foreground">
                        {update.date}
                      </span>
                    </div>
                    <p className="text-sm mb-2">{update.description}</p>
                    <div className="h-24 bg-muted flex items-center justify-center rounded">
                      <span className="text-muted-foreground">
                        Update Image
                      </span>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="members" className="mt-4">
            <div className="space-y-3">
              {localMembers.map((member) => {
                const currentUser = localMembers.find(
                  (m) => m.id === currentUserId
                );
                const isCurrentUserAdmin = currentUser?.isAdmin;

                return (
                  <div
                    key={member.id}
                    className="flex items-center p-3 bg-muted rounded-lg"
                  >
                    <div className="w-10 h-10 rounded-full bg-secondary flex items-center justify-center">
                      {member.name.charAt(0)}
                    </div>
                    <div className="ml-3 flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <p className="font-medium">{member.name}</p>
                        {member.isAdmin && (
                          <span className="px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">
                            Admin
                          </span>
                        )}
                        <span
                          className={`px-2 py-1 text-xs rounded-full border ${getStatusStyling(
                            member.status
                          )}`}
                        >
                          {member.status.charAt(0).toUpperCase() +
                            member.status.slice(1)}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {member.isAdmin ? "Group Administrator" : "Member"}
                      </p>

                      {/* Admin controls for member status */}
                      {isCurrentUserAdmin && !member.isAdmin && (
                        <div className="flex gap-1 mt-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-6 px-2 text-xs"
                            onClick={() =>
                              handleUpdateMemberStatus(member.id, "active")
                            }
                            disabled={member.status === "active"}
                          >
                            <Check className="h-3 w-3 mr-1" />
                            Active
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-6 px-2 text-xs"
                            onClick={() =>
                              handleUpdateMemberStatus(member.id, "inactive")
                            }
                            disabled={member.status === "inactive"}
                          >
                            <X className="h-3 w-3 mr-1" />
                            Inactive
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-6 px-2 text-xs"
                            onClick={() =>
                              handleUpdateMemberStatus(member.id, "pending")
                            }
                            disabled={member.status === "pending"}
                          >
                            <AlertCircle className="h-3 w-3 mr-1" />
                            Pending
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-6 px-2 text-xs text-red-600 hover:text-red-700"
                            onClick={() =>
                              handleUpdateMemberStatus(member.id, "removed")
                            }
                            disabled={member.status === "removed"}
                          >
                            <UserMinus className="h-3 w-3 mr-1" />
                            Remove
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Product Selector Dialog */}
      <Dialog open={showProductSelector} onOpenChange={setShowProductSelector}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Select a Product</DialogTitle>
            <DialogDescription>
              Choose a product to reference in your message
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-3 mt-4 max-h-[300px] overflow-y-auto">
            {groupData.suggestedProducts.map((product) => (
              <div
                key={product.id}
                className="p-3 border rounded-md cursor-pointer hover:bg-muted flex items-center"
                onClick={() => handleSelectProduct(product.id)}
              >
                <div className="w-16 h-16 bg-muted rounded overflow-hidden mr-3 flex items-center justify-center">
                  {product.image ? (
                    <div className="relative w-full h-full">
                      <Image
                        src={product.image || "/images/placeholder.png"}
                        alt={product.name}
                        fill
                        className="object-cover"
                      />
                    </div>
                  ) : (
                    <ShoppingBag className="h-6 w-6 text-muted-foreground" />
                  )}
                </div>
                <div>
                  <h4 className="font-medium">{product.name}</h4>
                  <p className="text-sm text-muted-foreground">
                    {product.price > 0
                      ? `$${product.price}`
                      : "Price unavailable"}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </DialogContent>
      </Dialog>

      {/* Floating Message Input - Only show on discussion tab */}
      {activeTab === "discussion" && (
        <div className="fixed bottom-20 left-4 right-4 z-50">
          <div className="max-w-4xl mx-auto">
            {/* Selected Product Preview */}
            {selectedProductRef && (
              <div className="mb-2 bg-white rounded-lg border border-border shadow-lg p-2">
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-muted rounded overflow-hidden flex items-center justify-center mr-2">
                    {localProducts.find((p) => p.id === selectedProductRef)
                      ?.image ? (
                      <div className="relative w-full h-full">
                        <Image
                          src={
                            localProducts.find(
                              (p) => p.id === selectedProductRef
                            )?.image || "/images/placeholder.png"
                          }
                          alt={
                            localProducts.find(
                              (p) => p.id === selectedProductRef
                            )?.name || "Product"
                          }
                          fill
                          className="object-cover"
                        />
                      </div>
                    ) : (
                      <ShoppingBag className="h-4 w-4 text-muted-foreground" />
                    )}
                  </div>
                  <div className="flex-1 text-xs">
                    <p className="font-medium">
                      {
                        localProducts.find((p) => p.id === selectedProductRef)
                          ?.name
                      }
                    </p>
                    <p className="text-muted-foreground">
                      $
                      {localProducts.find((p) => p.id === selectedProductRef)
                        ?.price || "Price unavailable"}
                    </p>
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6 rounded-full"
                    type="button"
                    onClick={() => setSelectedProductRef(null)}
                  >
                    ×
                  </Button>
                </div>
              </div>
            )}

            {/* Floating Input Bar */}
            <div className="flex items-center gap-2">
              {/* Action Buttons */}
              <div className="flex gap-1">
                <Button
                  variant="ghost"
                  size="icon"
                  className="rounded-full h-10 w-10 bg-white shadow-lg border"
                  type="button"
                >
                  <label htmlFor="file-upload" className="cursor-pointer">
                    <input
                      id="file-upload"
                      type="file"
                      accept="image/*"
                      className="sr-only"
                      aria-label="Upload image"
                    />
                    <Upload className="h-4 w-4" />
                  </label>
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="rounded-full h-10 w-10 bg-white shadow-lg border"
                  type="button"
                  onClick={() => setShowProductSelector(true)}
                >
                  <ShoppingBag className="h-4 w-4" />
                </Button>
              </div>

              {/* Message Input with Send Button Inside */}
              <div className="flex-1 relative">
                <input
                  type="text"
                  placeholder="Type your message..."
                  className="w-full h-10 rounded-full border border-input bg-white px-4 pr-12 text-sm shadow-lg ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                  value={messageText}
                  onChange={(e) => setMessageText(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" && !e.shiftKey) {
                      e.preventDefault();
                      handleSendMessage();
                    }
                  }}
                />
                <Button
                  size="icon"
                  className="absolute right-1 top-1 h-8 w-8 rounded-full"
                  onClick={handleSendMessage}
                  disabled={!messageText.trim()}
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>

              {/* Products Button */}
              <Button
                onClick={() => setShowProductsOverview(true)}
                className="rounded-full shadow-lg h-10 px-4 bg-white border text-foreground hover:bg-accent"
                variant="outline"
                size="sm"
              >
                <ShoppingBag className="h-4 w-4 mr-2" />
                {localProducts.length}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Products Overview Modal */}
      <Dialog
        open={showProductsOverview}
        onOpenChange={setShowProductsOverview}
      >
        <DialogContent className="sm:max-w-[500px] max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Product Suggestions</DialogTitle>
            <DialogDescription>
              {localProducts.length} products suggested by the group
            </DialogDescription>
          </DialogHeader>
          <div className="grid grid-cols-1 gap-4 mt-4">
            {localProducts.map((product) => (
              <Card key={product.id} className="overflow-hidden">
                <div className="flex">
                  <div className="w-20 h-20 bg-muted flex items-center justify-center">
                    {product.image ? (
                      <div className="relative w-full h-full">
                        <Image
                          src={product.image || "/images/placeholder.png"}
                          alt={product.name}
                          fill
                          className="object-cover"
                        />
                      </div>
                    ) : (
                      <ShoppingBag className="h-6 w-6 text-muted-foreground" />
                    )}
                  </div>
                  <div className="flex-1 p-3">
                    <div className="flex justify-between items-start mb-1">
                      <h4 className="font-medium text-sm">{product.name}</h4>
                      <div className="flex items-center gap-1">
                        <span
                          className={`text-xs px-1.5 py-0.5 rounded ${
                            product.source === "internal"
                              ? "bg-primary/10 text-primary"
                              : "bg-secondary text-secondary-foreground"
                          }`}
                        >
                          {product.source === "internal"
                            ? "Catalog"
                            : "External"}
                        </span>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6"
                          onClick={() =>
                            setShowEmojiPicker({ productId: product.id })
                          }
                        >
                          <Smile className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    <p className="text-xs text-muted-foreground mb-2">
                      {product.description}
                    </p>

                    {/* Product Reactions */}
                    {product.reactions && product.reactions.length > 0 && (
                      <div className="flex flex-wrap gap-1 mb-2">
                        {/* Group reactions by emoji */}
                        {Object.entries(
                          product.reactions.reduce((acc, reaction) => {
                            if (!acc[reaction.emoji]) {
                              acc[reaction.emoji] = [];
                            }
                            acc[reaction.emoji].push(reaction);
                            return acc;
                          }, {} as Record<string, typeof product.reactions>)
                        ).map(([emoji, reactions]) => {
                          const userReaction = reactions.find(
                            (r) => r.userId === currentUserId
                          );
                          return (
                            <Button
                              key={emoji}
                              variant="outline"
                              size="sm"
                              className={`h-5 px-1.5 text-xs relative group ${
                                userReaction ? "bg-blue-50 border-blue-200" : ""
                              }`}
                              title={`${reactions
                                .map((r) => r.userName)
                                .join(", ")} reacted with ${emoji}`}
                              onClick={() => {
                                if (userReaction) {
                                  handleDeleteReaction(
                                    userReaction.id,
                                    undefined,
                                    product.id
                                  );
                                }
                              }}
                            >
                              {emoji} {reactions.length}
                              {userReaction && (
                                <span className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-2 h-2 text-xs flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                                  ×
                                </span>
                              )}
                            </Button>
                          );
                        })}
                      </div>
                    )}

                    {/* Consensus Indicator */}
                    {product.consensusScore && (
                      <div className="mb-2">
                        <div className="flex items-center gap-2 mb-1">
                          <div className="flex-1 bg-gray-200 rounded-full h-1.5">
                            <div
                              className={`h-1.5 rounded-full ${
                                product.consensusScore >= 80
                                  ? "bg-green-500"
                                  : product.consensusScore >= 60
                                  ? "bg-yellow-500"
                                  : "bg-red-500"
                              }`}
                              style={{ width: `${product.consensusScore}%` }}
                            />
                          </div>
                          <span
                            className={`text-xs font-medium ${getConsensusColor(
                              product.consensusScore
                            )}`}
                          >
                            {product.consensusScore}%
                          </span>
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Group consensus • {product.threadCount || 0}{" "}
                          discussions
                        </div>
                      </div>
                    )}

                    <div className="flex justify-between items-center">
                      <span className="font-medium text-sm">
                        {product.price > 0
                          ? `$${product.price}`
                          : "Price unavailable"}
                      </span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setShowProductsOverview(false);
                          // In a real app, this would scroll to the product in discussion
                        }}
                      >
                        <MessageCircle className="h-3 w-3 mr-1" />
                        Discuss
                      </Button>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </DialogContent>
      </Dialog>

      {/* Emoji Picker Dialog */}
      <Dialog
        open={!!showEmojiPicker}
        onOpenChange={() => setShowEmojiPicker(null)}
      >
        <DialogContent className="sm:max-w-[300px]">
          <DialogHeader>
            <DialogTitle>Add Reaction</DialogTitle>
            <DialogDescription>Choose an emoji to react with</DialogDescription>
          </DialogHeader>
          <div className="grid grid-cols-6 gap-2 mt-4">
            {[
              "👍",
              "👎",
              "❤️",
              "😍",
              "🤔",
              "😕",
              "💰",
              "💸",
              "🎉",
              "❌",
              "👀",
              "📸",
              "👋",
              "🔥",
              "💡",
              "✨",
              "⚠️",
              "🚫",
            ].map((emoji) => (
              <Button
                key={emoji}
                variant="ghost"
                className="h-12 w-12 text-2xl hover:bg-accent"
                onClick={() =>
                  handleAddReaction(
                    emoji,
                    showEmojiPicker?.messageId,
                    showEmojiPicker?.productId
                  )
                }
              >
                {emoji}
              </Button>
            ))}
          </div>
        </DialogContent>
      </Dialog>
    </MobileLayout>
  );
}
