// Adapted from shadcn/ui toast component
// https://ui.shadcn.com/docs/components/toast
"use client";

import { useState, useEffect, createContext, useContext } from "react";
import React from "react";

type ToastType = "default" | "destructive" | "success";

export interface Toast {
  id: string;
  title?: string;
  description?: string;
  type?: ToastType;
  duration?: number;
}

interface ToastContextType {
  toasts: Toast[];
  addToast: (toast: Omit<Toast, "id">) => void;
  dismissToast: (id: string) => void;
}

const ToastContext = createContext<ToastContextType | null>(null);

export function ToastProvider({
  children,
}: {
  children: React.ReactNode;
}): React.ReactElement {
  const [toasts, setToasts] = useState<Toast[]>([]);

  const addToast = (toast: Omit<Toast, "id">) => {
    const id = Math.random().toString(36).substring(2, 9);
    const newToast = { id, ...toast };
    setToasts((prev) => [...prev, newToast]);

    if (toast.duration !== Infinity) {
      setTimeout(() => {
        dismissToast(id);
      }, toast.duration || 5000);
    }
  };

  const dismissToast = (id: string) => {
    setToasts((prev) => prev.filter((toast) => toast.id !== id));
  };

  // Create a simpler UI implementation just for now
  useEffect(() => {
    toasts.forEach((toast) => {
      // Simple browser alert as a placeholder
      // In a real implementation, this would render a proper UI toast
      if (toast.title && !document.getElementById(`toast-${toast.id}`)) {
        const message = toast.description
          ? `${toast.title}\n${toast.description}`
          : toast.title;

        // For now, we'll just console.log instead of showing an alert
        console.log(`TOAST: ${message}`);
      }
    });
  }, [toasts]);

  return (
    <ToastContext.Provider value={{ toasts, addToast, dismissToast }}>
      {children}
    </ToastContext.Provider>
  );
}

export const useToast = () => {
  const context = useContext(ToastContext);

  if (!context) {
    throw new Error("useToast must be used within a ToastProvider");
  }

  const toast = (props: Omit<Toast, "id">) => {
    context.addToast(props);
  };

  return {
    toast,
    dismiss: context.dismissToast,
    toasts: context.toasts,
  };
};
