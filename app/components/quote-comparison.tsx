"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Clock, FileText, Check, X, ShoppingBag } from "lucide-react";
import { useState } from "react";
import { useToast } from "@/app/components/ui/toast";

export interface Quote {
  id: number;
  productId: number;
  merchantName: string;
  amount: number;
  deliveryEstimate: string;
  description: string;
  isAccepted: boolean;
}

interface ProductQuote {
  productName: string;
  productId: number;
  quotes: Quote[];
}

interface QuoteComparisonProps {
  groupId: string;
  productQuotes: ProductQuote[];
}

export function QuoteComparison({
  groupId,
  productQuotes,
}: QuoteComparisonProps) {
  const [acceptedQuotes, setAcceptedQuotes] = useState<Record<number, number>>(
    {}
  );
  const { toast } = useToast();

  const handleAcceptQuote = (productId: number, quoteId: number) => {
    // In a real app, this would submit to an API
    setAcceptedQuotes({
      ...acceptedQuotes,
      [productId]: quoteId,
    });

    toast({
      title: "Quote accepted",
      description: "You've accepted this quote for the group purchase.",
    });
  };

  const handleRequestNewQuote = () => {
    // In a real app, this would open a form to request a quote
    toast({
      title: "Request quote",
      description:
        "Feature coming soon: Request quotes from additional merchants.",
    });
  };

  return (
    <div className="space-y-6">
      {productQuotes.map((productQuote) => (
        <div key={productQuote.productId} className="space-y-4">
          <div className="flex items-center mb-2">
            <ShoppingBag className="h-5 w-5 mr-2" />
            <h3 className="font-medium">{productQuote.productName}</h3>
          </div>

          <div className="space-y-4">
            {productQuote.quotes.map((quote) => (
              <Card key={quote.id} className="overflow-hidden">
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-center">
                    <CardTitle className="text-base">
                      {quote.merchantName}
                    </CardTitle>
                    <span className="text-lg font-bold">${quote.amount}</span>
                  </div>
                </CardHeader>
                <CardContent className="pb-2">
                  <p className="text-sm text-muted-foreground mb-2">
                    {quote.description}
                  </p>
                  <div className="flex items-center text-xs text-muted-foreground">
                    <Clock className="h-3 w-3 mr-1" />
                    <span>Delivery estimate: {quote.deliveryEstimate}</span>
                  </div>
                </CardContent>
                <CardFooter>
                  {acceptedQuotes[productQuote.productId] === quote.id ||
                  quote.isAccepted ? (
                    <Button className="w-full" variant="secondary">
                      <Check className="h-4 w-4 mr-2" />
                      Selected Quote
                    </Button>
                  ) : (
                    <Button
                      className="w-full"
                      onClick={() =>
                        handleAcceptQuote(productQuote.productId, quote.id)
                      }
                      disabled={!!acceptedQuotes[productQuote.productId]}
                    >
                      Accept Quote
                    </Button>
                  )}
                </CardFooter>
              </Card>
            ))}
          </div>

          <div className="flex justify-end">
            <Button variant="outline" size="sm" onClick={handleRequestNewQuote}>
              <FileText className="h-4 w-4 mr-2" />
              Request New Quote
            </Button>
          </div>
        </div>
      ))}

      {productQuotes.length === 0 && (
        <Card>
          <CardContent className="py-8 flex flex-col items-center justify-center">
            <FileText className="h-10 w-10 mb-4 text-muted-foreground" />
            <p className="text-center text-muted-foreground">
              No quotes available yet. As your group selects products, merchants
              will be able to provide quotes.
            </p>
            <Button variant="outline" className="mt-4">
              Request a Quote
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
