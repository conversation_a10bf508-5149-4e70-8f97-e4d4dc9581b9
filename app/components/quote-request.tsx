"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON>alogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/app/components/ui/toast";
import { zodResolver } from "@hookform/resolvers/zod";
import { FileText, ShoppingBag } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import * as z from "zod";
// ProductSuggestion interface is now defined in the group detail page
// We'll define it locally here to avoid circular dependencies
interface ProductSuggestion {
  id: number;
  name: string;
  description: string;
  price: number;
  image: string;
  source: "internal" | "external";
  merchant?: string | null;
}

const formSchema = z.object({
  productId: z.string({
    required_error: "Please select a product",
  }),
  merchantIds: z.array(z.string()).min(1, {
    message: "Please select at least one merchant",
  }),
  deliveryTimeline: z.string().min(1, {
    message: "Please provide a preferred delivery timeline",
  }),
  budget: z.string().optional(),
  additionalInfo: z.string().optional(),
});

type QuoteRequestValues = z.infer<typeof formSchema>;

// Mock data for merchants - would come from API in real app
const merchants = [
  { id: "1", name: "Luxury Furniture Co." },
  { id: "2", name: "Contemporary Home" },
  { id: "3", name: "Artisan Woodworks" },
  { id: "4", name: "Designer Furnishing" },
];

interface QuoteRequestProps {
  groupId: string;
  products: ProductSuggestion[];
  onRequestSubmitted?: () => void;
}

export function QuoteRequest({
  groupId,
  products,
  onRequestSubmitted,
}: QuoteRequestProps) {
  const [open, setOpen] = useState(false);
  const { toast } = useToast();
  const [selectedMerchants, setSelectedMerchants] = useState<string[]>([]);

  const form = useForm<QuoteRequestValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      productId: "",
      merchantIds: [],
      deliveryTimeline: "",
      budget: "",
      additionalInfo: "",
    },
  });

  const toggleMerchant = (merchantId: string) => {
    const isSelected = selectedMerchants.includes(merchantId);
    let updated: string[];

    if (isSelected) {
      updated = selectedMerchants.filter((id) => id !== merchantId);
    } else {
      updated = [...selectedMerchants, merchantId];
    }

    setSelectedMerchants(updated);
    form.setValue("merchantIds", updated);
  };

  function onSubmit(values: QuoteRequestValues) {
    // In a real app, this would submit to an API
    console.log(values);

    toast({
      title: "Quote Request Sent",
      description: "Merchants will be notified of your request.",
    });

    setOpen(false);
    form.reset();
    if (onRequestSubmitted) {
      onRequestSubmitted();
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <FileText className="h-4 w-4 mr-2" />
          Request Quote
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Request Quote</DialogTitle>
          <DialogDescription>
            Get quotes from merchants for your preferred product.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="productId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Product</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a product" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {products.map((product) => (
                        <SelectItem
                          key={product.id}
                          value={product.id.toString()}
                        >
                          {product.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Choose which product you want quotes for
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="merchantIds"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Merchants to Request From</FormLabel>
                  <FormDescription>
                    Select one or more merchants to request quotes from
                  </FormDescription>
                  <div className="grid grid-cols-2 gap-2 mt-2">
                    {merchants.map((merchant) => (
                      <Button
                        key={merchant.id}
                        type="button"
                        variant={
                          selectedMerchants.includes(merchant.id)
                            ? "default"
                            : "outline"
                        }
                        className="justify-start"
                        onClick={() => toggleMerchant(merchant.id)}
                      >
                        <ShoppingBag className="h-4 w-4 mr-2" />
                        {merchant.name}
                      </Button>
                    ))}
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="deliveryTimeline"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Preferred Delivery Timeline</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select timeline" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="asap">As soon as possible</SelectItem>
                      <SelectItem value="2-4weeks">2-4 weeks</SelectItem>
                      <SelectItem value="1-2months">1-2 months</SelectItem>
                      <SelectItem value="3-6months">3-6 months</SelectItem>
                      <SelectItem value="flexible">Flexible</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="budget"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Budget Range (optional)</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., $3000-$3500" {...field} />
                  </FormControl>
                  <FormDescription>
                    Providing a budget helps merchants prepare appropriate
                    quotes
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="additionalInfo"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Additional Information (optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Any specific requirements or questions for the merchants"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button type="submit">Submit Request</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
