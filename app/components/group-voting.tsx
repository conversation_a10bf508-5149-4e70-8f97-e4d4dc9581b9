"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ListChecks } from "lucide-react";
import { useState } from "react";
import { useToast } from "@/app/components/ui/toast";

export interface ProductSuggestion {
  id: number;
  name: string;
  description: string;
  price: number;
  image: string;
  votes: number;
  source: "internal" | "external";
  merchant?: string | null;
}

export interface GroupMember {
  id: number;
  name: string;
  isAdmin: boolean;
  amountPaid: number;
  hasVoted: boolean;
}

interface GroupVotingProps {
  groupId: string;
  products: ProductSuggestion[];
  members: GroupMember[];
  currentUserHasVoted: boolean;
  expiresIn: string;
}

export function GroupVoting({
  groupId,
  products,
  members,
  currentUserHasVoted,
  expiresIn,
}: GroupVotingProps) {
  const [votedProductId, setVotedProductId] = useState<number | null>(null);
  const { toast } = useToast();

  const handleVote = (productId: number) => {
    // In a real app, this would submit to an API
    setVotedProductId(productId);

    toast({
      title: "Vote submitted",
      description: "Your vote has been recorded.",
    });
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="font-medium mb-2">Cast Your Vote</h3>
        <p className="text-sm text-muted-foreground mb-4">
          Select your preferred product from the suggestions. Current voting
          ends in {expiresIn}.
        </p>

        <div className="space-y-4">
          {products.map((product) => (
            <Card key={product.id} className="overflow-hidden">
              <div className="p-3">
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-medium">{product.name}</h4>
                  <Badge
                    variant="outline"
                    className="bg-primary/10 text-primary"
                  >
                    {product.votes} votes
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground mb-3">
                  {product.description}
                </p>

                <div className="flex items-center justify-between">
                  <span className="font-medium">
                    {product.price > 0
                      ? `$${product.price}`
                      : "Price unavailable"}
                  </span>
                  <Button
                    variant={
                      votedProductId === product.id ? "secondary" : "outline"
                    }
                    size="sm"
                    onClick={() => handleVote(product.id)}
                    disabled={
                      currentUserHasVoted && votedProductId !== product.id
                    }
                  >
                    <ListChecks className="h-4 w-4 mr-2" />
                    {votedProductId === product.id ? "Voted" : "Vote"}
                  </Button>
                </div>

                <div className="mt-3 w-full bg-secondary rounded-full h-2">
                  <div
                    className="bg-primary h-2 rounded-full transition-all duration-500 ease-in-out"
                    style={{
                      width: `${(product.votes / members.length) * 100}%`,
                    }}
                  ></div>
                </div>
                <div className="flex justify-between mt-1 text-xs text-muted-foreground">
                  <span>{product.votes} votes</span>
                  <span>{members.length} members</span>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Voting Status</CardTitle>
          <CardDescription>
            {members.filter((m) => m.hasVoted).length} of {members.length}{" "}
            members have voted
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {members.map((member) => (
              <div
                key={member.id}
                className="flex items-center justify-between"
              >
                <div className="flex items-center">
                  <div className="w-8 h-8 rounded-full bg-secondary flex items-center justify-center">
                    {member.name.charAt(0)}
                  </div>
                  <span className="ml-2 font-medium">{member.name}</span>
                </div>
                <Badge
                  variant={member.hasVoted ? "default" : "outline"}
                  className={
                    member.hasVoted
                      ? "bg-green-100 text-green-800 hover:bg-green-100"
                      : ""
                  }
                >
                  {member.hasVoted ? "Voted" : "Not voted"}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
