"use client";

import { useState } from "react";
import { notFound } from "next/navigation";
import Link from "next/link";
import { products } from "@/data/products";
import { MobileLayout } from "@/components/layouts/mobile-layout";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import {
  StarIcon,
  Clock,
  Truck,
  Users,
  ChevronLeft,
  Plus,
  Minus,
  Heart,
  Share,
} from "lucide-react";
import Image from "next/image";
import { formatPrice } from "@/lib/utils";

interface ProductDetailPageProps {
  params: {
    id: string;
  };
}

export default function ProductDetailPage({ params }: ProductDetailPageProps) {
  const product = products.find((p) => p.id === params.id);

  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [activeTab, setActiveTab] = useState("description");

  if (!product) {
    return notFound();
  }

  const incrementQuantity = () => {
    setQuantity((prev) => prev + 1);
  };

  const decrementQuantity = () => {
    setQuantity((prev) => (prev > 1 ? prev - 1 : 1));
  };

  // For demo purposes, use a placeholder image when the product image isn't available
  const imageSrc =
    product.images.length > 0
      ? product.images[currentImageIndex]
      : "https://placehold.co/600x600";

  return (
    <MobileLayout showBackButton>
      <div className="pb-24">
        {/* Product Images */}
        <div className="relative aspect-square overflow-hidden mb-6">
          <Image
            src={product.images[currentImageIndex] || "/images/placeholder.png"}
            alt={product.name}
            fill
            priority
            className="object-cover"
          />

          {/* Image carousel navigation dots */}
          {product.images.length > 1 && (
            <div className="absolute bottom-4 left-0 right-0 flex justify-center gap-1.5">
              {product.images.map((_, index) => (
                <button
                  key={index}
                  className={`h-2 w-2 rounded-full ${
                    index === currentImageIndex
                      ? "bg-primary"
                      : "bg-background/50"
                  }`}
                  onClick={() => setCurrentImageIndex(index)}
                  aria-label={`View image ${index + 1}`}
                />
              ))}
            </div>
          )}

          {/* Manufacturing badge */}
          {product.manufacturingTime > 0 && (
            <div className="absolute top-4 left-4 bg-primary text-primary-foreground px-3 py-1.5 rounded-md text-sm font-medium">
              Custom Made
            </div>
          )}

          <div className="absolute right-4 top-4 flex gap-2">
            <Button
              variant="secondary"
              size="icon"
              className="h-10 w-10 rounded-full bg-background/80 backdrop-blur-sm"
              aria-label="Share product"
            >
              <Share className="h-5 w-5" />
            </Button>
            <Button
              variant="secondary"
              size="icon"
              className="h-10 w-10 rounded-full bg-background/80 backdrop-blur-sm"
              aria-label="Add to wishlist"
            >
              <Heart className="h-5 w-5" />
            </Button>
          </div>
        </div>

        <div className="px-4">
          {/* Product Name & Price */}
          <h1 className="text-2xl font-bold">{product.name}</h1>
          <div className="flex items-center justify-between mt-2">
            <span className="text-xl font-semibold">
              {formatPrice(product.price)}
            </span>
            <div className="flex items-center">
              <StarIcon className="h-5 w-5 fill-primary text-primary" />
              <span className="text-sm font-medium ml-1">{product.rating}</span>
              <span className="text-xs text-muted-foreground ml-1">
                ({product.reviews} reviews)
              </span>
            </div>
          </div>

          {/* Merchant */}
          <div className="text-sm text-muted-foreground mt-1">
            By <span className="font-medium">{product.merchant.name}</span>
          </div>

          {/* Manufacturing Time */}
          {product.manufacturingTime > 0 && (
            <div className="flex items-center text-sm mt-3 text-muted-foreground">
              <Clock className="h-4 w-4 mr-1" />
              <span>Est. Manufacturing: {product.manufacturingTime} days</span>
            </div>
          )}

          {/* Separator */}
          <Separator className="my-4" />

          {/* Quantity */}
          <div className="mb-6">
            <label className="text-sm font-medium mb-2 block">Quantity</label>
            <div className="flex items-center">
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8 rounded-r-none"
                onClick={decrementQuantity}
                aria-label="Decrease quantity"
                disabled={quantity <= 1}
              >
                <Minus className="h-3 w-3" />
              </Button>
              <div className="h-8 px-3 flex items-center justify-center border-y border-input">
                {quantity}
              </div>
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8 rounded-l-none"
                onClick={incrementQuantity}
                aria-label="Increase quantity"
              >
                <Plus className="h-3 w-3" />
              </Button>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="grid grid-cols-2 gap-3 my-6">
            <Link
              href={`/groups/new?productId=${product.id}&quantity=${quantity}`}
            >
              <Button className="w-full" size="lg">
                <Users className="mr-2 h-4 w-4" />
                Create Group
              </Button>
            </Link>
            <Link href={`/groups?productId=${product.id}`}>
              <Button variant="outline" className="w-full" size="lg">
                Join Existing Group
              </Button>
            </Link>
          </div>

          {/* Product Information Tabs */}
          <Tabs defaultValue="description" className="w-full mt-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="description">Description</TabsTrigger>
              <TabsTrigger value="specifications">Specifications</TabsTrigger>
              <TabsTrigger value="shipping">Shipping</TabsTrigger>
            </TabsList>
            <TabsContent value="description" className="py-4">
              <p className="text-sm text-muted-foreground leading-relaxed">
                {product.description}
              </p>
              <p className="text-sm text-muted-foreground leading-relaxed mt-3">
                This is a custom-manufactured product created specifically for
                your group purchase. Manufacturing will begin once the group
                commitment is complete.
              </p>
            </TabsContent>
            <TabsContent value="specifications" className="py-4">
              <div className="space-y-3">
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div className="text-muted-foreground">Category</div>
                  <div>{product.category}</div>
                </div>
                <Separator />
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div className="text-muted-foreground">Manufacturing</div>
                  <div>
                    {product.manufacturingTime > 0
                      ? "Custom Made"
                      : "Ready Stock"}
                  </div>
                </div>
                <Separator />
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div className="text-muted-foreground">Lead Time</div>
                  <div>{product.manufacturingTime} days</div>
                </div>
                <Separator />
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div className="text-muted-foreground">Merchant</div>
                  <div>{product.merchant.name}</div>
                </div>
              </div>
            </TabsContent>
            <TabsContent value="shipping" className="py-4">
              <div className="space-y-4 text-sm text-muted-foreground">
                <div className="flex items-start">
                  <Truck className="h-5 w-5 mr-2 mt-0.5" />
                  <div>
                    <p className="text-foreground font-medium">
                      Shipping Information
                    </p>
                    <p className="mt-1">
                      Shipping estimates will be calculated based on the
                      manufacturing completion date. The product will be shipped
                      to all group members once manufacturing is complete.
                    </p>
                  </div>
                </div>

                <div className="bg-muted p-3 rounded-md mt-4">
                  <p className="font-medium text-foreground mb-1">
                    Estimated Timeline
                  </p>
                  <ul className="space-y-1 list-disc list-inside">
                    <li>Group Formation: 1-14 days</li>
                    <li>Manufacturing: {product.manufacturingTime} days</li>
                    <li>Shipping: 3-7 days (domestic)</li>
                  </ul>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </MobileLayout>
  );
}
