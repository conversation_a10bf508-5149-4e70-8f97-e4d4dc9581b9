/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/groups/[id]/page";
exports.ids = ["app/groups/[id]/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fgroups%2F%5Bid%5D%2Fpage&page=%2Fgroups%2F%5Bid%5D%2Fpage&appPaths=%2Fgroups%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fgroups%2F%5Bid%5D%2Fpage.tsx&appDir=%2FUsers%2Fhlogi%2FCode%2Fkhenesis%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fhlogi%2FCode%2Fkhenesis&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fgroups%2F%5Bid%5D%2Fpage&page=%2Fgroups%2F%5Bid%5D%2Fpage&appPaths=%2Fgroups%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fgroups%2F%5Bid%5D%2Fpage.tsx&appDir=%2FUsers%2Fhlogi%2FCode%2Fkhenesis%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fhlogi%2FCode%2Fkhenesis&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'groups',\n        {\n        children: [\n        '[id]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/groups/[id]/page.tsx */ \"(rsc)/./app/groups/[id]/page.tsx\")), \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Users/<USER>/Code/khenesis/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/groups/[id]/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/groups/[id]/page\",\n        pathname: \"/groups/[id]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fgroups%2F%5Bid%5D%2Fpage&page=%2Fgroups%2F%5Bid%5D%2Fpage&appPaths=%2Fgroups%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fgroups%2F%5Bid%5D%2Fpage.tsx&appDir=%2FUsers%2Fhlogi%2FCode%2Fkhenesis%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fhlogi%2FCode%2Fkhenesis&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fhlogi%2FCode%2Fkhenesis%2Fapp%2Fcomponents%2Fui%2Ftoast.tsx&modules=%2FUsers%2Fhlogi%2FCode%2Fkhenesis%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fhlogi%2FCode%2Fkhenesis%2Fapp%2Fglobals.css&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fhlogi%2FCode%2Fkhenesis%2Fapp%2Fcomponents%2Fui%2Ftoast.tsx&modules=%2FUsers%2Fhlogi%2FCode%2Fkhenesis%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fhlogi%2FCode%2Fkhenesis%2Fapp%2Fglobals.css&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/ui/toast.tsx */ \"(ssr)/./app/components/ui/toast.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZobG9naSUyRkNvZGUlMkZraGVuZXNpcyUyRmFwcCUyRmNvbXBvbmVudHMlMkZ1aSUyRnRvYXN0LnRzeCZtb2R1bGVzPSUyRlVzZXJzJTJGaGxvZ2klMkZDb2RlJTJGa2hlbmVzaXMlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZm9udCUyRmdvb2dsZSUyRnRhcmdldC5jc3MlM0YlN0IlMjJwYXRoJTIyJTNBJTIyYXBwJTJGbGF5b3V0LnRzeCUyMiUyQyUyMmltcG9ydCUyMiUzQSUyMkludGVyJTIyJTJDJTIyYXJndW1lbnRzJTIyJTNBJTVCJTdCJTIyc3Vic2V0cyUyMiUzQSU1QiUyMmxhdGluJTIyJTVEJTdEJTVEJTJDJTIydmFyaWFibGVOYW1lJTIyJTNBJTIyaW50ZXIlMjIlN0QmbW9kdWxlcz0lMkZVc2VycyUyRmhsb2dpJTJGQ29kZSUyRmtoZW5lc2lzJTJGYXBwJTJGZ2xvYmFscy5jc3Mmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va2hlbmVzaXMvPzlmMWYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvaGxvZ2kvQ29kZS9raGVuZXNpcy9hcHAvY29tcG9uZW50cy91aS90b2FzdC50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fhlogi%2FCode%2Fkhenesis%2Fapp%2Fcomponents%2Fui%2Ftoast.tsx&modules=%2FUsers%2Fhlogi%2FCode%2Fkhenesis%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fhlogi%2FCode%2Fkhenesis%2Fapp%2Fglobals.css&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fhlogi%2FCode%2Fkhenesis%2Fapp%2Fgroups%2F%5Bid%5D%2Fpage.tsx&server=true!":
/*!**************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fhlogi%2FCode%2Fkhenesis%2Fapp%2Fgroups%2F%5Bid%5D%2Fpage.tsx&server=true! ***!
  \**************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/groups/[id]/page.tsx */ \"(ssr)/./app/groups/[id]/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZobG9naSUyRkNvZGUlMkZraGVuZXNpcyUyRmFwcCUyRmdyb3VwcyUyRiU1QmlkJTVEJTJGcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va2hlbmVzaXMvPzk5YTQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvaGxvZ2kvQ29kZS9raGVuZXNpcy9hcHAvZ3JvdXBzL1tpZF0vcGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fhlogi%2FCode%2Fkhenesis%2Fapp%2Fgroups%2F%5Bid%5D%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fhlogi%2FCode%2Fkhenesis%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fhlogi%2FCode%2Fkhenesis%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fhlogi%2FCode%2Fkhenesis%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fhlogi%2FCode%2Fkhenesis%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fhlogi%2FCode%2Fkhenesis%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fhlogi%2FCode%2Fkhenesis%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fhlogi%2FCode%2Fkhenesis%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fhlogi%2FCode%2Fkhenesis%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fhlogi%2FCode%2Fkhenesis%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fhlogi%2FCode%2Fkhenesis%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fhlogi%2FCode%2Fkhenesis%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fhlogi%2FCode%2Fkhenesis%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fhlogi%2FCode%2Fkhenesis%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fhlogi%2FCode%2Fkhenesis%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fhlogi%2FCode%2Fkhenesis%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fhlogi%2FCode%2Fkhenesis%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fhlogi%2FCode%2Fkhenesis%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fhlogi%2FCode%2Fkhenesis%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/components/ui/toast.tsx":
/*!*************************************!*\
  !*** ./app/components/ui/toast.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ToastProvider,useToast auto */ \n\n\nconst ToastContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createContext(undefined);\nfunction ToastProvider({ children }) {\n    const [toasts, setToasts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const toast = (props)=>{\n        const id = Math.random().toString(36).substring(2, 9);\n        const newToast = {\n            ...props,\n            id\n        };\n        setToasts((prev)=>[\n                ...prev,\n                newToast\n            ]);\n        // Auto dismiss\n        if (props.duration !== Infinity) {\n            setTimeout(()=>{\n                setToasts((prev)=>prev.filter((t)=>t.id !== id));\n                props.onClose?.();\n            }, props.duration || 5000);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContext.Provider, {\n        value: {\n            toast\n        },\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"toast-container\",\n                children: toasts.map((t)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"toast-message\",\n                        children: [\n                            t.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"toast-title\",\n                                children: t.title\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/components/ui/toast.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 25\n                            }, this),\n                            t.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"toast-description\",\n                                children: t.description\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/components/ui/toast.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, t.id, true, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/components/ui/toast.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/components/ui/toast.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Code/khenesis/app/components/ui/toast.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\nfunction useToast() {\n    const context = react__WEBPACK_IMPORTED_MODULE_1__.useContext(ToastContext);\n    if (!context) {\n        throw new Error(\"useToast must be used within a ToastProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy91aS90b2FzdC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUUrQjtBQUNhO0FBZTVDLE1BQU1FLDZCQUFlRixnREFBbUIsQ0FDdENJO0FBR0ssU0FBU0MsY0FBYyxFQUFFQyxRQUFRLEVBQWlDO0lBQ3ZFLE1BQU0sQ0FBQ0MsUUFBUUMsVUFBVSxHQUFHUCwrQ0FBUUEsQ0FBa0MsRUFBRTtJQUV4RSxNQUFNUSxRQUFRLENBQUNDO1FBQ2IsTUFBTUMsS0FBS0MsS0FBS0MsTUFBTSxHQUFHQyxRQUFRLENBQUMsSUFBSUMsU0FBUyxDQUFDLEdBQUc7UUFDbkQsTUFBTUMsV0FBVztZQUFFLEdBQUdOLEtBQUs7WUFBRUM7UUFBRztRQUNoQ0gsVUFBVSxDQUFDUyxPQUFTO21CQUFJQTtnQkFBTUQ7YUFBUztRQUV2QyxlQUFlO1FBQ2YsSUFBSU4sTUFBTVEsUUFBUSxLQUFLQyxVQUFVO1lBQy9CQyxXQUFXO2dCQUNUWixVQUFVLENBQUNTLE9BQVNBLEtBQUtJLE1BQU0sQ0FBQyxDQUFDQyxJQUFNQSxFQUFFWCxFQUFFLEtBQUtBO2dCQUNoREQsTUFBTWEsT0FBTztZQUNmLEdBQUdiLE1BQU1RLFFBQVEsSUFBSTtRQUN2QjtJQUNGO0lBRUEscUJBQ0UsOERBQUNoQixhQUFhc0IsUUFBUTtRQUFDQyxPQUFPO1lBQUVoQjtRQUFNOztZQUNuQ0g7MEJBQ0QsOERBQUNvQjtnQkFBSUMsV0FBVTswQkFDWnBCLE9BQU9xQixHQUFHLENBQUMsQ0FBQ04sa0JBQ1gsOERBQUNJO3dCQUFlQyxXQUFVOzs0QkFDdkJMLEVBQUVPLEtBQUssa0JBQUksOERBQUNIO2dDQUFJQyxXQUFVOzBDQUFlTCxFQUFFTyxLQUFLOzs7Ozs7NEJBQ2hEUCxFQUFFUSxXQUFXLGtCQUNaLDhEQUFDSjtnQ0FBSUMsV0FBVTswQ0FBcUJMLEVBQUVRLFdBQVc7Ozs7Ozs7dUJBSDNDUixFQUFFWCxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7O0FBVXhCO0FBRU8sU0FBU29CO0lBQ2QsTUFBTUMsVUFBVWhDLDZDQUFnQixDQUFDRTtJQUVqQyxJQUFJLENBQUM4QixTQUFTO1FBQ1osTUFBTSxJQUFJRSxNQUFNO0lBQ2xCO0lBRUEsT0FBT0Y7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL2toZW5lc2lzLy4vYXBwL2NvbXBvbmVudHMvdWkvdG9hc3QudHN4PzJjYzciXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gXCJyZWFjdFwiO1xuXG5leHBvcnQgaW50ZXJmYWNlIFRvYXN0UHJvcHMge1xuICB0aXRsZT86IHN0cmluZztcbiAgZGVzY3JpcHRpb24/OiBzdHJpbmc7XG4gIGFjdGlvbj86IFJlYWN0LlJlYWN0Tm9kZTtcbiAgY2xhc3NOYW1lPzogc3RyaW5nO1xuICBkdXJhdGlvbj86IG51bWJlcjtcbiAgb25DbG9zZT86ICgpID0+IHZvaWQ7XG59XG5cbmludGVyZmFjZSBUb2FzdENvbnRleHRUeXBlIHtcbiAgdG9hc3Q6IChwcm9wczogVG9hc3RQcm9wcykgPT4gdm9pZDtcbn1cblxuY29uc3QgVG9hc3RDb250ZXh0ID0gUmVhY3QuY3JlYXRlQ29udGV4dDxUb2FzdENvbnRleHRUeXBlIHwgdW5kZWZpbmVkPihcbiAgdW5kZWZpbmVkXG4pO1xuXG5leHBvcnQgZnVuY3Rpb24gVG9hc3RQcm92aWRlcih7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSB7XG4gIGNvbnN0IFt0b2FzdHMsIHNldFRvYXN0c10gPSB1c2VTdGF0ZTwoVG9hc3RQcm9wcyAmIHsgaWQ6IHN0cmluZyB9KVtdPihbXSk7XG5cbiAgY29uc3QgdG9hc3QgPSAocHJvcHM6IFRvYXN0UHJvcHMpID0+IHtcbiAgICBjb25zdCBpZCA9IE1hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cmluZygyLCA5KTtcbiAgICBjb25zdCBuZXdUb2FzdCA9IHsgLi4ucHJvcHMsIGlkIH07XG4gICAgc2V0VG9hc3RzKChwcmV2KSA9PiBbLi4ucHJldiwgbmV3VG9hc3RdKTtcblxuICAgIC8vIEF1dG8gZGlzbWlzc1xuICAgIGlmIChwcm9wcy5kdXJhdGlvbiAhPT0gSW5maW5pdHkpIHtcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICBzZXRUb2FzdHMoKHByZXYpID0+IHByZXYuZmlsdGVyKCh0KSA9PiB0LmlkICE9PSBpZCkpO1xuICAgICAgICBwcm9wcy5vbkNsb3NlPy4oKTtcbiAgICAgIH0sIHByb3BzLmR1cmF0aW9uIHx8IDUwMDApO1xuICAgIH1cbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxUb2FzdENvbnRleHQuUHJvdmlkZXIgdmFsdWU9e3sgdG9hc3QgfX0+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInRvYXN0LWNvbnRhaW5lclwiPlxuICAgICAgICB7dG9hc3RzLm1hcCgodCkgPT4gKFxuICAgICAgICAgIDxkaXYga2V5PXt0LmlkfSBjbGFzc05hbWU9XCJ0b2FzdC1tZXNzYWdlXCI+XG4gICAgICAgICAgICB7dC50aXRsZSAmJiA8ZGl2IGNsYXNzTmFtZT1cInRvYXN0LXRpdGxlXCI+e3QudGl0bGV9PC9kaXY+fVxuICAgICAgICAgICAge3QuZGVzY3JpcHRpb24gJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRvYXN0LWRlc2NyaXB0aW9uXCI+e3QuZGVzY3JpcHRpb259PC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApKX1cbiAgICAgIDwvZGl2PlxuICAgIDwvVG9hc3RDb250ZXh0LlByb3ZpZGVyPlxuICApO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gdXNlVG9hc3QoKSB7XG4gIGNvbnN0IGNvbnRleHQgPSBSZWFjdC51c2VDb250ZXh0KFRvYXN0Q29udGV4dCk7XG5cbiAgaWYgKCFjb250ZXh0KSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFwidXNlVG9hc3QgbXVzdCBiZSB1c2VkIHdpdGhpbiBhIFRvYXN0UHJvdmlkZXJcIik7XG4gIH1cblxuICByZXR1cm4gY29udGV4dDtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwiVG9hc3RDb250ZXh0IiwiY3JlYXRlQ29udGV4dCIsInVuZGVmaW5lZCIsIlRvYXN0UHJvdmlkZXIiLCJjaGlsZHJlbiIsInRvYXN0cyIsInNldFRvYXN0cyIsInRvYXN0IiwicHJvcHMiLCJpZCIsIk1hdGgiLCJyYW5kb20iLCJ0b1N0cmluZyIsInN1YnN0cmluZyIsIm5ld1RvYXN0IiwicHJldiIsImR1cmF0aW9uIiwiSW5maW5pdHkiLCJzZXRUaW1lb3V0IiwiZmlsdGVyIiwidCIsIm9uQ2xvc2UiLCJQcm92aWRlciIsInZhbHVlIiwiZGl2IiwiY2xhc3NOYW1lIiwibWFwIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsInVzZVRvYXN0IiwiY29udGV4dCIsInVzZUNvbnRleHQiLCJFcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ui/toast.tsx\n");

/***/ }),

/***/ "(ssr)/./app/groups/[id]/page.tsx":
/*!**********************************!*\
  !*** ./app/groups/[id]/page.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GroupDetail)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layouts_mobile_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layouts/mobile-layout */ \"(ssr)/./components/layouts/mobile-layout.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(ssr)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/smile.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/reply.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/dialog */ \"(ssr)/./components/ui/dialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n// Removed quote components - quotes functionality has been removed\n\n// Mock data - would be fetched based on [id] in real app\nconst groupData = {\n    id: 1,\n    name: \"Living Room Remodel Group\",\n    description: \"A collective purchase for premium living room furniture at wholesale prices.\",\n    stage: \"suggestion\",\n    suggestedProducts: [\n        {\n            id: 1,\n            name: \"Premium Leather Sofa Set\",\n            price: 3500,\n            image: \"/images/placeholder.png\",\n            description: \"Genuine leather sofa set with matching ottoman\",\n            merchant: \"Luxury Furniture Co.\",\n            source: \"internal\",\n            reactions: [\n                {\n                    id: 1,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 11:20\"\n                },\n                {\n                    id: 2,\n                    userId: 2,\n                    userName: \"John Doe\",\n                    emoji: \"❤️\",\n                    timestamp: \"2023-03-10 11:25\"\n                },\n                {\n                    id: 3,\n                    userId: 3,\n                    userName: \"Alice Johnson\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 11:30\"\n                }\n            ],\n            consensusScore: 85,\n            threadCount: 3\n        },\n        {\n            id: 2,\n            name: \"Modern Fabric Sectional\",\n            price: 2800,\n            image: \"/images/placeholder.png\",\n            description: \"L-shaped sectional with chaise lounge in premium fabric\",\n            merchant: \"Contemporary Home\",\n            source: \"internal\",\n            reactions: [\n                {\n                    id: 4,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 11:35\"\n                },\n                {\n                    id: 5,\n                    userId: 4,\n                    userName: \"Bob Williams\",\n                    emoji: \"\\uD83E\\uDD14\",\n                    timestamp: \"2023-03-10 11:40\"\n                },\n                {\n                    id: 8,\n                    userId: 5,\n                    userName: \"Carol Davis\",\n                    emoji: \"\\uD83D\\uDC4E\",\n                    timestamp: \"2023-03-10 11:45\"\n                },\n                {\n                    id: 9,\n                    userId: 6,\n                    userName: \"Mike Johnson\",\n                    emoji: \"\\uD83D\\uDCB8\",\n                    timestamp: \"2023-03-10 11:50\"\n                }\n            ],\n            consensusScore: 45,\n            threadCount: 2\n        },\n        {\n            id: 3,\n            name: \"Custom Wood Frame Sofa\",\n            price: 0,\n            image: \"/images/placeholder.png\",\n            description: \"Hand-crafted wooden frame sofa with custom upholstery\",\n            merchant: null,\n            source: \"external\",\n            reactions: [\n                {\n                    id: 6,\n                    userId: 5,\n                    userName: \"Carol Davis\",\n                    emoji: \"\\uD83D\\uDE0D\",\n                    timestamp: \"2023-03-10 12:20\"\n                },\n                {\n                    id: 7,\n                    userId: 2,\n                    userName: \"John Doe\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 12:25\"\n                }\n            ],\n            consensusScore: 70,\n            threadCount: 1\n        }\n    ],\n    selectedProduct: null,\n    product: {\n        name: \"Premium Leather Sofa Set\",\n        price: 3500,\n        image: \"/images/placeholder.png\"\n    },\n    members: [\n        {\n            id: 1,\n            name: \"Jane Smith\",\n            isAdmin: true,\n            amountPaid: 850\n        },\n        {\n            id: 2,\n            name: \"John Doe\",\n            isAdmin: false,\n            amountPaid: 700\n        },\n        {\n            id: 3,\n            name: \"Alice Johnson\",\n            isAdmin: false,\n            amountPaid: 600\n        },\n        {\n            id: 4,\n            name: \"Bob Williams\",\n            isAdmin: false,\n            amountPaid: 0\n        },\n        {\n            id: 5,\n            name: \"Carol Davis\",\n            isAdmin: false,\n            amountPaid: 0\n        }\n    ],\n    amountPaid: 2150,\n    totalAmount: 3500,\n    expiresIn: \"5 days\",\n    status: \"manufacturing\",\n    manufacturingProgress: 65,\n    manufacturingUpdates: [\n        {\n            id: 1,\n            date: \"2023-03-15\",\n            title: \"Production Started\",\n            description: \"Materials sourced and production has begun.\",\n            images: [\n                \"/images/placeholder.png\"\n            ]\n        },\n        {\n            id: 2,\n            date: \"2023-03-18\",\n            title: \"Frame Assembly\",\n            description: \"Wooden frames are assembled and ready for upholstery.\",\n            images: [\n                \"/images/placeholder.png\"\n            ]\n        },\n        {\n            id: 3,\n            date: \"2023-03-21\",\n            title: \"Upholstery Progress\",\n            description: \"Leather upholstery is being applied to the frames.\",\n            images: [\n                \"/images/placeholder.png\"\n            ]\n        }\n    ],\n    messages: [\n        {\n            id: 1,\n            user: \"Jane Smith\",\n            userId: 1,\n            content: \"Welcome everyone to our group buy!\",\n            timestamp: \"2023-03-10 10:23\",\n            type: \"text\",\n            reactions: [\n                {\n                    id: 1,\n                    userId: 2,\n                    userName: \"John Doe\",\n                    emoji: \"\\uD83D\\uDC4B\",\n                    timestamp: \"2023-03-10 10:25\"\n                },\n                {\n                    id: 2,\n                    userId: 3,\n                    userName: \"Alice Johnson\",\n                    emoji: \"\\uD83C\\uDF89\",\n                    timestamp: \"2023-03-10 10:26\"\n                }\n            ]\n        },\n        {\n            id: 2,\n            user: \"John Doe\",\n            userId: 2,\n            content: \"Thanks for organizing this!\",\n            timestamp: \"2023-03-10 10:45\",\n            type: \"text\",\n            reactions: [\n                {\n                    id: 3,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"❤️\",\n                    timestamp: \"2023-03-10 10:46\"\n                }\n            ]\n        },\n        {\n            id: 3,\n            user: \"Alice Johnson\",\n            userId: 3,\n            content: \"I added a Premium Leather Sofa Set to our product suggestions. What do you all think?\",\n            timestamp: \"2023-03-10 11:15\",\n            type: \"product-suggestion\",\n            productRef: 1,\n            detectedProducts: [\n                {\n                    text: \"Premium Leather Sofa Set\",\n                    startIndex: 9,\n                    endIndex: 33,\n                    suggestedProductId: 1,\n                    confidence: 0.95\n                }\n            ],\n            reactions: [\n                {\n                    id: 4,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 11:20\"\n                },\n                {\n                    id: 5,\n                    userId: 2,\n                    userName: \"John Doe\",\n                    emoji: \"\\uD83E\\uDD14\",\n                    timestamp: \"2023-03-10 11:22\"\n                }\n            ]\n        },\n        {\n            id: 4,\n            user: \"Jane Smith\",\n            userId: 1,\n            content: \"I like it but it's a bit pricey. I found this fabric sectional that might be more budget-friendly.\",\n            timestamp: \"2023-03-10 11:30\",\n            type: \"product-suggestion\",\n            productRef: 2,\n            parentMessageId: 3,\n            threadId: \"product-1-discussion\",\n            detectedProducts: [\n                {\n                    text: \"fabric sectional\",\n                    startIndex: 55,\n                    endIndex: 70,\n                    suggestedProductId: 2,\n                    confidence: 0.88\n                }\n            ],\n            reactions: [\n                {\n                    id: 6,\n                    userId: 4,\n                    userName: \"Bob Williams\",\n                    emoji: \"\\uD83D\\uDCB0\",\n                    timestamp: \"2023-03-10 11:35\"\n                },\n                {\n                    id: 10,\n                    userId: 5,\n                    userName: \"Carol Davis\",\n                    emoji: \"\\uD83D\\uDC4E\",\n                    timestamp: \"2023-03-10 11:37\"\n                }\n            ]\n        },\n        {\n            id: 5,\n            user: \"Bob Williams\",\n            userId: 4,\n            content: \"I saw this custom sofa at a local craftsman's shop. Uploading a photo I took.\",\n            timestamp: \"2023-03-10 12:15\",\n            type: \"product-suggestion\",\n            productRef: 3,\n            attachment: \"/images/placeholder.png\",\n            detectedProducts: [\n                {\n                    text: \"custom sofa\",\n                    startIndex: 12,\n                    endIndex: 23,\n                    suggestedProductId: 3,\n                    confidence: 0.92\n                }\n            ],\n            reactions: [\n                {\n                    id: 7,\n                    userId: 5,\n                    userName: \"Carol Davis\",\n                    emoji: \"\\uD83D\\uDCF8\",\n                    timestamp: \"2023-03-10 12:20\"\n                },\n                {\n                    id: 8,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"\\uD83D\\uDC40\",\n                    timestamp: \"2023-03-10 12:22\"\n                }\n            ]\n        },\n        {\n            id: 6,\n            user: \"John Doe\",\n            userId: 2,\n            content: \"The leather sofa looks amazing! How's the delivery time?\",\n            timestamp: \"2023-03-10 12:30\",\n            type: \"text\",\n            parentMessageId: 3,\n            threadId: \"product-1-discussion\",\n            detectedProducts: [\n                {\n                    text: \"leather sofa\",\n                    startIndex: 4,\n                    endIndex: 16,\n                    suggestedProductId: 1,\n                    confidence: 0.9\n                }\n            ]\n        },\n        {\n            id: 7,\n            user: \"Carol Davis\",\n            userId: 5,\n            content: \"I'm really interested in the custom wood frame option. Can we get more details?\",\n            timestamp: \"2023-03-10 13:00\",\n            type: \"text\",\n            parentMessageId: 5,\n            threadId: \"product-3-discussion\",\n            detectedProducts: [\n                {\n                    text: \"custom wood frame\",\n                    startIndex: 30,\n                    endIndex: 47,\n                    suggestedProductId: 3,\n                    confidence: 0.85\n                }\n            ]\n        }\n    ]\n};\n// Helper function to get the appropriate default tab based on group stage\nconst getDefaultTab = (stage)=>{\n    switch(stage){\n        case \"suggestion\":\n            return \"discussion\"; // suggestions are now part of discussion\n        case \"discussion\":\n            return \"discussion\";\n        case \"payment\":\n            return \"payment\";\n        case \"manufacturing\":\n            return \"manufacturing\";\n        case \"shipping\":\n            return \"shipping\";\n        default:\n            return \"discussion\";\n    }\n};\nfunction GroupDetail({ params }) {\n    // In a real app, you would fetch the group data based on params.id\n    const defaultTab = getDefaultTab(groupData.stage);\n    const [selectedProductRef, setSelectedProductRef] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showProductSelector, setShowProductSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProductsOverview, setShowProductsOverview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messageText, setMessageText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Phase 2 state management\n    const [expandedThreads, setExpandedThreads] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [showEmojiPicker, setShowEmojiPicker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [replyingTo, setReplyingTo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentUserId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1); // Mock current user - Jane Smith\n    // Local state for dynamic updates (in a real app, this would be managed by a state management system)\n    const [localMessages, setLocalMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(groupData.messages);\n    const [localProducts, setLocalProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(groupData.suggestedProducts);\n    const [replyText, setReplyText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleSendMessage = ()=>{\n        if (!messageText.trim()) return;\n        const newMessage = {\n            id: Date.now(),\n            user: \"Jane Smith\",\n            userId: currentUserId,\n            content: messageText,\n            timestamp: new Date().toLocaleString(),\n            type: selectedProductRef ? \"product-suggestion\" : \"text\",\n            productRef: selectedProductRef || undefined,\n            reactions: []\n        };\n        setLocalMessages((prev)=>[\n                ...prev,\n                newMessage\n            ]);\n        setMessageText(\"\");\n        setSelectedProductRef(null);\n    };\n    const handleSendReply = (parentMessageId)=>{\n        if (!replyText.trim()) return;\n        const parentMessage = localMessages.find((m)=>m.id === parentMessageId);\n        const newReply = {\n            id: Date.now(),\n            user: \"Jane Smith\",\n            userId: currentUserId,\n            content: replyText,\n            timestamp: new Date().toLocaleString(),\n            type: \"text\",\n            parentMessageId: parentMessageId,\n            threadId: parentMessage?.threadId || `thread-${parentMessageId}`,\n            reactions: []\n        };\n        console.log(\"Creating reply:\", newReply);\n        console.log(\"Current messages before:\", localMessages.length);\n        setLocalMessages((prev)=>{\n            const updated = [\n                ...prev,\n                newReply\n            ];\n            console.log(\"Updated messages after:\", updated.length);\n            return updated;\n        });\n        // Auto-expand the thread to show the new reply\n        setExpandedThreads((prev)=>{\n            const newSet = new Set(prev);\n            newSet.add(`thread-${parentMessageId}`);\n            return newSet;\n        });\n        setReplyText(\"\");\n        setReplyingTo(null);\n    };\n    const handleSelectProduct = (productId)=>{\n        setSelectedProductRef(productId);\n        setShowProductSelector(false);\n    };\n    // Phase 2 helper functions\n    const handleAddReaction = (emoji, messageId, productId)=>{\n        if (messageId) {\n            // Check if user already has a reaction on this message\n            const message = localMessages.find((m)=>m.id === messageId);\n            const existingReaction = message?.reactions?.find((r)=>r.userId === currentUserId);\n            setLocalMessages((prev)=>prev.map((msg)=>{\n                    if (msg.id === messageId) {\n                        let newReactions = msg.reactions || [];\n                        if (existingReaction) {\n                            // Replace existing reaction\n                            newReactions = newReactions.map((r)=>r.userId === currentUserId ? {\n                                    ...r,\n                                    emoji,\n                                    timestamp: new Date().toLocaleString()\n                                } : r);\n                        } else {\n                            // Add new reaction\n                            const newReaction = {\n                                id: Date.now(),\n                                userId: currentUserId,\n                                userName: \"Jane Smith\",\n                                emoji,\n                                timestamp: new Date().toLocaleString()\n                            };\n                            newReactions = [\n                                ...newReactions,\n                                newReaction\n                            ];\n                        }\n                        return {\n                            ...msg,\n                            reactions: newReactions\n                        };\n                    }\n                    return msg;\n                }));\n        }\n        if (productId) {\n            // Check if user already has a reaction on this product\n            const product = localProducts.find((p)=>p.id === productId);\n            const existingReaction = product?.reactions?.find((r)=>r.userId === currentUserId);\n            setLocalProducts((prev)=>prev.map((product)=>{\n                    if (product.id === productId) {\n                        let newReactions = product.reactions || [];\n                        if (existingReaction) {\n                            // Replace existing reaction\n                            newReactions = newReactions.map((r)=>r.userId === currentUserId ? {\n                                    ...r,\n                                    emoji,\n                                    timestamp: new Date().toLocaleString()\n                                } : r);\n                        } else {\n                            // Add new reaction\n                            const newReaction = {\n                                id: Date.now(),\n                                userId: currentUserId,\n                                userName: \"Jane Smith\",\n                                emoji,\n                                timestamp: new Date().toLocaleString()\n                            };\n                            newReactions = [\n                                ...newReactions,\n                                newReaction\n                            ];\n                        }\n                        return {\n                            ...product,\n                            reactions: newReactions,\n                            consensusScore: calculateConsensusScore(newReactions)\n                        };\n                    }\n                    return product;\n                }));\n        }\n        setShowEmojiPicker(null);\n    };\n    const handleDeleteMessage = (messageId)=>{\n        // Remove the message and any replies to it\n        setLocalMessages((prev)=>prev.filter((msg)=>msg.id !== messageId && msg.parentMessageId !== messageId));\n    };\n    const handleDeleteReaction = (reactionId, messageId, productId)=>{\n        if (messageId) {\n            setLocalMessages((prev)=>prev.map((msg)=>msg.id === messageId ? {\n                        ...msg,\n                        reactions: msg.reactions?.filter((r)=>r.id !== reactionId) || []\n                    } : msg));\n        }\n        if (productId) {\n            setLocalProducts((prev)=>prev.map((product)=>{\n                    if (product.id === productId) {\n                        const newReactions = product.reactions?.filter((r)=>r.id !== reactionId) || [];\n                        return {\n                            ...product,\n                            reactions: newReactions,\n                            consensusScore: calculateConsensusScore(newReactions)\n                        };\n                    }\n                    return product;\n                }));\n        }\n    };\n    const toggleThread = (threadId)=>{\n        const newExpanded = new Set(expandedThreads);\n        if (newExpanded.has(threadId)) {\n            newExpanded.delete(threadId);\n        } else {\n            newExpanded.add(threadId);\n        }\n        setExpandedThreads(newExpanded);\n    };\n    const getThreadMessages = (parentMessageId)=>{\n        return localMessages.filter((msg)=>msg.parentMessageId === parentMessageId);\n    };\n    const getMainMessages = ()=>{\n        return localMessages.filter((msg)=>!msg.parentMessageId);\n    };\n    const getConsensusColor = (score)=>{\n        if (score >= 80) return \"text-green-600\";\n        if (score >= 60) return \"text-yellow-600\";\n        return \"text-red-600\";\n    };\n    const calculateConsensusScore = (reactions)=>{\n        if (!reactions || reactions.length === 0) return 0;\n        const positiveEmojis = [\n            \"\\uD83D\\uDC4D\",\n            \"❤️\",\n            \"\\uD83D\\uDE0D\",\n            \"\\uD83C\\uDF89\",\n            \"\\uD83D\\uDD25\",\n            \"\\uD83D\\uDCA1\",\n            \"✨\"\n        ];\n        const negativeEmojis = [\n            \"\\uD83D\\uDC4E\",\n            \"\\uD83D\\uDE15\",\n            \"❌\",\n            \"\\uD83D\\uDCB8\",\n            \"⚠️\",\n            \"\\uD83D\\uDEAB\"\n        ];\n        // neutralEmojis: [\"🤔\", \"👀\", \"📸\", \"👋\", \"💰\"] - treated as neutral (0.5 weight)\n        let positiveCount = 0;\n        let negativeCount = 0;\n        let neutralCount = 0;\n        reactions.forEach((reaction)=>{\n            if (positiveEmojis.includes(reaction.emoji)) {\n                positiveCount++;\n            } else if (negativeEmojis.includes(reaction.emoji)) {\n                negativeCount++;\n            } else {\n                neutralCount++;\n            }\n        });\n        const totalReactions = reactions.length;\n        const positiveWeight = positiveCount * 1.0;\n        const neutralWeight = neutralCount * 0.5;\n        const negativeWeight = negativeCount * 0.0;\n        const weightedScore = (positiveWeight + neutralWeight + negativeWeight) / totalReactions;\n        return Math.round(weightedScore * 100);\n    };\n    const renderDetectedProducts = (content, detectedProducts)=>{\n        if (!detectedProducts || detectedProducts.length === 0) {\n            return content;\n        }\n        let result = content;\n        let offset = 0;\n        detectedProducts.sort((a, b)=>a.startIndex - b.startIndex).forEach((detected)=>{\n            const start = detected.startIndex + offset;\n            const end = detected.endIndex + offset;\n            const productName = result.substring(start, end);\n            const replacement = `<span class=\"bg-blue-100 text-blue-800 px-1 rounded cursor-pointer hover:bg-blue-200\" data-product-id=\"${detected.suggestedProductId}\">${productName}</span>`;\n            result = result.substring(0, start) + replacement + result.substring(end);\n            offset += replacement.length - productName.length;\n        });\n        return result;\n    };\n    const renderStageIndicator = ()=>{\n        const stages = [\n            {\n                id: \"suggestion\",\n                label: \"Suggestions\"\n            },\n            {\n                id: \"discussion\",\n                label: \"Discussion\"\n            },\n            {\n                id: \"payment\",\n                label: \"Payment\"\n            },\n            {\n                id: \"manufacturing\",\n                label: \"Manufacturing\"\n            },\n            {\n                id: \"shipping\",\n                label: \"Shipping\"\n            }\n        ];\n        const currentIndex = stages.findIndex((s)=>s.id === groupData.stage);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-sm font-medium mb-2\",\n                    children: \"Current Stage\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 814,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full bg-secondary rounded-full h-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-primary h-2 rounded-full transition-all duration-500 ease-in-out\",\n                        style: {\n                            width: `${(currentIndex + 1) / stages.length * 100}%`\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 816,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 815,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between mt-1 text-xs\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-primary font-medium\",\n                            children: stages[currentIndex].label\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 822,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-muted-foreground\",\n                            children: [\n                                \"Step \",\n                                currentIndex + 1,\n                                \" of \",\n                                stages.length\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 825,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 821,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n            lineNumber: 813,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layouts_mobile_layout__WEBPACK_IMPORTED_MODULE_2__.MobileLayout, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative bg-primary text-primary-foreground p-4 pb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                        href: \"/groups\",\n                        className: \"flex items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 837,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-1\",\n                                children: \"Back to Groups\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 838,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 836,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-fluid-xl font-bold\",\n                        children: groupData.name\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 840,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mt-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                size: 16,\n                                className: \"mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 842,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    groupData.members.length,\n                                    \" members\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 843,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mx-2\",\n                                children: \"•\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 844,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                size: 16,\n                                className: \"mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 845,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Expires in \",\n                                    groupData.expiresIn\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 846,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 841,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 835,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-4 pb-32\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"-mt-6 mb-6 relative z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"p-4\",\n                            children: renderStageIndicator()\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 852,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 851,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                        defaultValue: defaultTab,\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                                className: \"w-full grid grid-cols-1 mb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"discussion\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 858,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \"Discussion\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 859,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 857,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 856,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                                className: \"w-full grid grid-cols-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                        value: \"manufacturing\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 865,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"Manufacturing\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 866,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 864,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                        value: \"members\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 869,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"Members\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 870,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 868,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                        value: \"payment\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 873,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"Payment\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 874,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 872,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 863,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                value: \"discussion\",\n                                className: \"mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: getMainMessages().map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `rounded-lg p-3 ${message.type === \"product-suggestion\" ? \"bg-blue-50 border border-blue-200\" : \"bg-muted\"}`,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-start mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: message.user\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 895,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        message.type === \"product-suggestion\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                            variant: \"secondary\",\n                                                                            className: \"text-xs\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                    className: \"h-3 w-3 mr-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 898,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                \"Product\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 897,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 894,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-muted-foreground\",\n                                                                            children: message.timestamp\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 904,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                            variant: \"ghost\",\n                                                                            size: \"icon\",\n                                                                            className: \"h-6 w-6\",\n                                                                            onClick: ()=>setShowEmojiPicker({\n                                                                                    messageId: message.id\n                                                                                }),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"h-3 w-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 915,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 907,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                            variant: \"ghost\",\n                                                                            size: \"icon\",\n                                                                            className: \"h-6 w-6\",\n                                                                            onClick: ()=>setReplyingTo(message.id),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                className: \"h-3 w-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 923,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 917,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        message.userId === currentUserId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                            variant: \"ghost\",\n                                                                            size: \"icon\",\n                                                                            className: \"h-6 w-6 text-red-500 hover:text-red-700\",\n                                                                            onClick: ()=>handleDeleteMessage(message.id),\n                                                                            title: \"Delete message\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                className: \"h-3 w-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 933,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 926,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 903,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 893,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm mb-2\",\n                                                            dangerouslySetInnerHTML: {\n                                                                __html: renderDetectedProducts(message.content, message.detectedProducts)\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 939,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        message.reactions && message.reactions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-1 mb-2\",\n                                                            children: Object.entries(message.reactions.reduce((acc, reaction)=>{\n                                                                if (!acc[reaction.emoji]) {\n                                                                    acc[reaction.emoji] = [];\n                                                                }\n                                                                acc[reaction.emoji].push(reaction);\n                                                                return acc;\n                                                            }, {})).map(([emoji, reactions])=>{\n                                                                const userReaction = reactions.find((r)=>r.userId === currentUserId);\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: `h-6 px-2 text-xs relative group ${userReaction ? \"bg-blue-50 border-blue-200\" : \"\"}`,\n                                                                    title: `${reactions.map((r)=>r.userName).join(\", \")} reacted with ${emoji}`,\n                                                                    onClick: ()=>{\n                                                                        if (userReaction) {\n                                                                            handleDeleteReaction(userReaction.id, message.id);\n                                                                        }\n                                                                    },\n                                                                    children: [\n                                                                        emoji,\n                                                                        \" \",\n                                                                        reactions.length,\n                                                                        userReaction && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-3 h-3 text-xs flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                                            children: \"\\xd7\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 987,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, emoji, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 966,\n                                                                    columnNumber: 29\n                                                                }, this);\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 951,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        getThreadMessages(message.id).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            className: \"text-xs text-blue-600 hover:text-blue-800 p-0 h-auto\",\n                                                            onClick: ()=>toggleThread(`thread-${message.id}`),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-3 w-3 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1005,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                getThreadMessages(message.id).length,\n                                                                \" replies\",\n                                                                expandedThreads.has(`thread-${message.id}`) ? \" ▼\" : \" ▶\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 999,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        message.productRef && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-2 p-3 bg-background rounded border border-border\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-16 h-16 bg-muted rounded overflow-hidden flex items-center justify-center mr-3\",\n                                                                        children: localProducts.find((p)=>p.id === message.productRef)?.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"relative w-full h-full\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                                                                src: localProducts.find((p)=>p.id === message.productRef)?.image || \"/images/placeholder.png\",\n                                                                                alt: localProducts.find((p)=>p.id === message.productRef)?.name || \"Product\",\n                                                                                fill: true,\n                                                                                className: \"object-cover\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 1022,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1021,\n                                                                            columnNumber: 31\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-6 w-6 text-muted-foreground\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1038,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1017,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-start mb-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"font-medium text-sm\",\n                                                                                        children: localProducts.find((p)=>p.id === message.productRef)?.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                        lineNumber: 1043,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center gap-1\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                            variant: \"ghost\",\n                                                                                            size: \"icon\",\n                                                                                            className: \"h-6 w-6\",\n                                                                                            onClick: ()=>setShowEmojiPicker({\n                                                                                                    productId: message.productRef\n                                                                                                }),\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                                className: \"h-3 w-3\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                                lineNumber: 1061,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                            lineNumber: 1051,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                        lineNumber: 1050,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 1042,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-muted-foreground text-sm mb-2\",\n                                                                                children: [\n                                                                                    \"$\",\n                                                                                    localProducts.find((p)=>p.id === message.productRef)?.price || \"Price unavailable\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 1065,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            (()=>{\n                                                                                const product = localProducts.find((p)=>p.id === message.productRef);\n                                                                                return product?.reactions && product.reactions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex flex-wrap gap-1 mb-2\",\n                                                                                    children: Object.entries(product.reactions.reduce((acc, reaction)=>{\n                                                                                        if (!acc[reaction.emoji]) {\n                                                                                            acc[reaction.emoji] = [];\n                                                                                        }\n                                                                                        acc[reaction.emoji].push(reaction);\n                                                                                        return acc;\n                                                                                    }, {})).map(([emoji, reactions])=>{\n                                                                                        const userReaction = reactions.find((r)=>r.userId === currentUserId);\n                                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                            variant: \"outline\",\n                                                                                            size: \"sm\",\n                                                                                            className: `h-6 px-2 text-xs relative group ${userReaction ? \"bg-blue-50 border-blue-200\" : \"\"}`,\n                                                                                            title: `${reactions.map((r)=>r.userName).join(\", \")} reacted with ${emoji}`,\n                                                                                            onClick: ()=>{\n                                                                                                if (userReaction) {\n                                                                                                    handleDeleteReaction(userReaction.id, undefined, product.id);\n                                                                                                }\n                                                                                            },\n                                                                                            children: [\n                                                                                                emoji,\n                                                                                                \" \",\n                                                                                                reactions.length,\n                                                                                                userReaction && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-3 h-3 text-xs flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                                                                    children: \"\\xd7\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                                    lineNumber: 1125,\n                                                                                                    columnNumber: 45\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, emoji, true, {\n                                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                            lineNumber: 1101,\n                                                                                            columnNumber: 41\n                                                                                        }, this);\n                                                                                    })\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 1080,\n                                                                                    columnNumber: 35\n                                                                                }, this);\n                                                                            })(),\n                                                                            (()=>{\n                                                                                const product = localProducts.find((p)=>p.id === message.productRef);\n                                                                                return product?.consensusScore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center gap-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex-1 bg-gray-200 rounded-full h-2\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: `h-2 rounded-full ${product.consensusScore >= 80 ? \"bg-green-500\" : product.consensusScore >= 60 ? \"bg-yellow-500\" : \"bg-red-500\"}`,\n                                                                                                style: {\n                                                                                                    width: `${product.consensusScore}%`\n                                                                                                }\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                                lineNumber: 1146,\n                                                                                                columnNumber: 39\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                            lineNumber: 1145,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: `text-xs font-medium ${getConsensusColor(product.consensusScore)}`,\n                                                                                            children: [\n                                                                                                product.consensusScore,\n                                                                                                \"% consensus\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                            lineNumber: 1159,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 1144,\n                                                                                    columnNumber: 35\n                                                                                }, this);\n                                                                            })()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1041,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1016,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1015,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        message.attachment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full max-w-[200px] h-[150px] bg-muted rounded-md overflow-hidden relative\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 flex items-center justify-center text-muted-foreground\",\n                                                                    children: \"Image Attachment\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1178,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1177,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1176,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 886,\n                                                    columnNumber: 19\n                                                }, this),\n                                                expandedThreads.has(`thread-${message.id}`) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-4 pl-4 border-l-2 border-gray-200 space-y-2\",\n                                                    children: getThreadMessages(message.id).map((threadMessage)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gray-50 rounded-lg p-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-start mb-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-sm\",\n                                                                            children: threadMessage.user\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1195,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-muted-foreground\",\n                                                                                    children: threadMessage.timestamp\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 1199,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                threadMessage.userId === currentUserId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                    variant: \"ghost\",\n                                                                                    size: \"icon\",\n                                                                                    className: \"h-4 w-4 text-red-500 hover:text-red-700\",\n                                                                                    onClick: ()=>handleDeleteMessage(threadMessage.id),\n                                                                                    title: \"Delete reply\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                        className: \"h-2 w-2\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                        lineNumber: 1212,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 1203,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1198,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1194,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm\",\n                                                                    dangerouslySetInnerHTML: {\n                                                                        __html: renderDetectedProducts(threadMessage.content, threadMessage.detectedProducts)\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1217,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, threadMessage.id, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1190,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1188,\n                                                    columnNumber: 21\n                                                }, this),\n                                                replyingTo === message.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-2 p-2 bg-gray-50 rounded border\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-muted-foreground mb-2\",\n                                                            children: [\n                                                                \"Replying to \",\n                                                                message.user\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1234,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    placeholder: \"Type your reply...\",\n                                                                    className: \"flex-1 h-8 rounded-l border border-input bg-background px-2 text-sm\",\n                                                                    value: replyText,\n                                                                    onChange: (e)=>setReplyText(e.target.value),\n                                                                    onKeyDown: (e)=>{\n                                                                        if (e.key === \"Enter\" && !e.shiftKey) {\n                                                                            e.preventDefault();\n                                                                            handleSendReply(message.id);\n                                                                        }\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1238,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>handleSendReply(message.id),\n                                                                    disabled: !replyText.trim(),\n                                                                    className: \"rounded-l-none\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1257,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1251,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    size: \"sm\",\n                                                                    variant: \"outline\",\n                                                                    onClick: ()=>{\n                                                                        setReplyingTo(null);\n                                                                        setReplyText(\"\");\n                                                                    },\n                                                                    children: \"Cancel\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1259,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1237,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1233,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, message.id, true, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 884,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 882,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 881,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                value: \"payment\",\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                className: \"pb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                    className: \"text-lg\",\n                                                    children: \"Payment Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1287,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1286,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full bg-secondary rounded-full h-3 mb-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-primary h-3 rounded-full\",\n                                                            style: {\n                                                                width: `${groupData.amountPaid / groupData.totalAmount * 100}%`\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1291,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1290,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"$\",\n                                                                    groupData.amountPaid,\n                                                                    \" raised\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1301,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"$\",\n                                                                    groupData.totalAmount,\n                                                                    \" goal\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1302,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1300,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-muted-foreground text-center\",\n                                                        children: [\n                                                            \"$\",\n                                                            groupData.totalAmount - groupData.amountPaid,\n                                                            \" remaining\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1304,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1289,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardFooter, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                    href: `/groups/${params.id}/payment`,\n                                                    className: \"w-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        className: \"w-full\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1311,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Make a Payment\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1310,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1309,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1308,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1285,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium mb-2\",\n                                        children: \"Payment History\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1318,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: groupData.members.filter((member)=>member.amountPaid > 0).map((member)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-muted p-3 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-8 h-8 rounded-full bg-secondary flex items-center justify-center\",\n                                                                    children: member.name.charAt(0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1326,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: member.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1330,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-muted-foreground\",\n                                                                            children: new Date().toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1331,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1329,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1325,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: [\n                                                                \"$\",\n                                                                member.amountPaid\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1336,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1324,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, member.id, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1323,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1319,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 1284,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                value: \"manufacturing\",\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Manufacturing Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1346,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-primary font-medium capitalize\",\n                                                        children: groupData.status\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1347,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1345,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-secondary rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-primary h-2 rounded-full\",\n                                                    style: {\n                                                        width: `${groupData.manufacturingProgress}%`\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1352,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1351,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mt-1 text-xs text-muted-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Production Started\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1358,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Ready for Shipping\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1359,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1357,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1344,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium\",\n                                                children: \"Latest Updates\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1364,\n                                                columnNumber: 15\n                                            }, this),\n                                            groupData.manufacturingUpdates.map((update)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                    className: \"overflow-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium\",\n                                                                        children: update.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1369,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: update.date\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1370,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1368,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm mb-2\",\n                                                                children: update.description\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1374,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-24 bg-muted flex items-center justify-center rounded\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-muted-foreground\",\n                                                                    children: \"Update Image\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1376,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1375,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1367,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, update.id, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1366,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1363,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 1343,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                value: \"members\",\n                                className: \"mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: groupData.members.map((member)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 rounded-full bg-secondary flex items-center justify-center\",\n                                                            children: member.name.charAt(0)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1394,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"ml-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: member.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1399,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        member.isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-2 px-1.5 py-0.5 bg-primary/10 text-primary text-xs rounded\",\n                                                                            children: \"Admin\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1401,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1398,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-muted-foreground\",\n                                                                    children: member.amountPaid > 0 ? `Paid $${member.amountPaid}` : \"No payment yet\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1406,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1397,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1393,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-secondary text-secondary-foreground rounded-full flex items-center justify-center\",\n                                                    children: [\n                                                        Math.round(member.amountPaid / (groupData.totalAmount / groupData.members.length) * 100),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1413,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, member.id, true, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1389,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1387,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 1386,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 855,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 850,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                open: showProductSelector,\n                onOpenChange: setShowProductSelector,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                    className: \"sm:max-w-[425px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTitle, {\n                                    children: \"Select a Product\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1432,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogDescription, {\n                                    children: \"Choose a product to reference in your message\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1433,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1431,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3 mt-4 max-h-[300px] overflow-y-auto\",\n                            children: groupData.suggestedProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border rounded-md cursor-pointer hover:bg-muted flex items-center\",\n                                    onClick: ()=>handleSelectProduct(product.id),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-muted rounded overflow-hidden mr-3 flex items-center justify-center\",\n                                            children: product.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative w-full h-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                                    src: product.image || \"/images/placeholder.png\",\n                                                    alt: product.name,\n                                                    fill: true,\n                                                    className: \"object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1447,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1446,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-6 w-6 text-muted-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1455,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1444,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium\",\n                                                    children: product.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1459,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: product.price > 0 ? `$${product.price}` : \"Price unavailable\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1460,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1458,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, product.id, true, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1439,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1437,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 1430,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 1429,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-4 left-4 right-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        selectedProductRef && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-2 bg-white rounded-lg border border-border shadow-lg p-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-muted rounded overflow-hidden flex items-center justify-center mr-2\",\n                                        children: localProducts.find((p)=>p.id === selectedProductRef)?.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative w-full h-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                                src: localProducts.find((p)=>p.id === selectedProductRef)?.image || \"/images/placeholder.png\",\n                                                alt: localProducts.find((p)=>p.id === selectedProductRef)?.name || \"Product\",\n                                                fill: true,\n                                                className: \"object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1483,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1482,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1497,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1479,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-xs\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium\",\n                                                children: localProducts.find((p)=>p.id === selectedProductRef)?.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1501,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-muted-foreground\",\n                                                children: [\n                                                    \"$\",\n                                                    localProducts.find((p)=>p.id === selectedProductRef)?.price || \"Price unavailable\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1507,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1500,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        className: \"h-6 w-6 rounded-full\",\n                                        type: \"button\",\n                                        onClick: ()=>setSelectedProductRef(null),\n                                        children: \"\\xd7\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1513,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 1478,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1477,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"icon\",\n                                            className: \"rounded-full h-10 w-10 bg-white shadow-lg border\",\n                                            type: \"button\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"file-upload\",\n                                                className: \"cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        id: \"file-upload\",\n                                                        type: \"file\",\n                                                        accept: \"image/*\",\n                                                        className: \"sr-only\",\n                                                        \"aria-label\": \"Upload image\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1537,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1544,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1536,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1530,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"icon\",\n                                            className: \"rounded-full h-10 w-10 bg-white shadow-lg border\",\n                                            type: \"button\",\n                                            onClick: ()=>setShowProductSelector(true),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1554,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1547,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1529,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Type your message...\",\n                                            className: \"w-full h-10 rounded-full border border-input bg-white px-4 pr-12 text-sm shadow-lg ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n                                            value: messageText,\n                                            onChange: (e)=>setMessageText(e.target.value),\n                                            onKeyDown: (e)=>{\n                                                if (e.key === \"Enter\" && !e.shiftKey) {\n                                                    e.preventDefault();\n                                                    handleSendMessage();\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1560,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            size: \"icon\",\n                                            className: \"absolute right-1 top-1 h-8 w-8 rounded-full\",\n                                            onClick: handleSendMessage,\n                                            disabled: !messageText.trim(),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1579,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1573,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1559,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>setShowProductsOverview(true),\n                                    className: \"rounded-full shadow-lg h-10 px-4 bg-white border text-foreground hover:bg-accent\",\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1590,\n                                            columnNumber: 15\n                                        }, this),\n                                        localProducts.length\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1584,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1527,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 1474,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 1473,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                open: showProductsOverview,\n                onOpenChange: setShowProductsOverview,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                    className: \"sm:max-w-[500px] max-h-[80vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTitle, {\n                                    children: \"Product Suggestions\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1604,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogDescription, {\n                                    children: [\n                                        localProducts.length,\n                                        \" products suggested by the group\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1605,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1603,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 gap-4 mt-4\",\n                            children: localProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    className: \"overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-20 h-20 bg-muted flex items-center justify-center\",\n                                                children: product.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative w-full h-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                                        src: product.image || \"/images/placeholder.png\",\n                                                        alt: product.name,\n                                                        fill: true,\n                                                        className: \"object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1616,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1615,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-6 w-6 text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1624,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1613,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-sm\",\n                                                                children: product.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1629,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: `text-xs px-1.5 py-0.5 rounded ${product.source === \"internal\" ? \"bg-primary/10 text-primary\" : \"bg-secondary text-secondary-foreground\"}`,\n                                                                        children: product.source === \"internal\" ? \"Catalog\" : \"External\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1631,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-6 w-6\",\n                                                                        onClick: ()=>setShowEmojiPicker({\n                                                                                productId: product.id\n                                                                            }),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1650,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1642,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1630,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1628,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground mb-2\",\n                                                        children: product.description\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1654,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    product.reactions && product.reactions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1 mb-2\",\n                                                        children: Object.entries(product.reactions.reduce((acc, reaction)=>{\n                                                            if (!acc[reaction.emoji]) {\n                                                                acc[reaction.emoji] = [];\n                                                            }\n                                                            acc[reaction.emoji].push(reaction);\n                                                            return acc;\n                                                        }, {})).map(([emoji, reactions])=>{\n                                                            const userReaction = reactions.find((r)=>r.userId === currentUserId);\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                className: `h-5 px-1.5 text-xs relative group ${userReaction ? \"bg-blue-50 border-blue-200\" : \"\"}`,\n                                                                title: `${reactions.map((r)=>r.userName).join(\", \")} reacted with ${emoji}`,\n                                                                onClick: ()=>{\n                                                                    if (userReaction) {\n                                                                        handleDeleteReaction(userReaction.id, undefined, product.id);\n                                                                    }\n                                                                },\n                                                                children: [\n                                                                    emoji,\n                                                                    \" \",\n                                                                    reactions.length,\n                                                                    userReaction && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-2 h-2 text-xs flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                                        children: \"\\xd7\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1697,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                ]\n                                                            }, emoji, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1675,\n                                                                columnNumber: 29\n                                                            }, this);\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1660,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    product.consensusScore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1 bg-gray-200 rounded-full h-1.5\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: `h-1.5 rounded-full ${product.consensusScore >= 80 ? \"bg-green-500\" : product.consensusScore >= 60 ? \"bg-yellow-500\" : \"bg-red-500\"}`,\n                                                                            style: {\n                                                                                width: `${product.consensusScore}%`\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1712,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1711,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: `text-xs font-medium ${getConsensusColor(product.consensusScore)}`,\n                                                                        children: [\n                                                                            product.consensusScore,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1723,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1710,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: [\n                                                                    \"Group consensus • \",\n                                                                    product.threadCount || 0,\n                                                                    \" \",\n                                                                    \"discussions\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1731,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1709,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm\",\n                                                                children: product.price > 0 ? `$${product.price}` : \"Price unavailable\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1739,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>{\n                                                                    setShowProductsOverview(false);\n                                                                // In a real app, this would scroll to the product in discussion\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1752,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Discuss\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1744,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1738,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1627,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1612,\n                                        columnNumber: 17\n                                    }, this)\n                                }, product.id, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1611,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1609,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 1602,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 1598,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                open: !!showEmojiPicker,\n                onOpenChange: ()=>setShowEmojiPicker(null),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                    className: \"sm:max-w-[300px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTitle, {\n                                    children: \"Add Reaction\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1771,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogDescription, {\n                                    children: \"Choose an emoji to react with\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1772,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1770,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-6 gap-2 mt-4\",\n                            children: [\n                                \"\\uD83D\\uDC4D\",\n                                \"\\uD83D\\uDC4E\",\n                                \"❤️\",\n                                \"\\uD83D\\uDE0D\",\n                                \"\\uD83E\\uDD14\",\n                                \"\\uD83D\\uDE15\",\n                                \"\\uD83D\\uDCB0\",\n                                \"\\uD83D\\uDCB8\",\n                                \"\\uD83C\\uDF89\",\n                                \"❌\",\n                                \"\\uD83D\\uDC40\",\n                                \"\\uD83D\\uDCF8\",\n                                \"\\uD83D\\uDC4B\",\n                                \"\\uD83D\\uDD25\",\n                                \"\\uD83D\\uDCA1\",\n                                \"✨\",\n                                \"⚠️\",\n                                \"\\uD83D\\uDEAB\"\n                            ].map((emoji)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    className: \"h-12 w-12 text-2xl hover:bg-accent\",\n                                    onClick: ()=>handleAddReaction(emoji, showEmojiPicker?.messageId, showEmojiPicker?.productId),\n                                    children: emoji\n                                }, emoji, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1795,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1774,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 1769,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 1765,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n        lineNumber: 834,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/groups/[id]/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/layouts/mobile-layout.tsx":
/*!**********************************************!*\
  !*** ./components/layouts/mobile-layout.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MobileLayout: () => (/* binding */ MobileLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Home_Search_ShoppingBag_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Home,Search,ShoppingBag,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Home_Search_ShoppingBag_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Home,Search,ShoppingBag,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Home_Search_ShoppingBag_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Home,Search,ShoppingBag,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Home_Search_ShoppingBag_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Home,Search,ShoppingBag,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Home_Search_ShoppingBag_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Home,Search,ShoppingBag,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ MobileLayout auto */ \n\n\n\nfunction MobileLayout({ children, showBackButton, showNav = true }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex min-h-screen flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"sticky top-0 z-10 border-b bg-background p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        (pathname !== \"/\" || showBackButton) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.back(),\n                            className: \"rounded-full p-2 hover:bg-muted\",\n                            \"aria-label\": \"Go back\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Home_Search_ShoppingBag_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/components/layouts/mobile-layout.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/components/layouts/mobile-layout.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-lg font-medium\",\n                            children: \"Khenesis\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/components/layouts/mobile-layout.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"rounded-full p-2 hover:bg-muted\",\n                            \"aria-label\": \"Search\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Home_Search_ShoppingBag_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/components/layouts/mobile-layout.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/components/layouts/mobile-layout.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/components/layouts/mobile-layout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Code/khenesis/components/layouts/mobile-layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 pb-16\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Code/khenesis/components/layouts/mobile-layout.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            showNav && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed bottom-0 z-10 w-full border-t bg-background\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid h-16 grid-cols-5\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/\",\n                            className: `flex flex-col items-center justify-center ${pathname === \"/\" ? \"text-primary\" : \"text-muted-foreground\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Home_Search_ShoppingBag_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/components/layouts/mobile-layout.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs\",\n                                    children: \"Home\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/components/layouts/mobile-layout.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/components/layouts/mobile-layout.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/products\",\n                            className: `flex flex-col items-center justify-center ${pathname === \"/products\" || pathname.startsWith(\"/products/\") ? \"text-primary\" : \"text-muted-foreground\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Home_Search_ShoppingBag_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/components/layouts/mobile-layout.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs\",\n                                    children: \"Products\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/components/layouts/mobile-layout.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/components/layouts/mobile-layout.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/orders\",\n                            className: `flex flex-col items-center justify-center ${pathname === \"/orders\" || pathname.startsWith(\"/orders/\") ? \"text-primary\" : \"text-muted-foreground\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"h-5 w-5\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    strokeWidth: \"2\",\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                            x: \"2\",\n                                            y: \"3\",\n                                            width: \"20\",\n                                            height: \"14\",\n                                            rx: \"2\",\n                                            ry: \"2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/components/layouts/mobile-layout.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                            x1: \"8\",\n                                            y1: \"21\",\n                                            x2: \"16\",\n                                            y2: \"21\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/components/layouts/mobile-layout.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                            x1: \"12\",\n                                            y1: \"17\",\n                                            x2: \"12\",\n                                            y2: \"21\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/components/layouts/mobile-layout.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/components/layouts/mobile-layout.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs\",\n                                    children: \"Orders\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/components/layouts/mobile-layout.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/components/layouts/mobile-layout.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/groups\",\n                            className: `flex flex-col items-center justify-center ${pathname === \"/groups\" || pathname.startsWith(\"/groups/\") ? \"text-primary\" : \"text-muted-foreground\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"h-5 w-5\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    strokeWidth: \"2\",\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                            cx: \"9\",\n                                            cy: \"7\",\n                                            r: \"4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/components/layouts/mobile-layout.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M3 21v-2a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/components/layouts/mobile-layout.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                            cx: \"19\",\n                                            cy: \"11\",\n                                            r: \"2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/components/layouts/mobile-layout.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M19 22v-3a2 2 0 0 0-2-2h-1.5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/components/layouts/mobile-layout.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/components/layouts/mobile-layout.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs\",\n                                    children: \"Groups\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/components/layouts/mobile-layout.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/components/layouts/mobile-layout.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/account\",\n                            className: `flex flex-col items-center justify-center ${pathname === \"/account\" || pathname.startsWith(\"/account/\") ? \"text-primary\" : \"text-muted-foreground\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Home_Search_ShoppingBag_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/components/layouts/mobile-layout.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs\",\n                                    children: \"Account\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/components/layouts/mobile-layout.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/components/layouts/mobile-layout.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/components/layouts/mobile-layout.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Code/khenesis/components/layouts/mobile-layout.tsx\",\n                lineNumber: 48,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Code/khenesis/components/layouts/mobile-layout.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/layouts/mobile-layout.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/badge.tsx":
/*!*********************************!*\
  !*** ./components/ui/badge.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Code/khenesis/components/ui/badge.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Code/khenesis/components/ui/button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Code/khenesis/components/ui/card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Code/khenesis/components/ui/card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Code/khenesis/components/ui/card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Code/khenesis/components/ui/card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Code/khenesis/components/ui/card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Code/khenesis/components/ui/card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/dialog.tsx":
/*!**********************************!*\
  !*** ./components/ui/dialog.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Dialog: () => (/* binding */ Dialog),\n/* harmony export */   DialogClose: () => (/* binding */ DialogClose),\n/* harmony export */   DialogContent: () => (/* binding */ DialogContent),\n/* harmony export */   DialogDescription: () => (/* binding */ DialogDescription),\n/* harmony export */   DialogFooter: () => (/* binding */ DialogFooter),\n/* harmony export */   DialogHeader: () => (/* binding */ DialogHeader),\n/* harmony export */   DialogOverlay: () => (/* binding */ DialogOverlay),\n/* harmony export */   DialogPortal: () => (/* binding */ DialogPortal),\n/* harmony export */   DialogTitle: () => (/* binding */ DialogTitle),\n/* harmony export */   DialogTrigger: () => (/* binding */ DialogTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-dialog */ \"(ssr)/./node_modules/@radix-ui/react-dialog/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Dialog,DialogPortal,DialogOverlay,DialogClose,DialogTrigger,DialogContent,DialogHeader,DialogFooter,DialogTitle,DialogDescription auto */ \n\n\n\n\nconst Dialog = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst DialogTrigger = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Trigger;\nconst DialogPortal = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Portal;\nconst DialogClose = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Close;\nconst DialogOverlay = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Overlay, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Code/khenesis/components/ui/dialog.tsx\",\n        lineNumber: 21,\n        columnNumber: 3\n    }, undefined));\nDialogOverlay.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Overlay.displayName;\nconst DialogContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogPortal, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogOverlay, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Code/khenesis/components/ui/dialog.tsx\",\n                lineNumber: 37,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Content, {\n                ref: ref,\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\", className),\n                ...props,\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Close, {\n                        className: \"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/components/ui/dialog.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"sr-only\",\n                                children: \"Close\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/components/ui/dialog.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Code/khenesis/components/ui/dialog.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Code/khenesis/components/ui/dialog.tsx\",\n                lineNumber: 38,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Code/khenesis/components/ui/dialog.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nDialogContent.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\nconst DialogHeader = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 text-center sm:text-left\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Code/khenesis/components/ui/dialog.tsx\",\n        lineNumber: 60,\n        columnNumber: 3\n    }, undefined);\nDialogHeader.displayName = \"DialogHeader\";\nconst DialogFooter = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Code/khenesis/components/ui/dialog.tsx\",\n        lineNumber: 74,\n        columnNumber: 3\n    }, undefined);\nDialogFooter.displayName = \"DialogFooter\";\nconst DialogTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-lg font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Code/khenesis/components/ui/dialog.tsx\",\n        lineNumber: 88,\n        columnNumber: 3\n    }, undefined));\nDialogTitle.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Title.displayName;\nconst DialogDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Code/khenesis/components/ui/dialog.tsx\",\n        lineNumber: 103,\n        columnNumber: 3\n    }, undefined));\nDialogDescription.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/dialog.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/tabs.tsx":
/*!********************************!*\
  !*** ./components/ui/tabs.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tabs: () => (/* binding */ Tabs),\n/* harmony export */   TabsContent: () => (/* binding */ TabsContent),\n/* harmony export */   TabsList: () => (/* binding */ TabsList),\n/* harmony export */   TabsTrigger: () => (/* binding */ TabsTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-tabs */ \"(ssr)/./node_modules/@radix-ui/react-tabs/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Tabs,TabsList,TabsTrigger,TabsContent auto */ \n\n\n\nconst Tabs = _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst TabsList = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.List, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Code/khenesis/components/ui/tabs.tsx\",\n        lineNumber: 14,\n        columnNumber: 3\n    }, undefined));\nTabsList.displayName = _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.List.displayName;\nconst TabsTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Trigger, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Code/khenesis/components/ui/tabs.tsx\",\n        lineNumber: 29,\n        columnNumber: 3\n    }, undefined));\nTabsTrigger.displayName = _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Trigger.displayName;\nconst TabsContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Content, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Code/khenesis/components/ui/tabs.tsx\",\n        lineNumber: 44,\n        columnNumber: 3\n    }, undefined));\nTabsContent.displayName = _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL3RhYnMudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBRStCO0FBQ3VCO0FBRXJCO0FBRWpDLE1BQU1HLE9BQU9GLHNEQUFrQjtBQUUvQixNQUFNSSx5QkFBV0wsNkNBQWdCLENBRy9CLENBQUMsRUFBRU8sU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDUixzREFBa0I7UUFDakJRLEtBQUtBO1FBQ0xGLFdBQVdMLDhDQUFFQSxDQUNYLDhGQUNBSztRQUVELEdBQUdDLEtBQUs7Ozs7OztBQUdiSCxTQUFTTSxXQUFXLEdBQUdWLHNEQUFrQixDQUFDVSxXQUFXO0FBRXJELE1BQU1DLDRCQUFjWiw2Q0FBZ0IsQ0FHbEMsQ0FBQyxFQUFFTyxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNSLHlEQUFxQjtRQUNwQlEsS0FBS0E7UUFDTEYsV0FBV0wsOENBQUVBLENBQ1gsdVlBQ0FLO1FBRUQsR0FBR0MsS0FBSzs7Ozs7O0FBR2JJLFlBQVlELFdBQVcsR0FBR1YseURBQXFCLENBQUNVLFdBQVc7QUFFM0QsTUFBTUcsNEJBQWNkLDZDQUFnQixDQUdsQyxDQUFDLEVBQUVPLFNBQVMsRUFBRSxHQUFHQyxPQUFPLEVBQUVDLG9CQUMxQiw4REFBQ1IseURBQXFCO1FBQ3BCUSxLQUFLQTtRQUNMRixXQUFXTCw4Q0FBRUEsQ0FDWCxtSUFDQUs7UUFFRCxHQUFHQyxLQUFLOzs7Ozs7QUFHYk0sWUFBWUgsV0FBVyxHQUFHVix5REFBcUIsQ0FBQ1UsV0FBVztBQUVQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va2hlbmVzaXMvLi9jb21wb25lbnRzL3VpL3RhYnMudHN4PzgyMWUiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0ICogYXMgVGFic1ByaW1pdGl2ZSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LXRhYnNcIjtcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIjtcblxuY29uc3QgVGFicyA9IFRhYnNQcmltaXRpdmUuUm9vdDtcblxuY29uc3QgVGFic0xpc3QgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBSZWFjdC5FbGVtZW50UmVmPHR5cGVvZiBUYWJzUHJpbWl0aXZlLkxpc3Q+LFxuICBSZWFjdC5Db21wb25lbnRQcm9wc1dpdGhvdXRSZWY8dHlwZW9mIFRhYnNQcmltaXRpdmUuTGlzdD5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPFRhYnNQcmltaXRpdmUuTGlzdFxuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICBcImlubGluZS1mbGV4IGgtMTAgaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHJvdW5kZWQtbWQgYmctbXV0ZWQgcC0xIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiLFxuICAgICAgY2xhc3NOYW1lXG4gICAgKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKTtcblRhYnNMaXN0LmRpc3BsYXlOYW1lID0gVGFic1ByaW1pdGl2ZS5MaXN0LmRpc3BsYXlOYW1lO1xuXG5jb25zdCBUYWJzVHJpZ2dlciA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIFRhYnNQcmltaXRpdmUuVHJpZ2dlcj4sXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgVGFic1ByaW1pdGl2ZS5UcmlnZ2VyPlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8VGFic1ByaW1pdGl2ZS5UcmlnZ2VyXG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihcbiAgICAgIFwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHdoaXRlc3BhY2Utbm93cmFwIHJvdW5kZWQtc20gcHgtMyBweS0xLjUgdGV4dC1zbSBmb250LW1lZGl1bSByaW5nLW9mZnNldC1iYWNrZ3JvdW5kIHRyYW5zaXRpb24tYWxsIGZvY3VzLXZpc2libGU6b3V0bGluZS1ub25lIGZvY3VzLXZpc2libGU6cmluZy0yIGZvY3VzLXZpc2libGU6cmluZy1yaW5nIGZvY3VzLXZpc2libGU6cmluZy1vZmZzZXQtMiBkaXNhYmxlZDpwb2ludGVyLWV2ZW50cy1ub25lIGRpc2FibGVkOm9wYWNpdHktNTAgZGF0YS1bc3RhdGU9YWN0aXZlXTpiZy1iYWNrZ3JvdW5kIGRhdGEtW3N0YXRlPWFjdGl2ZV06dGV4dC1mb3JlZ3JvdW5kIGRhdGEtW3N0YXRlPWFjdGl2ZV06c2hhZG93LXNtXCIsXG4gICAgICBjbGFzc05hbWVcbiAgICApfVxuICAgIHsuLi5wcm9wc31cbiAgLz5cbikpO1xuVGFic1RyaWdnZXIuZGlzcGxheU5hbWUgPSBUYWJzUHJpbWl0aXZlLlRyaWdnZXIuZGlzcGxheU5hbWU7XG5cbmNvbnN0IFRhYnNDb250ZW50ID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgVGFic1ByaW1pdGl2ZS5Db250ZW50PixcbiAgUmVhY3QuQ29tcG9uZW50UHJvcHNXaXRob3V0UmVmPHR5cGVvZiBUYWJzUHJpbWl0aXZlLkNvbnRlbnQ+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxUYWJzUHJpbWl0aXZlLkNvbnRlbnRcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgXCJtdC0yIHJpbmctb2Zmc2V0LWJhY2tncm91bmQgZm9jdXMtdmlzaWJsZTpvdXRsaW5lLW5vbmUgZm9jdXMtdmlzaWJsZTpyaW5nLTIgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcgZm9jdXMtdmlzaWJsZTpyaW5nLW9mZnNldC0yXCIsXG4gICAgICBjbGFzc05hbWVcbiAgICApfVxuICAgIHsuLi5wcm9wc31cbiAgLz5cbikpO1xuVGFic0NvbnRlbnQuZGlzcGxheU5hbWUgPSBUYWJzUHJpbWl0aXZlLkNvbnRlbnQuZGlzcGxheU5hbWU7XG5cbmV4cG9ydCB7IFRhYnMsIFRhYnNMaXN0LCBUYWJzVHJpZ2dlciwgVGFic0NvbnRlbnQgfTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlRhYnNQcmltaXRpdmUiLCJjbiIsIlRhYnMiLCJSb290IiwiVGFic0xpc3QiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJyZWYiLCJMaXN0IiwiZGlzcGxheU5hbWUiLCJUYWJzVHJpZ2dlciIsIlRyaWdnZXIiLCJUYWJzQ29udGVudCIsIkNvbnRlbnQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/tabs.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatPrice: () => (/* binding */ formatPrice),\n/* harmony export */   generateId: () => (/* binding */ generateId)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatPrice(price) {\n    return new Intl.NumberFormat(\"en-US\", {\n        style: \"currency\",\n        currency: \"USD\"\n    }).format(price);\n}\nfunction formatDate(date) {\n    return new Intl.DateTimeFormat(\"en-US\", {\n        day: \"numeric\",\n        month: \"long\",\n        year: \"numeric\"\n    }).format(date);\n}\nfunction generateId() {\n    return Math.random().toString(36).substring(2, 9);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTZDO0FBQ0o7QUFFbEMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCO0FBRU8sU0FBU0MsWUFBWUMsS0FBYTtJQUN2QyxPQUFPLElBQUlDLEtBQUtDLFlBQVksQ0FBQyxTQUFTO1FBQ3BDQyxPQUFPO1FBQ1BDLFVBQVU7SUFDWixHQUFHQyxNQUFNLENBQUNMO0FBQ1o7QUFFTyxTQUFTTSxXQUFXQyxJQUFVO0lBQ25DLE9BQU8sSUFBSU4sS0FBS08sY0FBYyxDQUFDLFNBQVM7UUFDdENDLEtBQUs7UUFDTEMsT0FBTztRQUNQQyxNQUFNO0lBQ1IsR0FBR04sTUFBTSxDQUFDRTtBQUNaO0FBRU8sU0FBU0s7SUFDZCxPQUFPQyxLQUFLQyxNQUFNLEdBQUdDLFFBQVEsQ0FBQyxJQUFJQyxTQUFTLENBQUMsR0FBRztBQUNqRCIsInNvdXJjZXMiOlsid2VicGFjazovL2toZW5lc2lzLy4vbGliL3V0aWxzLnRzP2Y3NDUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBDbGFzc1ZhbHVlLCBjbHN4IH0gZnJvbSBcImNsc3hcIjtcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIjtcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBmb3JtYXRQcmljZShwcmljZTogbnVtYmVyKTogc3RyaW5nIHtcbiAgcmV0dXJuIG5ldyBJbnRsLk51bWJlckZvcm1hdChcImVuLVVTXCIsIHtcbiAgICBzdHlsZTogXCJjdXJyZW5jeVwiLFxuICAgIGN1cnJlbmN5OiBcIlVTRFwiLFxuICB9KS5mb3JtYXQocHJpY2UpO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gZm9ybWF0RGF0ZShkYXRlOiBEYXRlKTogc3RyaW5nIHtcbiAgcmV0dXJuIG5ldyBJbnRsLkRhdGVUaW1lRm9ybWF0KFwiZW4tVVNcIiwge1xuICAgIGRheTogXCJudW1lcmljXCIsXG4gICAgbW9udGg6IFwibG9uZ1wiLFxuICAgIHllYXI6IFwibnVtZXJpY1wiLFxuICB9KS5mb3JtYXQoZGF0ZSk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBnZW5lcmF0ZUlkKCk6IHN0cmluZyB7XG4gIHJldHVybiBNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHJpbmcoMiwgOSk7XG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyIsImZvcm1hdFByaWNlIiwicHJpY2UiLCJJbnRsIiwiTnVtYmVyRm9ybWF0Iiwic3R5bGUiLCJjdXJyZW5jeSIsImZvcm1hdCIsImZvcm1hdERhdGUiLCJkYXRlIiwiRGF0ZVRpbWVGb3JtYXQiLCJkYXkiLCJtb250aCIsInllYXIiLCJnZW5lcmF0ZUlkIiwiTWF0aCIsInJhbmRvbSIsInRvU3RyaW5nIiwic3Vic3RyaW5nIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"a7365fc0daf9\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9raGVuZXNpcy8uL2FwcC9nbG9iYWxzLmNzcz80MDAyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYTczNjVmYzBkYWY5XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/components/ui/toast.tsx":
/*!*************************************!*\
  !*** ./app/components/ui/toast.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ToastProvider: () => (/* binding */ e0),
/* harmony export */   useToast: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Code/khenesis/app/components/ui/toast.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Code/khenesis/app/components/ui/toast.tsx#ToastProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Code/khenesis/app/components/ui/toast.tsx#useToast`);


/***/ }),

/***/ "(rsc)/./app/groups/[id]/page.tsx":
/*!**********************************!*\
  !*** ./app/groups/[id]/page.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   themeColor: () => (/* binding */ themeColor),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./lib/utils.ts\");\n/* harmony import */ var _app_components_ui_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/components/ui/toast */ \"(rsc)/./app/components/ui/toast.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"Khenesis - Collaborative Shopping Platform\",\n    description: \"Group purchasing with installment payments and manufacturing visibility | Shop together, save together\"\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1,\n    maximumScale: 1,\n    userScalable: false,\n    viewportFit: \"cover\"\n};\nconst themeColor = [\n    {\n        media: \"(prefers-color-scheme: light)\",\n        color: \"white\"\n    },\n    {\n        media: \"(prefers-color-scheme: dark)\",\n        color: \"black\"\n    }\n];\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)((next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className), \"min-h-full flex flex-col bg-background text-foreground antialiased\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_ui_toast__WEBPACK_IMPORTED_MODULE_3__.ToastProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/layout.tsx\",\n                lineNumber: 41,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Code/khenesis/app/layout.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Code/khenesis/app/layout.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatPrice: () => (/* binding */ formatPrice),\n/* harmony export */   generateId: () => (/* binding */ generateId)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatPrice(price) {\n    return new Intl.NumberFormat(\"en-US\", {\n        style: \"currency\",\n        currency: \"USD\"\n    }).format(price);\n}\nfunction formatDate(date) {\n    return new Intl.DateTimeFormat(\"en-US\", {\n        day: \"numeric\",\n        month: \"long\",\n        year: \"numeric\"\n    }).format(date);\n}\nfunction generateId() {\n    return Math.random().toString(36).substring(2, 9);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTZDO0FBQ0o7QUFFbEMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCO0FBRU8sU0FBU0MsWUFBWUMsS0FBYTtJQUN2QyxPQUFPLElBQUlDLEtBQUtDLFlBQVksQ0FBQyxTQUFTO1FBQ3BDQyxPQUFPO1FBQ1BDLFVBQVU7SUFDWixHQUFHQyxNQUFNLENBQUNMO0FBQ1o7QUFFTyxTQUFTTSxXQUFXQyxJQUFVO0lBQ25DLE9BQU8sSUFBSU4sS0FBS08sY0FBYyxDQUFDLFNBQVM7UUFDdENDLEtBQUs7UUFDTEMsT0FBTztRQUNQQyxNQUFNO0lBQ1IsR0FBR04sTUFBTSxDQUFDRTtBQUNaO0FBRU8sU0FBU0s7SUFDZCxPQUFPQyxLQUFLQyxNQUFNLEdBQUdDLFFBQVEsQ0FBQyxJQUFJQyxTQUFTLENBQUMsR0FBRztBQUNqRCIsInNvdXJjZXMiOlsid2VicGFjazovL2toZW5lc2lzLy4vbGliL3V0aWxzLnRzP2Y3NDUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBDbGFzc1ZhbHVlLCBjbHN4IH0gZnJvbSBcImNsc3hcIjtcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIjtcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBmb3JtYXRQcmljZShwcmljZTogbnVtYmVyKTogc3RyaW5nIHtcbiAgcmV0dXJuIG5ldyBJbnRsLk51bWJlckZvcm1hdChcImVuLVVTXCIsIHtcbiAgICBzdHlsZTogXCJjdXJyZW5jeVwiLFxuICAgIGN1cnJlbmN5OiBcIlVTRFwiLFxuICB9KS5mb3JtYXQocHJpY2UpO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gZm9ybWF0RGF0ZShkYXRlOiBEYXRlKTogc3RyaW5nIHtcbiAgcmV0dXJuIG5ldyBJbnRsLkRhdGVUaW1lRm9ybWF0KFwiZW4tVVNcIiwge1xuICAgIGRheTogXCJudW1lcmljXCIsXG4gICAgbW9udGg6IFwibG9uZ1wiLFxuICAgIHllYXI6IFwibnVtZXJpY1wiLFxuICB9KS5mb3JtYXQoZGF0ZSk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBnZW5lcmF0ZUlkKCk6IHN0cmluZyB7XG4gIHJldHVybiBNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHJpbmcoMiwgOSk7XG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyIsImZvcm1hdFByaWNlIiwicHJpY2UiLCJJbnRsIiwiTnVtYmVyRm9ybWF0Iiwic3R5bGUiLCJjdXJyZW5jeSIsImZvcm1hdCIsImZvcm1hdERhdGUiLCJkYXRlIiwiRGF0ZVRpbWVGb3JtYXQiLCJkYXkiLCJtb250aCIsInllYXIiLCJnZW5lcmF0ZUlkIiwiTWF0aCIsInJhbmRvbSIsInRvU3RyaW5nIiwic3Vic3RyaW5nIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./lib/utils.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/@swc","vendor-chunks/clsx","vendor-chunks/@radix-ui","vendor-chunks/class-variance-authority","vendor-chunks/react-remove-scroll","vendor-chunks/tslib","vendor-chunks/aria-hidden","vendor-chunks/react-remove-scroll-bar","vendor-chunks/use-callback-ref","vendor-chunks/use-sidecar","vendor-chunks/react-style-singleton","vendor-chunks/get-nonce"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fgroups%2F%5Bid%5D%2Fpage&page=%2Fgroups%2F%5Bid%5D%2Fpage&appPaths=%2Fgroups%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fgroups%2F%5Bid%5D%2Fpage.tsx&appDir=%2FUsers%2Fhlogi%2FCode%2Fkhenesis%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fhlogi%2FCode%2Fkhenesis&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();