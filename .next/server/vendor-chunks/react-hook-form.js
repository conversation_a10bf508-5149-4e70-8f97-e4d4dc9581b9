"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-hook-form";
exports.ids = ["vendor-chunks/react-hook-form"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/react-hook-form/dist/index.esm.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Controller: () => (/* binding */ Controller),\n/* harmony export */   Form: () => (/* binding */ Form),\n/* harmony export */   FormProvider: () => (/* binding */ FormProvider),\n/* harmony export */   appendErrors: () => (/* binding */ appendErrors),\n/* harmony export */   get: () => (/* binding */ get),\n/* harmony export */   set: () => (/* binding */ set),\n/* harmony export */   useController: () => (/* binding */ useController),\n/* harmony export */   useFieldArray: () => (/* binding */ useFieldArray),\n/* harmony export */   useForm: () => (/* binding */ useForm),\n/* harmony export */   useFormContext: () => (/* binding */ useFormContext),\n/* harmony export */   useFormState: () => (/* binding */ useFormState),\n/* harmony export */   useWatch: () => (/* binding */ useWatch)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\nvar isCheckBoxInput = (element)=>element.type === \"checkbox\";\nvar isDateObject = (value1)=>value1 instanceof Date;\nvar isNullOrUndefined = (value1)=>value1 == null;\nconst isObjectType = (value1)=>typeof value1 === \"object\";\nvar isObject = (value1)=>!isNullOrUndefined(value1) && !Array.isArray(value1) && isObjectType(value1) && !isDateObject(value1);\nvar getEventValue = (event)=>isObject(event) && event.target ? isCheckBoxInput(event.target) ? event.target.checked : event.target.value : event;\nvar getNodeParentName = (name)=>name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\nvar isNameInFieldArray = (names, name)=>names.has(getNodeParentName(name));\nvar isPlainObject = (tempObject)=>{\n    const prototypeCopy = tempObject.constructor && tempObject.constructor.prototype;\n    return isObject(prototypeCopy) && prototypeCopy.hasOwnProperty(\"isPrototypeOf\");\n};\nvar isWeb =  false && 0;\nfunction cloneObject(data) {\n    let copy;\n    const isArray = Array.isArray(data);\n    const isFileListInstance = typeof FileList !== \"undefined\" ? data instanceof FileList : false;\n    if (data instanceof Date) {\n        copy = new Date(data);\n    } else if (data instanceof Set) {\n        copy = new Set(data);\n    } else if (!(isWeb && (data instanceof Blob || isFileListInstance)) && (isArray || isObject(data))) {\n        copy = isArray ? [] : {};\n        if (!isArray && !isPlainObject(data)) {\n            copy = data;\n        } else {\n            for(const key in data){\n                if (data.hasOwnProperty(key)) {\n                    copy[key] = cloneObject(data[key]);\n                }\n            }\n        }\n    } else {\n        return data;\n    }\n    return copy;\n}\nvar compact = (value1)=>Array.isArray(value1) ? value1.filter(Boolean) : [];\nvar isUndefined = (val)=>val === undefined;\nvar get = (object, path, defaultValue)=>{\n    if (!path || !isObject(object)) {\n        return defaultValue;\n    }\n    const result = compact(path.split(/[,[\\].]+?/)).reduce((result, key)=>isNullOrUndefined(result) ? result : result[key], object);\n    return isUndefined(result) || result === object ? isUndefined(object[path]) ? defaultValue : object[path] : result;\n};\nvar isBoolean = (value1)=>typeof value1 === \"boolean\";\nvar isKey = (value1)=>/^\\w*$/.test(value1);\nvar stringToPath = (input)=>compact(input.replace(/[\"|']|\\]/g, \"\").split(/\\.|\\[/));\nvar set = (object, path, value1)=>{\n    let index = -1;\n    const tempPath = isKey(path) ? [\n        path\n    ] : stringToPath(path);\n    const length = tempPath.length;\n    const lastIndex = length - 1;\n    while(++index < length){\n        const key = tempPath[index];\n        let newValue = value1;\n        if (index !== lastIndex) {\n            const objValue = object[key];\n            newValue = isObject(objValue) || Array.isArray(objValue) ? objValue : !isNaN(+tempPath[index + 1]) ? [] : {};\n        }\n        if (key === \"__proto__\" || key === \"constructor\" || key === \"prototype\") {\n            return;\n        }\n        object[key] = newValue;\n        object = object[key];\n    }\n    return object;\n};\nconst EVENTS = {\n    BLUR: \"blur\",\n    FOCUS_OUT: \"focusout\",\n    CHANGE: \"change\"\n};\nconst VALIDATION_MODE = {\n    onBlur: \"onBlur\",\n    onChange: \"onChange\",\n    onSubmit: \"onSubmit\",\n    onTouched: \"onTouched\",\n    all: \"all\"\n};\nconst INPUT_VALIDATION_RULES = {\n    max: \"max\",\n    min: \"min\",\n    maxLength: \"maxLength\",\n    minLength: \"minLength\",\n    pattern: \"pattern\",\n    required: \"required\",\n    validate: \"validate\"\n};\nconst HookFormContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */ const useFormContext = ()=>react__WEBPACK_IMPORTED_MODULE_0__.useContext(HookFormContext);\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */ const FormProvider = (props)=>{\n    const { children, ...data } = props;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(HookFormContext.Provider, {\n        value: data\n    }, children);\n};\nvar getProxyFormState = (formState, control, localProxyFormState, isRoot = true)=>{\n    const result = {\n        defaultValues: control._defaultValues\n    };\n    for(const key in formState){\n        Object.defineProperty(result, key, {\n            get: ()=>{\n                const _key = key;\n                if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n                    control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n                }\n                localProxyFormState && (localProxyFormState[_key] = true);\n                return formState[_key];\n            }\n        });\n    }\n    return result;\n};\nvar isEmptyObject = (value1)=>isObject(value1) && !Object.keys(value1).length;\nvar shouldRenderFormState = (formStateData, _proxyFormState, updateFormState, isRoot)=>{\n    updateFormState(formStateData);\n    const { name, ...formState } = formStateData;\n    return isEmptyObject(formState) || Object.keys(formState).length >= Object.keys(_proxyFormState).length || Object.keys(formState).find((key)=>_proxyFormState[key] === (!isRoot || VALIDATION_MODE.all));\n};\nvar convertToArrayPayload = (value1)=>Array.isArray(value1) ? value1 : [\n        value1\n    ];\nvar shouldSubscribeByName = (name, signalName, exact)=>!name || !signalName || name === signalName || convertToArrayPayload(name).some((currentName)=>currentName && (exact ? currentName === signalName : currentName.startsWith(signalName) || signalName.startsWith(currentName)));\nfunction useSubscribe(props) {\n    const _props = react__WEBPACK_IMPORTED_MODULE_0__.useRef(props);\n    _props.current = props;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const subscription = !props.disabled && _props.current.subject && _props.current.subject.subscribe({\n            next: _props.current.next\n        });\n        return ()=>{\n            subscription && subscription.unsubscribe();\n        };\n    }, [\n        props.disabled\n    ]);\n}\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */ function useFormState(props) {\n    const methods = useFormContext();\n    const { control = methods.control, disabled, name, exact } = props || {};\n    const [formState, updateFormState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._formState);\n    const _mounted = react__WEBPACK_IMPORTED_MODULE_0__.useRef(true);\n    const _localProxyFormState = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n        isDirty: false,\n        isLoading: false,\n        dirtyFields: false,\n        touchedFields: false,\n        validatingFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false\n    });\n    const _name = react__WEBPACK_IMPORTED_MODULE_0__.useRef(name);\n    _name.current = name;\n    useSubscribe({\n        disabled,\n        next: (value1)=>_mounted.current && shouldSubscribeByName(_name.current, value1.name, exact) && shouldRenderFormState(value1, _localProxyFormState.current, control._updateFormState) && updateFormState({\n                ...control._formState,\n                ...value1\n            }),\n        subject: control._subjects.state\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        _mounted.current = true;\n        _localProxyFormState.current.isValid && control._updateValid(true);\n        return ()=>{\n            _mounted.current = false;\n        };\n    }, [\n        control\n    ]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>getProxyFormState(formState, control, _localProxyFormState.current, false), [\n        formState,\n        control\n    ]);\n}\nvar isString = (value1)=>typeof value1 === \"string\";\nvar generateWatchOutput = (names, _names, formValues, isGlobal, defaultValue)=>{\n    if (isString(names)) {\n        isGlobal && _names.watch.add(names);\n        return get(formValues, names, defaultValue);\n    }\n    if (Array.isArray(names)) {\n        return names.map((fieldName)=>(isGlobal && _names.watch.add(fieldName), get(formValues, fieldName)));\n    }\n    isGlobal && (_names.watchAll = true);\n    return formValues;\n};\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */ function useWatch(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, defaultValue, disabled, exact } = props || {};\n    const _name = react__WEBPACK_IMPORTED_MODULE_0__.useRef(name);\n    _name.current = name;\n    useSubscribe({\n        disabled,\n        subject: control._subjects.values,\n        next: (formState)=>{\n            if (shouldSubscribeByName(_name.current, formState.name, exact)) {\n                updateValue(cloneObject(generateWatchOutput(_name.current, control._names, formState.values || control._formValues, false, defaultValue)));\n            }\n        }\n    });\n    const [value1, updateValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._getWatch(name, defaultValue));\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>control._removeUnmounted());\n    return value1;\n}\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */ function useController(props) {\n    const methods = useFormContext();\n    const { name, disabled, control = methods.control, shouldUnregister } = props;\n    const isArrayField = isNameInFieldArray(control._names.array, name);\n    const value1 = useWatch({\n        control,\n        name,\n        defaultValue: get(control._formValues, name, get(control._defaultValues, name, props.defaultValue)),\n        exact: true\n    });\n    const formState = useFormState({\n        control,\n        name,\n        exact: true\n    });\n    const _registerProps = react__WEBPACK_IMPORTED_MODULE_0__.useRef(control.register(name, {\n        ...props.rules,\n        value: value1,\n        ...isBoolean(props.disabled) ? {\n            disabled: props.disabled\n        } : {}\n    }));\n    const fieldState = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>Object.defineProperties({}, {\n            invalid: {\n                enumerable: true,\n                get: ()=>!!get(formState.errors, name)\n            },\n            isDirty: {\n                enumerable: true,\n                get: ()=>!!get(formState.dirtyFields, name)\n            },\n            isTouched: {\n                enumerable: true,\n                get: ()=>!!get(formState.touchedFields, name)\n            },\n            isValidating: {\n                enumerable: true,\n                get: ()=>!!get(formState.validatingFields, name)\n            },\n            error: {\n                enumerable: true,\n                get: ()=>get(formState.errors, name)\n            }\n        }), [\n        formState,\n        name\n    ]);\n    const field = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n            name,\n            value: value1,\n            ...isBoolean(disabled) || formState.disabled ? {\n                disabled: formState.disabled || disabled\n            } : {},\n            onChange: (event)=>_registerProps.current.onChange({\n                    target: {\n                        value: getEventValue(event),\n                        name: name\n                    },\n                    type: EVENTS.CHANGE\n                }),\n            onBlur: ()=>_registerProps.current.onBlur({\n                    target: {\n                        value: get(control._formValues, name),\n                        name: name\n                    },\n                    type: EVENTS.BLUR\n                }),\n            ref: (elm)=>{\n                const field = get(control._fields, name);\n                if (field && elm) {\n                    field._f.ref = {\n                        focus: ()=>elm.focus(),\n                        select: ()=>elm.select(),\n                        setCustomValidity: (message)=>elm.setCustomValidity(message),\n                        reportValidity: ()=>elm.reportValidity()\n                    };\n                }\n            }\n        }), [\n        name,\n        control._formValues,\n        disabled,\n        formState.disabled,\n        value1,\n        control._fields\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const _shouldUnregisterField = control._options.shouldUnregister || shouldUnregister;\n        const updateMounted = (name, value1)=>{\n            const field = get(control._fields, name);\n            if (field && field._f) {\n                field._f.mount = value1;\n            }\n        };\n        updateMounted(name, true);\n        if (_shouldUnregisterField) {\n            const value1 = cloneObject(get(control._options.defaultValues, name));\n            set(control._defaultValues, name, value1);\n            if (isUndefined(get(control._formValues, name))) {\n                set(control._formValues, name, value1);\n            }\n        }\n        !isArrayField && control.register(name);\n        return ()=>{\n            (isArrayField ? _shouldUnregisterField && !control._state.action : _shouldUnregisterField) ? control.unregister(name) : updateMounted(name, false);\n        };\n    }, [\n        name,\n        control,\n        isArrayField,\n        shouldUnregister\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        control._updateDisabledField({\n            disabled,\n            fields: control._fields,\n            name\n        });\n    }, [\n        disabled,\n        name,\n        control\n    ]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n            field,\n            formState,\n            fieldState\n        }), [\n        field,\n        formState,\n        fieldState\n    ]);\n}\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */ const Controller = (props)=>props.render(useController(props));\nconst flatten = (obj)=>{\n    const output = {};\n    for (const key of Object.keys(obj)){\n        if (isObjectType(obj[key]) && obj[key] !== null) {\n            const nested = flatten(obj[key]);\n            for (const nestedKey of Object.keys(nested)){\n                output[`${key}.${nestedKey}`] = nested[nestedKey];\n            }\n        } else {\n            output[key] = obj[key];\n        }\n    }\n    return output;\n};\nconst POST_REQUEST = \"post\";\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */ function Form(props) {\n    const methods = useFormContext();\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const { control = methods.control, onSubmit, children, action, method = POST_REQUEST, headers, encType, onError, render, onSuccess, validateStatus, ...rest } = props;\n    const submit = async (event)=>{\n        let hasError = false;\n        let type = \"\";\n        await control.handleSubmit(async (data)=>{\n            const formData = new FormData();\n            let formDataJson = \"\";\n            try {\n                formDataJson = JSON.stringify(data);\n            } catch (_a) {}\n            const flattenFormValues = flatten(control._formValues);\n            for(const key in flattenFormValues){\n                formData.append(key, flattenFormValues[key]);\n            }\n            if (onSubmit) {\n                await onSubmit({\n                    data,\n                    event,\n                    method,\n                    formData,\n                    formDataJson\n                });\n            }\n            if (action) {\n                try {\n                    const shouldStringifySubmissionData = [\n                        headers && headers[\"Content-Type\"],\n                        encType\n                    ].some((value1)=>value1 && value1.includes(\"json\"));\n                    const response = await fetch(String(action), {\n                        method,\n                        headers: {\n                            ...headers,\n                            ...encType ? {\n                                \"Content-Type\": encType\n                            } : {}\n                        },\n                        body: shouldStringifySubmissionData ? formDataJson : formData\n                    });\n                    if (response && (validateStatus ? !validateStatus(response.status) : response.status < 200 || response.status >= 300)) {\n                        hasError = true;\n                        onError && onError({\n                            response\n                        });\n                        type = String(response.status);\n                    } else {\n                        onSuccess && onSuccess({\n                            response\n                        });\n                    }\n                } catch (error) {\n                    hasError = true;\n                    onError && onError({\n                        error\n                    });\n                }\n            }\n        })(event);\n        if (hasError && props.control) {\n            props.control._subjects.state.next({\n                isSubmitSuccessful: false\n            });\n            props.control.setError(\"root.server\", {\n                type\n            });\n        }\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        setMounted(true);\n    }, []);\n    return render ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, render({\n        submit\n    })) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"form\", {\n        noValidate: mounted,\n        action: action,\n        method: method,\n        encType: encType,\n        onSubmit: submit,\n        ...rest\n    }, children);\n}\nvar appendErrors = (name, validateAllFieldCriteria, errors, type, message)=>validateAllFieldCriteria ? {\n        ...errors[name],\n        types: {\n            ...errors[name] && errors[name].types ? errors[name].types : {},\n            [type]: message || true\n        }\n    } : {};\nvar generateId = ()=>{\n    const d = typeof performance === \"undefined\" ? Date.now() : performance.now() * 1000;\n    return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/g, (c)=>{\n        const r = (Math.random() * 16 + d) % 16 | 0;\n        return (c == \"x\" ? r : r & 0x3 | 0x8).toString(16);\n    });\n};\nvar getFocusFieldName = (name, index, options = {})=>options.shouldFocus || isUndefined(options.shouldFocus) ? options.focusName || `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.` : \"\";\nvar getValidationModes = (mode)=>({\n        isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n        isOnBlur: mode === VALIDATION_MODE.onBlur,\n        isOnChange: mode === VALIDATION_MODE.onChange,\n        isOnAll: mode === VALIDATION_MODE.all,\n        isOnTouch: mode === VALIDATION_MODE.onTouched\n    });\nvar isWatched = (name, _names, isBlurEvent)=>!isBlurEvent && (_names.watchAll || _names.watch.has(name) || [\n        ..._names.watch\n    ].some((watchName)=>name.startsWith(watchName) && /^\\.\\w+/.test(name.slice(watchName.length))));\nconst iterateFieldsByAction = (fields, action, fieldsNames, abortEarly)=>{\n    for (const key of fieldsNames || Object.keys(fields)){\n        const field = get(fields, key);\n        if (field) {\n            const { _f, ...currentField } = field;\n            if (_f) {\n                if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n                    return true;\n                } else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n                    return true;\n                } else {\n                    if (iterateFieldsByAction(currentField, action)) {\n                        break;\n                    }\n                }\n            } else if (isObject(currentField)) {\n                if (iterateFieldsByAction(currentField, action)) {\n                    break;\n                }\n            }\n        }\n    }\n    return;\n};\nvar updateFieldArrayRootError = (errors, error, name)=>{\n    const fieldArrayErrors = convertToArrayPayload(get(errors, name));\n    set(fieldArrayErrors, \"root\", error[name]);\n    set(errors, name, fieldArrayErrors);\n    return errors;\n};\nvar isFileInput = (element)=>element.type === \"file\";\nvar isFunction = (value1)=>typeof value1 === \"function\";\nvar isHTMLElement = (value1)=>{\n    if (!isWeb) {\n        return false;\n    }\n    const owner = value1 ? value1.ownerDocument : 0;\n    return value1 instanceof (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement);\n};\nvar isMessage = (value1)=>isString(value1);\nvar isRadioInput = (element)=>element.type === \"radio\";\nvar isRegex = (value1)=>value1 instanceof RegExp;\nconst defaultResult = {\n    value: false,\n    isValid: false\n};\nconst validResult = {\n    value: true,\n    isValid: true\n};\nvar getCheckboxValue = (options)=>{\n    if (Array.isArray(options)) {\n        if (options.length > 1) {\n            const values = options.filter((option)=>option && option.checked && !option.disabled).map((option)=>option.value);\n            return {\n                value: values,\n                isValid: !!values.length\n            };\n        }\n        return options[0].checked && !options[0].disabled ? options[0].attributes && !isUndefined(options[0].attributes.value) ? isUndefined(options[0].value) || options[0].value === \"\" ? validResult : {\n            value: options[0].value,\n            isValid: true\n        } : validResult : defaultResult;\n    }\n    return defaultResult;\n};\nconst defaultReturn = {\n    isValid: false,\n    value: null\n};\nvar getRadioValue = (options)=>Array.isArray(options) ? options.reduce((previous, option)=>option && option.checked && !option.disabled ? {\n            isValid: true,\n            value: option.value\n        } : previous, defaultReturn) : defaultReturn;\nfunction getValidateError(result, ref, type = \"validate\") {\n    if (isMessage(result) || Array.isArray(result) && result.every(isMessage) || isBoolean(result) && !result) {\n        return {\n            type,\n            message: isMessage(result) ? result : \"\",\n            ref\n        };\n    }\n}\nvar getValueAndMessage = (validationData)=>isObject(validationData) && !isRegex(validationData) ? validationData : {\n        value: validationData,\n        message: \"\"\n    };\nvar validateField = async (field, disabledFieldNames, formValues, validateAllFieldCriteria, shouldUseNativeValidation, isFieldArray)=>{\n    const { ref, refs, required, maxLength, minLength, min, max, pattern, validate, name, valueAsNumber, mount } = field._f;\n    const inputValue = get(formValues, name);\n    if (!mount || disabledFieldNames.has(name)) {\n        return {};\n    }\n    const inputRef = refs ? refs[0] : ref;\n    const setCustomValidity = (message)=>{\n        if (shouldUseNativeValidation && inputRef.reportValidity) {\n            inputRef.setCustomValidity(isBoolean(message) ? \"\" : message || \"\");\n            inputRef.reportValidity();\n        }\n    };\n    const error = {};\n    const isRadio = isRadioInput(ref);\n    const isCheckBox = isCheckBoxInput(ref);\n    const isRadioOrCheckbox = isRadio || isCheckBox;\n    const isEmpty = (valueAsNumber || isFileInput(ref)) && isUndefined(ref.value) && isUndefined(inputValue) || isHTMLElement(ref) && ref.value === \"\" || inputValue === \"\" || Array.isArray(inputValue) && !inputValue.length;\n    const appendErrorsCurry = appendErrors.bind(null, name, validateAllFieldCriteria, error);\n    const getMinMaxMessage = (exceedMax, maxLengthMessage, minLengthMessage, maxType = INPUT_VALIDATION_RULES.maxLength, minType = INPUT_VALIDATION_RULES.minLength)=>{\n        const message = exceedMax ? maxLengthMessage : minLengthMessage;\n        error[name] = {\n            type: exceedMax ? maxType : minType,\n            message,\n            ref,\n            ...appendErrorsCurry(exceedMax ? maxType : minType, message)\n        };\n    };\n    if (isFieldArray ? !Array.isArray(inputValue) || !inputValue.length : required && (!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue)) || isBoolean(inputValue) && !inputValue || isCheckBox && !getCheckboxValue(refs).isValid || isRadio && !getRadioValue(refs).isValid)) {\n        const { value: value1, message } = isMessage(required) ? {\n            value: !!required,\n            message: required\n        } : getValueAndMessage(required);\n        if (value1) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.required,\n                message,\n                ref: inputRef,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message)\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n        let exceedMax;\n        let exceedMin;\n        const maxOutput = getValueAndMessage(max);\n        const minOutput = getValueAndMessage(min);\n        if (!isNullOrUndefined(inputValue) && !isNaN(inputValue)) {\n            const valueNumber = ref.valueAsNumber || (inputValue ? +inputValue : inputValue);\n            if (!isNullOrUndefined(maxOutput.value)) {\n                exceedMax = valueNumber > maxOutput.value;\n            }\n            if (!isNullOrUndefined(minOutput.value)) {\n                exceedMin = valueNumber < minOutput.value;\n            }\n        } else {\n            const valueDate = ref.valueAsDate || new Date(inputValue);\n            const convertTimeToDate = (time)=>new Date(new Date().toDateString() + \" \" + time);\n            const isTime = ref.type == \"time\";\n            const isWeek = ref.type == \"week\";\n            if (isString(maxOutput.value) && inputValue) {\n                exceedMax = isTime ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value) : isWeek ? inputValue > maxOutput.value : valueDate > new Date(maxOutput.value);\n            }\n            if (isString(minOutput.value) && inputValue) {\n                exceedMin = isTime ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value) : isWeek ? inputValue < minOutput.value : valueDate < new Date(minOutput.value);\n            }\n        }\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(!!exceedMax, maxOutput.message, minOutput.message, INPUT_VALIDATION_RULES.max, INPUT_VALIDATION_RULES.min);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if ((maxLength || minLength) && !isEmpty && (isString(inputValue) || isFieldArray && Array.isArray(inputValue))) {\n        const maxLengthOutput = getValueAndMessage(maxLength);\n        const minLengthOutput = getValueAndMessage(minLength);\n        const exceedMax = !isNullOrUndefined(maxLengthOutput.value) && inputValue.length > +maxLengthOutput.value;\n        const exceedMin = !isNullOrUndefined(minLengthOutput.value) && inputValue.length < +minLengthOutput.value;\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(exceedMax, maxLengthOutput.message, minLengthOutput.message);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if (pattern && !isEmpty && isString(inputValue)) {\n        const { value: patternValue, message } = getValueAndMessage(pattern);\n        if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.pattern,\n                message,\n                ref,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message)\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (validate) {\n        if (isFunction(validate)) {\n            const result = await validate(inputValue, formValues);\n            const validateError = getValidateError(result, inputRef);\n            if (validateError) {\n                error[name] = {\n                    ...validateError,\n                    ...appendErrorsCurry(INPUT_VALIDATION_RULES.validate, validateError.message)\n                };\n                if (!validateAllFieldCriteria) {\n                    setCustomValidity(validateError.message);\n                    return error;\n                }\n            }\n        } else if (isObject(validate)) {\n            let validationResult = {};\n            for(const key in validate){\n                if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n                    break;\n                }\n                const validateError = getValidateError(await validate[key](inputValue, formValues), inputRef, key);\n                if (validateError) {\n                    validationResult = {\n                        ...validateError,\n                        ...appendErrorsCurry(key, validateError.message)\n                    };\n                    setCustomValidity(validateError.message);\n                    if (validateAllFieldCriteria) {\n                        error[name] = validationResult;\n                    }\n                }\n            }\n            if (!isEmptyObject(validationResult)) {\n                error[name] = {\n                    ref: inputRef,\n                    ...validationResult\n                };\n                if (!validateAllFieldCriteria) {\n                    return error;\n                }\n            }\n        }\n    }\n    setCustomValidity(true);\n    return error;\n};\nvar appendAt = (data, value1)=>[\n        ...data,\n        ...convertToArrayPayload(value1)\n    ];\nvar fillEmptyArray = (value1)=>Array.isArray(value1) ? value1.map(()=>undefined) : undefined;\nfunction insert(data, index, value1) {\n    return [\n        ...data.slice(0, index),\n        ...convertToArrayPayload(value1),\n        ...data.slice(index)\n    ];\n}\nvar moveArrayAt = (data, from, to)=>{\n    if (!Array.isArray(data)) {\n        return [];\n    }\n    if (isUndefined(data[to])) {\n        data[to] = undefined;\n    }\n    data.splice(to, 0, data.splice(from, 1)[0]);\n    return data;\n};\nvar prependAt = (data, value1)=>[\n        ...convertToArrayPayload(value1),\n        ...convertToArrayPayload(data)\n    ];\nfunction removeAtIndexes(data, indexes) {\n    let i = 0;\n    const temp = [\n        ...data\n    ];\n    for (const index of indexes){\n        temp.splice(index - i, 1);\n        i++;\n    }\n    return compact(temp).length ? temp : [];\n}\nvar removeArrayAt = (data, index)=>isUndefined(index) ? [] : removeAtIndexes(data, convertToArrayPayload(index).sort((a, b)=>a - b));\nvar swapArrayAt = (data, indexA, indexB)=>{\n    [data[indexA], data[indexB]] = [\n        data[indexB],\n        data[indexA]\n    ];\n};\nfunction baseGet(object, updatePath) {\n    const length = updatePath.slice(0, -1).length;\n    let index = 0;\n    while(index < length){\n        object = isUndefined(object) ? index++ : object[updatePath[index++]];\n    }\n    return object;\n}\nfunction isEmptyArray(obj) {\n    for(const key in obj){\n        if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction unset(object, path) {\n    const paths = Array.isArray(path) ? path : isKey(path) ? [\n        path\n    ] : stringToPath(path);\n    const childObject = paths.length === 1 ? object : baseGet(object, paths);\n    const index = paths.length - 1;\n    const key = paths[index];\n    if (childObject) {\n        delete childObject[key];\n    }\n    if (index !== 0 && (isObject(childObject) && isEmptyObject(childObject) || Array.isArray(childObject) && isEmptyArray(childObject))) {\n        unset(object, paths.slice(0, -1));\n    }\n    return object;\n}\nvar updateAt = (fieldValues, index, value1)=>{\n    fieldValues[index] = value1;\n    return fieldValues;\n};\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */ function useFieldArray(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, keyName = \"id\", shouldUnregister, rules } = props;\n    const [fields, setFields] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._getFieldArray(name));\n    const ids = react__WEBPACK_IMPORTED_MODULE_0__.useRef(control._getFieldArray(name).map(generateId));\n    const _fieldIds = react__WEBPACK_IMPORTED_MODULE_0__.useRef(fields);\n    const _name = react__WEBPACK_IMPORTED_MODULE_0__.useRef(name);\n    const _actioned = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    _name.current = name;\n    _fieldIds.current = fields;\n    control._names.array.add(name);\n    rules && control.register(name, rules);\n    useSubscribe({\n        next: ({ values, name: fieldArrayName })=>{\n            if (fieldArrayName === _name.current || !fieldArrayName) {\n                const fieldValues = get(values, _name.current);\n                if (Array.isArray(fieldValues)) {\n                    setFields(fieldValues);\n                    ids.current = fieldValues.map(generateId);\n                }\n            }\n        },\n        subject: control._subjects.array\n    });\n    const updateValues = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((updatedFieldArrayValues)=>{\n        _actioned.current = true;\n        control._updateFieldArray(name, updatedFieldArrayValues);\n    }, [\n        control,\n        name\n    ]);\n    const append = (value1, options)=>{\n        const appendValue = convertToArrayPayload(cloneObject(value1));\n        const updatedFieldArrayValues = appendAt(control._getFieldArray(name), appendValue);\n        control._names.focus = getFocusFieldName(name, updatedFieldArrayValues.length - 1, options);\n        ids.current = appendAt(ids.current, appendValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._updateFieldArray(name, updatedFieldArrayValues, appendAt, {\n            argA: fillEmptyArray(value1)\n        });\n    };\n    const prepend = (value1, options)=>{\n        const prependValue = convertToArrayPayload(cloneObject(value1));\n        const updatedFieldArrayValues = prependAt(control._getFieldArray(name), prependValue);\n        control._names.focus = getFocusFieldName(name, 0, options);\n        ids.current = prependAt(ids.current, prependValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._updateFieldArray(name, updatedFieldArrayValues, prependAt, {\n            argA: fillEmptyArray(value1)\n        });\n    };\n    const remove = (index)=>{\n        const updatedFieldArrayValues = removeArrayAt(control._getFieldArray(name), index);\n        ids.current = removeArrayAt(ids.current, index);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        !Array.isArray(get(control._fields, name)) && set(control._fields, name, undefined);\n        control._updateFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n            argA: index\n        });\n    };\n    const insert$1 = (index, value1, options)=>{\n        const insertValue = convertToArrayPayload(cloneObject(value1));\n        const updatedFieldArrayValues = insert(control._getFieldArray(name), index, insertValue);\n        control._names.focus = getFocusFieldName(name, index, options);\n        ids.current = insert(ids.current, index, insertValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._updateFieldArray(name, updatedFieldArrayValues, insert, {\n            argA: index,\n            argB: fillEmptyArray(value1)\n        });\n    };\n    const swap = (indexA, indexB)=>{\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n        swapArrayAt(ids.current, indexA, indexB);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._updateFieldArray(name, updatedFieldArrayValues, swapArrayAt, {\n            argA: indexA,\n            argB: indexB\n        }, false);\n    };\n    const move = (from, to)=>{\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        moveArrayAt(updatedFieldArrayValues, from, to);\n        moveArrayAt(ids.current, from, to);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._updateFieldArray(name, updatedFieldArrayValues, moveArrayAt, {\n            argA: from,\n            argB: to\n        }, false);\n    };\n    const update = (index, value1)=>{\n        const updateValue = cloneObject(value1);\n        const updatedFieldArrayValues = updateAt(control._getFieldArray(name), index, updateValue);\n        ids.current = [\n            ...updatedFieldArrayValues\n        ].map((item, i)=>!item || i === index ? generateId() : ids.current[i]);\n        updateValues(updatedFieldArrayValues);\n        setFields([\n            ...updatedFieldArrayValues\n        ]);\n        control._updateFieldArray(name, updatedFieldArrayValues, updateAt, {\n            argA: index,\n            argB: updateValue\n        }, true, false);\n    };\n    const replace = (value1)=>{\n        const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value1));\n        ids.current = updatedFieldArrayValues.map(generateId);\n        updateValues([\n            ...updatedFieldArrayValues\n        ]);\n        setFields([\n            ...updatedFieldArrayValues\n        ]);\n        control._updateFieldArray(name, [\n            ...updatedFieldArrayValues\n        ], (data)=>data, {}, true, false);\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        control._state.action = false;\n        isWatched(name, control._names) && control._subjects.state.next({\n            ...control._formState\n        });\n        if (_actioned.current && (!getValidationModes(control._options.mode).isOnSubmit || control._formState.isSubmitted)) {\n            if (control._options.resolver) {\n                control._executeSchema([\n                    name\n                ]).then((result)=>{\n                    const error = get(result.errors, name);\n                    const existingError = get(control._formState.errors, name);\n                    if (existingError ? !error && existingError.type || error && (existingError.type !== error.type || existingError.message !== error.message) : error && error.type) {\n                        error ? set(control._formState.errors, name, error) : unset(control._formState.errors, name);\n                        control._subjects.state.next({\n                            errors: control._formState.errors\n                        });\n                    }\n                });\n            } else {\n                const field = get(control._fields, name);\n                if (field && field._f && !(getValidationModes(control._options.reValidateMode).isOnSubmit && getValidationModes(control._options.mode).isOnSubmit)) {\n                    validateField(field, control._names.disabled, control._formValues, control._options.criteriaMode === VALIDATION_MODE.all, control._options.shouldUseNativeValidation, true).then((error)=>!isEmptyObject(error) && control._subjects.state.next({\n                            errors: updateFieldArrayRootError(control._formState.errors, error, name)\n                        }));\n                }\n            }\n        }\n        control._subjects.values.next({\n            name,\n            values: {\n                ...control._formValues\n            }\n        });\n        control._names.focus && iterateFieldsByAction(control._fields, (ref, key)=>{\n            if (control._names.focus && key.startsWith(control._names.focus) && ref.focus) {\n                ref.focus();\n                return 1;\n            }\n            return;\n        });\n        control._names.focus = \"\";\n        control._updateValid();\n        _actioned.current = false;\n    }, [\n        fields,\n        name,\n        control\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        !get(control._formValues, name) && control._updateFieldArray(name);\n        return ()=>{\n            (control._options.shouldUnregister || shouldUnregister) && control.unregister(name);\n        };\n    }, [\n        name,\n        control,\n        keyName,\n        shouldUnregister\n    ]);\n    return {\n        swap: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(swap, [\n            updateValues,\n            name,\n            control\n        ]),\n        move: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(move, [\n            updateValues,\n            name,\n            control\n        ]),\n        prepend: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(prepend, [\n            updateValues,\n            name,\n            control\n        ]),\n        append: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(append, [\n            updateValues,\n            name,\n            control\n        ]),\n        remove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(remove, [\n            updateValues,\n            name,\n            control\n        ]),\n        insert: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(insert$1, [\n            updateValues,\n            name,\n            control\n        ]),\n        update: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(update, [\n            updateValues,\n            name,\n            control\n        ]),\n        replace: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(replace, [\n            updateValues,\n            name,\n            control\n        ]),\n        fields: react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>fields.map((field, index)=>({\n                    ...field,\n                    [keyName]: ids.current[index] || generateId()\n                })), [\n            fields,\n            keyName\n        ])\n    };\n}\nvar createSubject = ()=>{\n    let _observers = [];\n    const next = (value1)=>{\n        for (const observer of _observers){\n            observer.next && observer.next(value1);\n        }\n    };\n    const subscribe = (observer)=>{\n        _observers.push(observer);\n        return {\n            unsubscribe: ()=>{\n                _observers = _observers.filter((o)=>o !== observer);\n            }\n        };\n    };\n    const unsubscribe = ()=>{\n        _observers = [];\n    };\n    return {\n        get observers () {\n            return _observers;\n        },\n        next,\n        subscribe,\n        unsubscribe\n    };\n};\nvar isPrimitive = (value1)=>isNullOrUndefined(value1) || !isObjectType(value1);\nfunction deepEqual(object1, object2) {\n    if (isPrimitive(object1) || isPrimitive(object2)) {\n        return object1 === object2;\n    }\n    if (isDateObject(object1) && isDateObject(object2)) {\n        return object1.getTime() === object2.getTime();\n    }\n    const keys1 = Object.keys(object1);\n    const keys2 = Object.keys(object2);\n    if (keys1.length !== keys2.length) {\n        return false;\n    }\n    for (const key of keys1){\n        const val1 = object1[key];\n        if (!keys2.includes(key)) {\n            return false;\n        }\n        if (key !== \"ref\") {\n            const val2 = object2[key];\n            if (isDateObject(val1) && isDateObject(val2) || isObject(val1) && isObject(val2) || Array.isArray(val1) && Array.isArray(val2) ? !deepEqual(val1, val2) : val1 !== val2) {\n                return false;\n            }\n        }\n    }\n    return true;\n}\nvar isMultipleSelect = (element)=>element.type === `select-multiple`;\nvar isRadioOrCheckbox = (ref)=>isRadioInput(ref) || isCheckBoxInput(ref);\nvar live = (ref)=>isHTMLElement(ref) && ref.isConnected;\nvar objectHasFunction = (data)=>{\n    for(const key in data){\n        if (isFunction(data[key])) {\n            return true;\n        }\n    }\n    return false;\n};\nfunction markFieldsDirty(data, fields = {}) {\n    const isParentNodeArray = Array.isArray(data);\n    if (isObject(data) || isParentNodeArray) {\n        for(const key in data){\n            if (Array.isArray(data[key]) || isObject(data[key]) && !objectHasFunction(data[key])) {\n                fields[key] = Array.isArray(data[key]) ? [] : {};\n                markFieldsDirty(data[key], fields[key]);\n            } else if (!isNullOrUndefined(data[key])) {\n                fields[key] = true;\n            }\n        }\n    }\n    return fields;\n}\nfunction getDirtyFieldsFromDefaultValues(data, formValues, dirtyFieldsFromValues) {\n    const isParentNodeArray = Array.isArray(data);\n    if (isObject(data) || isParentNodeArray) {\n        for(const key in data){\n            if (Array.isArray(data[key]) || isObject(data[key]) && !objectHasFunction(data[key])) {\n                if (isUndefined(formValues) || isPrimitive(dirtyFieldsFromValues[key])) {\n                    dirtyFieldsFromValues[key] = Array.isArray(data[key]) ? markFieldsDirty(data[key], []) : {\n                        ...markFieldsDirty(data[key])\n                    };\n                } else {\n                    getDirtyFieldsFromDefaultValues(data[key], isNullOrUndefined(formValues) ? {} : formValues[key], dirtyFieldsFromValues[key]);\n                }\n            } else {\n                dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n            }\n        }\n    }\n    return dirtyFieldsFromValues;\n}\nvar getDirtyFields = (defaultValues, formValues)=>getDirtyFieldsFromDefaultValues(defaultValues, formValues, markFieldsDirty(formValues));\nvar getFieldValueAs = (value1, { valueAsNumber, valueAsDate, setValueAs })=>isUndefined(value1) ? value1 : valueAsNumber ? value1 === \"\" ? NaN : value1 ? +value1 : value1 : valueAsDate && isString(value1) ? new Date(value1) : setValueAs ? setValueAs(value1) : value1;\nfunction getFieldValue(_f) {\n    const ref = _f.ref;\n    if (isFileInput(ref)) {\n        return ref.files;\n    }\n    if (isRadioInput(ref)) {\n        return getRadioValue(_f.refs).value;\n    }\n    if (isMultipleSelect(ref)) {\n        return [\n            ...ref.selectedOptions\n        ].map(({ value: value1 })=>value1);\n    }\n    if (isCheckBoxInput(ref)) {\n        return getCheckboxValue(_f.refs).value;\n    }\n    return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\nvar getResolverOptions = (fieldsNames, _fields, criteriaMode, shouldUseNativeValidation)=>{\n    const fields = {};\n    for (const name of fieldsNames){\n        const field = get(_fields, name);\n        field && set(fields, name, field._f);\n    }\n    return {\n        criteriaMode,\n        names: [\n            ...fieldsNames\n        ],\n        fields,\n        shouldUseNativeValidation\n    };\n};\nvar getRuleValue = (rule)=>isUndefined(rule) ? rule : isRegex(rule) ? rule.source : isObject(rule) ? isRegex(rule.value) ? rule.value.source : rule.value : rule;\nconst ASYNC_FUNCTION = \"AsyncFunction\";\nvar hasPromiseValidation = (fieldReference)=>!!fieldReference && !!fieldReference.validate && !!(isFunction(fieldReference.validate) && fieldReference.validate.constructor.name === ASYNC_FUNCTION || isObject(fieldReference.validate) && Object.values(fieldReference.validate).find((validateFunction)=>validateFunction.constructor.name === ASYNC_FUNCTION));\nvar hasValidation = (options)=>options.mount && (options.required || options.min || options.max || options.maxLength || options.minLength || options.pattern || options.validate);\nfunction schemaErrorLookup(errors, _fields, name) {\n    const error = get(errors, name);\n    if (error || isKey(name)) {\n        return {\n            error,\n            name\n        };\n    }\n    const names = name.split(\".\");\n    while(names.length){\n        const fieldName = names.join(\".\");\n        const field = get(_fields, fieldName);\n        const foundError = get(errors, fieldName);\n        if (field && !Array.isArray(field) && name !== fieldName) {\n            return {\n                name\n            };\n        }\n        if (foundError && foundError.type) {\n            return {\n                name: fieldName,\n                error: foundError\n            };\n        }\n        names.pop();\n    }\n    return {\n        name\n    };\n}\nvar skipValidation = (isBlurEvent, isTouched, isSubmitted, reValidateMode, mode)=>{\n    if (mode.isOnAll) {\n        return false;\n    } else if (!isSubmitted && mode.isOnTouch) {\n        return !(isTouched || isBlurEvent);\n    } else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n        return !isBlurEvent;\n    } else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n        return isBlurEvent;\n    }\n    return true;\n};\nvar unsetEmptyArray = (ref, name)=>!compact(get(ref, name)).length && unset(ref, name);\nconst defaultOptions = {\n    mode: VALIDATION_MODE.onSubmit,\n    reValidateMode: VALIDATION_MODE.onChange,\n    shouldFocusError: true\n};\nfunction createFormControl(props = {}) {\n    let _options = {\n        ...defaultOptions,\n        ...props\n    };\n    let _formState = {\n        submitCount: 0,\n        isDirty: false,\n        isLoading: isFunction(_options.defaultValues),\n        isValidating: false,\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        touchedFields: {},\n        dirtyFields: {},\n        validatingFields: {},\n        errors: _options.errors || {},\n        disabled: _options.disabled || false\n    };\n    let _fields = {};\n    let _defaultValues = isObject(_options.defaultValues) || isObject(_options.values) ? cloneObject(_options.defaultValues || _options.values) || {} : {};\n    let _formValues = _options.shouldUnregister ? {} : cloneObject(_defaultValues);\n    let _state = {\n        action: false,\n        mount: false,\n        watch: false\n    };\n    let _names = {\n        mount: new Set(),\n        disabled: new Set(),\n        unMount: new Set(),\n        array: new Set(),\n        watch: new Set()\n    };\n    let delayErrorCallback;\n    let timer = 0;\n    const _proxyFormState = {\n        isDirty: false,\n        dirtyFields: false,\n        validatingFields: false,\n        touchedFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false\n    };\n    const _subjects = {\n        values: createSubject(),\n        array: createSubject(),\n        state: createSubject()\n    };\n    const validationModeBeforeSubmit = getValidationModes(_options.mode);\n    const validationModeAfterSubmit = getValidationModes(_options.reValidateMode);\n    const shouldDisplayAllAssociatedErrors = _options.criteriaMode === VALIDATION_MODE.all;\n    const debounce = (callback)=>(wait)=>{\n            clearTimeout(timer);\n            timer = setTimeout(callback, wait);\n        };\n    const _updateValid = async (shouldUpdateValid)=>{\n        if (!_options.disabled && (_proxyFormState.isValid || shouldUpdateValid)) {\n            const isValid = _options.resolver ? isEmptyObject((await _executeSchema()).errors) : await executeBuiltInValidation(_fields, true);\n            if (isValid !== _formState.isValid) {\n                _subjects.state.next({\n                    isValid\n                });\n            }\n        }\n    };\n    const _updateIsValidating = (names, isValidating)=>{\n        if (!_options.disabled && (_proxyFormState.isValidating || _proxyFormState.validatingFields)) {\n            (names || Array.from(_names.mount)).forEach((name)=>{\n                if (name) {\n                    isValidating ? set(_formState.validatingFields, name, isValidating) : unset(_formState.validatingFields, name);\n                }\n            });\n            _subjects.state.next({\n                validatingFields: _formState.validatingFields,\n                isValidating: !isEmptyObject(_formState.validatingFields)\n            });\n        }\n    };\n    const _updateFieldArray = (name, values = [], method, args, shouldSetValues = true, shouldUpdateFieldsAndState = true)=>{\n        if (args && method && !_options.disabled) {\n            _state.action = true;\n            if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n                const fieldValues = method(get(_fields, name), args.argA, args.argB);\n                shouldSetValues && set(_fields, name, fieldValues);\n            }\n            if (shouldUpdateFieldsAndState && Array.isArray(get(_formState.errors, name))) {\n                const errors = method(get(_formState.errors, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.errors, name, errors);\n                unsetEmptyArray(_formState.errors, name);\n            }\n            if (_proxyFormState.touchedFields && shouldUpdateFieldsAndState && Array.isArray(get(_formState.touchedFields, name))) {\n                const touchedFields = method(get(_formState.touchedFields, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n            }\n            if (_proxyFormState.dirtyFields) {\n                _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n            }\n            _subjects.state.next({\n                name,\n                isDirty: _getDirty(name, values),\n                dirtyFields: _formState.dirtyFields,\n                errors: _formState.errors,\n                isValid: _formState.isValid\n            });\n        } else {\n            set(_formValues, name, values);\n        }\n    };\n    const updateErrors = (name, error)=>{\n        set(_formState.errors, name, error);\n        _subjects.state.next({\n            errors: _formState.errors\n        });\n    };\n    const _setErrors = (errors)=>{\n        _formState.errors = errors;\n        _subjects.state.next({\n            errors: _formState.errors,\n            isValid: false\n        });\n    };\n    const updateValidAndValue = (name, shouldSkipSetValueAs, value1, ref)=>{\n        const field = get(_fields, name);\n        if (field) {\n            const defaultValue = get(_formValues, name, isUndefined(value1) ? get(_defaultValues, name) : value1);\n            isUndefined(defaultValue) || ref && ref.defaultChecked || shouldSkipSetValueAs ? set(_formValues, name, shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f)) : setFieldValue(name, defaultValue);\n            _state.mount && _updateValid();\n        }\n    };\n    const updateTouchAndDirty = (name, fieldValue, isBlurEvent, shouldDirty, shouldRender)=>{\n        let shouldUpdateField = false;\n        let isPreviousDirty = false;\n        const output = {\n            name\n        };\n        if (!_options.disabled) {\n            const disabledField = !!(get(_fields, name) && get(_fields, name)._f && get(_fields, name)._f.disabled);\n            if (!isBlurEvent || shouldDirty) {\n                if (_proxyFormState.isDirty) {\n                    isPreviousDirty = _formState.isDirty;\n                    _formState.isDirty = output.isDirty = _getDirty();\n                    shouldUpdateField = isPreviousDirty !== output.isDirty;\n                }\n                const isCurrentFieldPristine = disabledField || deepEqual(get(_defaultValues, name), fieldValue);\n                isPreviousDirty = !!(!disabledField && get(_formState.dirtyFields, name));\n                isCurrentFieldPristine || disabledField ? unset(_formState.dirtyFields, name) : set(_formState.dirtyFields, name, true);\n                output.dirtyFields = _formState.dirtyFields;\n                shouldUpdateField = shouldUpdateField || _proxyFormState.dirtyFields && isPreviousDirty !== !isCurrentFieldPristine;\n            }\n            if (isBlurEvent) {\n                const isPreviousFieldTouched = get(_formState.touchedFields, name);\n                if (!isPreviousFieldTouched) {\n                    set(_formState.touchedFields, name, isBlurEvent);\n                    output.touchedFields = _formState.touchedFields;\n                    shouldUpdateField = shouldUpdateField || _proxyFormState.touchedFields && isPreviousFieldTouched !== isBlurEvent;\n                }\n            }\n            shouldUpdateField && shouldRender && _subjects.state.next(output);\n        }\n        return shouldUpdateField ? output : {};\n    };\n    const shouldRenderByError = (name, isValid, error, fieldState)=>{\n        const previousFieldError = get(_formState.errors, name);\n        const shouldUpdateValid = _proxyFormState.isValid && isBoolean(isValid) && _formState.isValid !== isValid;\n        if (_options.delayError && error) {\n            delayErrorCallback = debounce(()=>updateErrors(name, error));\n            delayErrorCallback(_options.delayError);\n        } else {\n            clearTimeout(timer);\n            delayErrorCallback = null;\n            error ? set(_formState.errors, name, error) : unset(_formState.errors, name);\n        }\n        if ((error ? !deepEqual(previousFieldError, error) : previousFieldError) || !isEmptyObject(fieldState) || shouldUpdateValid) {\n            const updatedFormState = {\n                ...fieldState,\n                ...shouldUpdateValid && isBoolean(isValid) ? {\n                    isValid\n                } : {},\n                errors: _formState.errors,\n                name\n            };\n            _formState = {\n                ..._formState,\n                ...updatedFormState\n            };\n            _subjects.state.next(updatedFormState);\n        }\n    };\n    const _executeSchema = async (name)=>{\n        _updateIsValidating(name, true);\n        const result = await _options.resolver(_formValues, _options.context, getResolverOptions(name || _names.mount, _fields, _options.criteriaMode, _options.shouldUseNativeValidation));\n        _updateIsValidating(name);\n        return result;\n    };\n    const executeSchemaAndUpdateState = async (names)=>{\n        const { errors } = await _executeSchema(names);\n        if (names) {\n            for (const name of names){\n                const error = get(errors, name);\n                error ? set(_formState.errors, name, error) : unset(_formState.errors, name);\n            }\n        } else {\n            _formState.errors = errors;\n        }\n        return errors;\n    };\n    const executeBuiltInValidation = async (fields, shouldOnlyCheckValid, context = {\n        valid: true\n    })=>{\n        for(const name in fields){\n            const field = fields[name];\n            if (field) {\n                const { _f, ...fieldValue } = field;\n                if (_f) {\n                    const isFieldArrayRoot = _names.array.has(_f.name);\n                    const isPromiseFunction = field._f && hasPromiseValidation(field._f);\n                    if (isPromiseFunction && _proxyFormState.validatingFields) {\n                        _updateIsValidating([\n                            name\n                        ], true);\n                    }\n                    const fieldError = await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation && !shouldOnlyCheckValid, isFieldArrayRoot);\n                    if (isPromiseFunction && _proxyFormState.validatingFields) {\n                        _updateIsValidating([\n                            name\n                        ]);\n                    }\n                    if (fieldError[_f.name]) {\n                        context.valid = false;\n                        if (shouldOnlyCheckValid) {\n                            break;\n                        }\n                    }\n                    !shouldOnlyCheckValid && (get(fieldError, _f.name) ? isFieldArrayRoot ? updateFieldArrayRootError(_formState.errors, fieldError, _f.name) : set(_formState.errors, _f.name, fieldError[_f.name]) : unset(_formState.errors, _f.name));\n                }\n                !isEmptyObject(fieldValue) && await executeBuiltInValidation(fieldValue, shouldOnlyCheckValid, context);\n            }\n        }\n        return context.valid;\n    };\n    const _removeUnmounted = ()=>{\n        for (const name of _names.unMount){\n            const field = get(_fields, name);\n            field && (field._f.refs ? field._f.refs.every((ref)=>!live(ref)) : !live(field._f.ref)) && unregister(name);\n        }\n        _names.unMount = new Set();\n    };\n    const _getDirty = (name, data)=>!_options.disabled && (name && data && set(_formValues, name, data), !deepEqual(getValues(), _defaultValues));\n    const _getWatch = (names, defaultValue, isGlobal)=>generateWatchOutput(names, _names, {\n            ..._state.mount ? _formValues : isUndefined(defaultValue) ? _defaultValues : isString(names) ? {\n                [names]: defaultValue\n            } : defaultValue\n        }, isGlobal, defaultValue);\n    const _getFieldArray = (name)=>compact(get(_state.mount ? _formValues : _defaultValues, name, _options.shouldUnregister ? get(_defaultValues, name, []) : []));\n    const setFieldValue = (name, value1, options = {})=>{\n        const field = get(_fields, name);\n        let fieldValue = value1;\n        if (field) {\n            const fieldReference = field._f;\n            if (fieldReference) {\n                !fieldReference.disabled && set(_formValues, name, getFieldValueAs(value1, fieldReference));\n                fieldValue = isHTMLElement(fieldReference.ref) && isNullOrUndefined(value1) ? \"\" : value1;\n                if (isMultipleSelect(fieldReference.ref)) {\n                    [\n                        ...fieldReference.ref.options\n                    ].forEach((optionRef)=>optionRef.selected = fieldValue.includes(optionRef.value));\n                } else if (fieldReference.refs) {\n                    if (isCheckBoxInput(fieldReference.ref)) {\n                        fieldReference.refs.length > 1 ? fieldReference.refs.forEach((checkboxRef)=>(!checkboxRef.defaultChecked || !checkboxRef.disabled) && (checkboxRef.checked = Array.isArray(fieldValue) ? !!fieldValue.find((data)=>data === checkboxRef.value) : fieldValue === checkboxRef.value)) : fieldReference.refs[0] && (fieldReference.refs[0].checked = !!fieldValue);\n                    } else {\n                        fieldReference.refs.forEach((radioRef)=>radioRef.checked = radioRef.value === fieldValue);\n                    }\n                } else if (isFileInput(fieldReference.ref)) {\n                    fieldReference.ref.value = \"\";\n                } else {\n                    fieldReference.ref.value = fieldValue;\n                    if (!fieldReference.ref.type) {\n                        _subjects.values.next({\n                            name,\n                            values: {\n                                ..._formValues\n                            }\n                        });\n                    }\n                }\n            }\n        }\n        (options.shouldDirty || options.shouldTouch) && updateTouchAndDirty(name, fieldValue, options.shouldTouch, options.shouldDirty, true);\n        options.shouldValidate && trigger(name);\n    };\n    const setValues = (name, value1, options)=>{\n        for(const fieldKey in value1){\n            const fieldValue = value1[fieldKey];\n            const fieldName = `${name}.${fieldKey}`;\n            const field = get(_fields, fieldName);\n            (_names.array.has(name) || isObject(fieldValue) || field && !field._f) && !isDateObject(fieldValue) ? setValues(fieldName, fieldValue, options) : setFieldValue(fieldName, fieldValue, options);\n        }\n    };\n    const setValue = (name, value1, options = {})=>{\n        const field = get(_fields, name);\n        const isFieldArray = _names.array.has(name);\n        const cloneValue = cloneObject(value1);\n        set(_formValues, name, cloneValue);\n        if (isFieldArray) {\n            _subjects.array.next({\n                name,\n                values: {\n                    ..._formValues\n                }\n            });\n            if ((_proxyFormState.isDirty || _proxyFormState.dirtyFields) && options.shouldDirty) {\n                _subjects.state.next({\n                    name,\n                    dirtyFields: getDirtyFields(_defaultValues, _formValues),\n                    isDirty: _getDirty(name, cloneValue)\n                });\n            }\n        } else {\n            field && !field._f && !isNullOrUndefined(cloneValue) ? setValues(name, cloneValue, options) : setFieldValue(name, cloneValue, options);\n        }\n        isWatched(name, _names) && _subjects.state.next({\n            ..._formState\n        });\n        _subjects.values.next({\n            name: _state.mount ? name : undefined,\n            values: {\n                ..._formValues\n            }\n        });\n    };\n    const onChange = async (event)=>{\n        _state.mount = true;\n        const target = event.target;\n        let name = target.name;\n        let isFieldValueUpdated = true;\n        const field = get(_fields, name);\n        const getCurrentFieldValue = ()=>target.type ? getFieldValue(field._f) : getEventValue(event);\n        const _updateIsFieldValueUpdated = (fieldValue)=>{\n            isFieldValueUpdated = Number.isNaN(fieldValue) || isDateObject(fieldValue) && isNaN(fieldValue.getTime()) || deepEqual(fieldValue, get(_formValues, name, fieldValue));\n        };\n        if (field) {\n            let error;\n            let isValid;\n            const fieldValue = getCurrentFieldValue();\n            const isBlurEvent = event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n            const shouldSkipValidation = !hasValidation(field._f) && !_options.resolver && !get(_formState.errors, name) && !field._f.deps || skipValidation(isBlurEvent, get(_formState.touchedFields, name), _formState.isSubmitted, validationModeAfterSubmit, validationModeBeforeSubmit);\n            const watched = isWatched(name, _names, isBlurEvent);\n            set(_formValues, name, fieldValue);\n            if (isBlurEvent) {\n                field._f.onBlur && field._f.onBlur(event);\n                delayErrorCallback && delayErrorCallback(0);\n            } else if (field._f.onChange) {\n                field._f.onChange(event);\n            }\n            const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent, false);\n            const shouldRender = !isEmptyObject(fieldState) || watched;\n            !isBlurEvent && _subjects.values.next({\n                name,\n                type: event.type,\n                values: {\n                    ..._formValues\n                }\n            });\n            if (shouldSkipValidation) {\n                if (_proxyFormState.isValid) {\n                    if (_options.mode === \"onBlur\" && isBlurEvent) {\n                        _updateValid();\n                    } else if (!isBlurEvent) {\n                        _updateValid();\n                    }\n                }\n                return shouldRender && _subjects.state.next({\n                    name,\n                    ...watched ? {} : fieldState\n                });\n            }\n            !isBlurEvent && watched && _subjects.state.next({\n                ..._formState\n            });\n            if (_options.resolver) {\n                const { errors } = await _executeSchema([\n                    name\n                ]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    const previousErrorLookupResult = schemaErrorLookup(_formState.errors, _fields, name);\n                    const errorLookupResult = schemaErrorLookup(errors, _fields, previousErrorLookupResult.name || name);\n                    error = errorLookupResult.error;\n                    name = errorLookupResult.name;\n                    isValid = isEmptyObject(errors);\n                }\n            } else {\n                _updateIsValidating([\n                    name\n                ], true);\n                error = (await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation))[name];\n                _updateIsValidating([\n                    name\n                ]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    if (error) {\n                        isValid = false;\n                    } else if (_proxyFormState.isValid) {\n                        isValid = await executeBuiltInValidation(_fields, true);\n                    }\n                }\n            }\n            if (isFieldValueUpdated) {\n                field._f.deps && trigger(field._f.deps);\n                shouldRenderByError(name, isValid, error, fieldState);\n            }\n        }\n    };\n    const _focusInput = (ref, key)=>{\n        if (get(_formState.errors, key) && ref.focus) {\n            ref.focus();\n            return 1;\n        }\n        return;\n    };\n    const trigger = async (name, options = {})=>{\n        let isValid;\n        let validationResult;\n        const fieldNames = convertToArrayPayload(name);\n        if (_options.resolver) {\n            const errors = await executeSchemaAndUpdateState(isUndefined(name) ? name : fieldNames);\n            isValid = isEmptyObject(errors);\n            validationResult = name ? !fieldNames.some((name)=>get(errors, name)) : isValid;\n        } else if (name) {\n            validationResult = (await Promise.all(fieldNames.map(async (fieldName)=>{\n                const field = get(_fields, fieldName);\n                return await executeBuiltInValidation(field && field._f ? {\n                    [fieldName]: field\n                } : field);\n            }))).every(Boolean);\n            !(!validationResult && !_formState.isValid) && _updateValid();\n        } else {\n            validationResult = isValid = await executeBuiltInValidation(_fields);\n        }\n        _subjects.state.next({\n            ...!isString(name) || _proxyFormState.isValid && isValid !== _formState.isValid ? {} : {\n                name\n            },\n            ..._options.resolver || !name ? {\n                isValid\n            } : {},\n            errors: _formState.errors\n        });\n        options.shouldFocus && !validationResult && iterateFieldsByAction(_fields, _focusInput, name ? fieldNames : _names.mount);\n        return validationResult;\n    };\n    const getValues = (fieldNames)=>{\n        const values = {\n            ..._state.mount ? _formValues : _defaultValues\n        };\n        return isUndefined(fieldNames) ? values : isString(fieldNames) ? get(values, fieldNames) : fieldNames.map((name)=>get(values, name));\n    };\n    const getFieldState = (name, formState)=>({\n            invalid: !!get((formState || _formState).errors, name),\n            isDirty: !!get((formState || _formState).dirtyFields, name),\n            error: get((formState || _formState).errors, name),\n            isValidating: !!get(_formState.validatingFields, name),\n            isTouched: !!get((formState || _formState).touchedFields, name)\n        });\n    const clearErrors = (name)=>{\n        name && convertToArrayPayload(name).forEach((inputName)=>unset(_formState.errors, inputName));\n        _subjects.state.next({\n            errors: name ? _formState.errors : {}\n        });\n    };\n    const setError = (name, error, options)=>{\n        const ref = (get(_fields, name, {\n            _f: {}\n        })._f || {}).ref;\n        const currentError = get(_formState.errors, name) || {};\n        // Don't override existing error messages elsewhere in the object tree.\n        const { ref: currentRef, message, type, ...restOfErrorTree } = currentError;\n        set(_formState.errors, name, {\n            ...restOfErrorTree,\n            ...error,\n            ref\n        });\n        _subjects.state.next({\n            name,\n            errors: _formState.errors,\n            isValid: false\n        });\n        options && options.shouldFocus && ref && ref.focus && ref.focus();\n    };\n    const watch = (name, defaultValue)=>isFunction(name) ? _subjects.values.subscribe({\n            next: (payload)=>name(_getWatch(undefined, defaultValue), payload)\n        }) : _getWatch(name, defaultValue, true);\n    const unregister = (name, options = {})=>{\n        for (const fieldName of name ? convertToArrayPayload(name) : _names.mount){\n            _names.mount.delete(fieldName);\n            _names.array.delete(fieldName);\n            if (!options.keepValue) {\n                unset(_fields, fieldName);\n                unset(_formValues, fieldName);\n            }\n            !options.keepError && unset(_formState.errors, fieldName);\n            !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n            !options.keepTouched && unset(_formState.touchedFields, fieldName);\n            !options.keepIsValidating && unset(_formState.validatingFields, fieldName);\n            !_options.shouldUnregister && !options.keepDefaultValue && unset(_defaultValues, fieldName);\n        }\n        _subjects.values.next({\n            values: {\n                ..._formValues\n            }\n        });\n        _subjects.state.next({\n            ..._formState,\n            ...!options.keepDirty ? {} : {\n                isDirty: _getDirty()\n            }\n        });\n        !options.keepIsValid && _updateValid();\n    };\n    const _updateDisabledField = ({ disabled, name, field, fields })=>{\n        if (isBoolean(disabled) && _state.mount || !!disabled || _names.disabled.has(name)) {\n            disabled ? _names.disabled.add(name) : _names.disabled.delete(name);\n            updateTouchAndDirty(name, getFieldValue(field ? field._f : get(fields, name)._f), false, false, true);\n        }\n    };\n    const register = (name, options = {})=>{\n        let field = get(_fields, name);\n        const disabledIsDefined = isBoolean(options.disabled) || isBoolean(_options.disabled);\n        set(_fields, name, {\n            ...field || {},\n            _f: {\n                ...field && field._f ? field._f : {\n                    ref: {\n                        name\n                    }\n                },\n                name,\n                mount: true,\n                ...options\n            }\n        });\n        _names.mount.add(name);\n        if (field) {\n            _updateDisabledField({\n                field,\n                disabled: isBoolean(options.disabled) ? options.disabled : _options.disabled,\n                name\n            });\n        } else {\n            updateValidAndValue(name, true, options.value);\n        }\n        return {\n            ...disabledIsDefined ? {\n                disabled: options.disabled || _options.disabled\n            } : {},\n            ..._options.progressive ? {\n                required: !!options.required,\n                min: getRuleValue(options.min),\n                max: getRuleValue(options.max),\n                minLength: getRuleValue(options.minLength),\n                maxLength: getRuleValue(options.maxLength),\n                pattern: getRuleValue(options.pattern)\n            } : {},\n            name,\n            onChange,\n            onBlur: onChange,\n            ref: (ref)=>{\n                if (ref) {\n                    register(name, options);\n                    field = get(_fields, name);\n                    const fieldRef = isUndefined(ref.value) ? ref.querySelectorAll ? ref.querySelectorAll(\"input,select,textarea\")[0] || ref : ref : ref;\n                    const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n                    const refs = field._f.refs || [];\n                    if (radioOrCheckbox ? refs.find((option)=>option === fieldRef) : fieldRef === field._f.ref) {\n                        return;\n                    }\n                    set(_fields, name, {\n                        _f: {\n                            ...field._f,\n                            ...radioOrCheckbox ? {\n                                refs: [\n                                    ...refs.filter(live),\n                                    fieldRef,\n                                    ...Array.isArray(get(_defaultValues, name)) ? [\n                                        {}\n                                    ] : []\n                                ],\n                                ref: {\n                                    type: fieldRef.type,\n                                    name\n                                }\n                            } : {\n                                ref: fieldRef\n                            }\n                        }\n                    });\n                    updateValidAndValue(name, false, undefined, fieldRef);\n                } else {\n                    field = get(_fields, name, {});\n                    if (field._f) {\n                        field._f.mount = false;\n                    }\n                    (_options.shouldUnregister || options.shouldUnregister) && !(isNameInFieldArray(_names.array, name) && _state.action) && _names.unMount.add(name);\n                }\n            }\n        };\n    };\n    const _focusError = ()=>_options.shouldFocusError && iterateFieldsByAction(_fields, _focusInput, _names.mount);\n    const _disableForm = (disabled)=>{\n        if (isBoolean(disabled)) {\n            _subjects.state.next({\n                disabled\n            });\n            iterateFieldsByAction(_fields, (ref, name)=>{\n                const currentField = get(_fields, name);\n                if (currentField) {\n                    ref.disabled = currentField._f.disabled || disabled;\n                    if (Array.isArray(currentField._f.refs)) {\n                        currentField._f.refs.forEach((inputRef)=>{\n                            inputRef.disabled = currentField._f.disabled || disabled;\n                        });\n                    }\n                }\n            }, 0, false);\n        }\n    };\n    const handleSubmit = (onValid, onInvalid)=>async (e)=>{\n            let onValidError = undefined;\n            if (e) {\n                e.preventDefault && e.preventDefault();\n                e.persist && e.persist();\n            }\n            let fieldValues = cloneObject(_formValues);\n            if (_names.disabled.size) {\n                for (const name of _names.disabled){\n                    set(fieldValues, name, undefined);\n                }\n            }\n            _subjects.state.next({\n                isSubmitting: true\n            });\n            if (_options.resolver) {\n                const { errors, values } = await _executeSchema();\n                _formState.errors = errors;\n                fieldValues = values;\n            } else {\n                await executeBuiltInValidation(_fields);\n            }\n            unset(_formState.errors, \"root\");\n            if (isEmptyObject(_formState.errors)) {\n                _subjects.state.next({\n                    errors: {}\n                });\n                try {\n                    await onValid(fieldValues, e);\n                } catch (error) {\n                    onValidError = error;\n                }\n            } else {\n                if (onInvalid) {\n                    await onInvalid({\n                        ..._formState.errors\n                    }, e);\n                }\n                _focusError();\n                setTimeout(_focusError);\n            }\n            _subjects.state.next({\n                isSubmitted: true,\n                isSubmitting: false,\n                isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n                submitCount: _formState.submitCount + 1,\n                errors: _formState.errors\n            });\n            if (onValidError) {\n                throw onValidError;\n            }\n        };\n    const resetField = (name, options = {})=>{\n        if (get(_fields, name)) {\n            if (isUndefined(options.defaultValue)) {\n                setValue(name, cloneObject(get(_defaultValues, name)));\n            } else {\n                setValue(name, options.defaultValue);\n                set(_defaultValues, name, cloneObject(options.defaultValue));\n            }\n            if (!options.keepTouched) {\n                unset(_formState.touchedFields, name);\n            }\n            if (!options.keepDirty) {\n                unset(_formState.dirtyFields, name);\n                _formState.isDirty = options.defaultValue ? _getDirty(name, cloneObject(get(_defaultValues, name))) : _getDirty();\n            }\n            if (!options.keepError) {\n                unset(_formState.errors, name);\n                _proxyFormState.isValid && _updateValid();\n            }\n            _subjects.state.next({\n                ..._formState\n            });\n        }\n    };\n    const _reset = (formValues, keepStateOptions = {})=>{\n        const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n        const cloneUpdatedValues = cloneObject(updatedValues);\n        const isEmptyResetValues = isEmptyObject(formValues);\n        const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n        if (!keepStateOptions.keepDefaultValues) {\n            _defaultValues = updatedValues;\n        }\n        if (!keepStateOptions.keepValues) {\n            if (keepStateOptions.keepDirtyValues) {\n                const fieldsToCheck = new Set([\n                    ..._names.mount,\n                    ...Object.keys(getDirtyFields(_defaultValues, _formValues))\n                ]);\n                for (const fieldName of Array.from(fieldsToCheck)){\n                    get(_formState.dirtyFields, fieldName) ? set(values, fieldName, get(_formValues, fieldName)) : setValue(fieldName, get(values, fieldName));\n                }\n            } else {\n                if (isWeb && isUndefined(formValues)) {\n                    for (const name of _names.mount){\n                        const field = get(_fields, name);\n                        if (field && field._f) {\n                            const fieldReference = Array.isArray(field._f.refs) ? field._f.refs[0] : field._f.ref;\n                            if (isHTMLElement(fieldReference)) {\n                                const form = fieldReference.closest(\"form\");\n                                if (form) {\n                                    form.reset();\n                                    break;\n                                }\n                            }\n                        }\n                    }\n                }\n                _fields = {};\n            }\n            _formValues = _options.shouldUnregister ? keepStateOptions.keepDefaultValues ? cloneObject(_defaultValues) : {} : cloneObject(values);\n            _subjects.array.next({\n                values: {\n                    ...values\n                }\n            });\n            _subjects.values.next({\n                values: {\n                    ...values\n                }\n            });\n        }\n        _names = {\n            mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n            unMount: new Set(),\n            array: new Set(),\n            disabled: new Set(),\n            watch: new Set(),\n            watchAll: false,\n            focus: \"\"\n        };\n        _state.mount = !_proxyFormState.isValid || !!keepStateOptions.keepIsValid || !!keepStateOptions.keepDirtyValues;\n        _state.watch = !!_options.shouldUnregister;\n        _subjects.state.next({\n            submitCount: keepStateOptions.keepSubmitCount ? _formState.submitCount : 0,\n            isDirty: isEmptyResetValues ? false : keepStateOptions.keepDirty ? _formState.isDirty : !!(keepStateOptions.keepDefaultValues && !deepEqual(formValues, _defaultValues)),\n            isSubmitted: keepStateOptions.keepIsSubmitted ? _formState.isSubmitted : false,\n            dirtyFields: isEmptyResetValues ? {} : keepStateOptions.keepDirtyValues ? keepStateOptions.keepDefaultValues && _formValues ? getDirtyFields(_defaultValues, _formValues) : _formState.dirtyFields : keepStateOptions.keepDefaultValues && formValues ? getDirtyFields(_defaultValues, formValues) : keepStateOptions.keepDirty ? _formState.dirtyFields : {},\n            touchedFields: keepStateOptions.keepTouched ? _formState.touchedFields : {},\n            errors: keepStateOptions.keepErrors ? _formState.errors : {},\n            isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful ? _formState.isSubmitSuccessful : false,\n            isSubmitting: false\n        });\n    };\n    const reset = (formValues, keepStateOptions)=>_reset(isFunction(formValues) ? formValues(_formValues) : formValues, keepStateOptions);\n    const setFocus = (name, options = {})=>{\n        const field = get(_fields, name);\n        const fieldReference = field && field._f;\n        if (fieldReference) {\n            const fieldRef = fieldReference.refs ? fieldReference.refs[0] : fieldReference.ref;\n            if (fieldRef.focus) {\n                fieldRef.focus();\n                options.shouldSelect && isFunction(fieldRef.select) && fieldRef.select();\n            }\n        }\n    };\n    const _updateFormState = (updatedFormState)=>{\n        _formState = {\n            ..._formState,\n            ...updatedFormState\n        };\n    };\n    const _resetDefaultValues = ()=>isFunction(_options.defaultValues) && _options.defaultValues().then((values)=>{\n            reset(values, _options.resetOptions);\n            _subjects.state.next({\n                isLoading: false\n            });\n        });\n    return {\n        control: {\n            register,\n            unregister,\n            getFieldState,\n            handleSubmit,\n            setError,\n            _executeSchema,\n            _getWatch,\n            _getDirty,\n            _updateValid,\n            _removeUnmounted,\n            _updateFieldArray,\n            _updateDisabledField,\n            _getFieldArray,\n            _reset,\n            _resetDefaultValues,\n            _updateFormState,\n            _disableForm,\n            _subjects,\n            _proxyFormState,\n            _setErrors,\n            get _fields () {\n                return _fields;\n            },\n            get _formValues () {\n                return _formValues;\n            },\n            get _state () {\n                return _state;\n            },\n            set _state (value){\n                _state = value;\n            },\n            get _defaultValues () {\n                return _defaultValues;\n            },\n            get _names () {\n                return _names;\n            },\n            set _names (value){\n                _names = value;\n            },\n            get _formState () {\n                return _formState;\n            },\n            set _formState (value){\n                _formState = value;\n            },\n            get _options () {\n                return _options;\n            },\n            set _options (value){\n                _options = {\n                    ..._options,\n                    ...value\n                };\n            }\n        },\n        trigger,\n        register,\n        handleSubmit,\n        watch,\n        setValue,\n        getValues,\n        reset,\n        resetField,\n        clearErrors,\n        unregister,\n        setError,\n        setFocus,\n        getFieldState\n    };\n}\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */ function useForm(props = {}) {\n    const _formControl = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const _values = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const [formState, updateFormState] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        isDirty: false,\n        isValidating: false,\n        isLoading: isFunction(props.defaultValues),\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        submitCount: 0,\n        dirtyFields: {},\n        touchedFields: {},\n        validatingFields: {},\n        errors: props.errors || {},\n        disabled: props.disabled || false,\n        defaultValues: isFunction(props.defaultValues) ? undefined : props.defaultValues\n    });\n    if (!_formControl.current) {\n        _formControl.current = {\n            ...createFormControl(props),\n            formState\n        };\n    }\n    const control = _formControl.current.control;\n    control._options = props;\n    useSubscribe({\n        subject: control._subjects.state,\n        next: (value1)=>{\n            if (shouldRenderFormState(value1, control._proxyFormState, control._updateFormState, true)) {\n                updateFormState({\n                    ...control._formState\n                });\n            }\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>control._disableForm(props.disabled), [\n        control,\n        props.disabled\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (control._proxyFormState.isDirty) {\n            const isDirty = control._getDirty();\n            if (isDirty !== formState.isDirty) {\n                control._subjects.state.next({\n                    isDirty\n                });\n            }\n        }\n    }, [\n        control,\n        formState.isDirty\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (props.values && !deepEqual(props.values, _values.current)) {\n            control._reset(props.values, control._options.resetOptions);\n            _values.current = props.values;\n            updateFormState((state)=>({\n                    ...state\n                }));\n        } else {\n            control._resetDefaultValues();\n        }\n    }, [\n        props.values,\n        control\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (props.errors) {\n            control._setErrors(props.errors);\n        }\n    }, [\n        props.errors,\n        control\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (!control._state.mount) {\n            control._updateValid();\n            control._state.mount = true;\n        }\n        if (control._state.watch) {\n            control._state.watch = false;\n            control._subjects.state.next({\n                ...control._formState\n            });\n        }\n        control._removeUnmounted();\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        props.shouldUnregister && control._subjects.values.next({\n            values: control._getWatch()\n        });\n    }, [\n        props.shouldUnregister,\n        control\n    ]);\n    _formControl.current.formState = getProxyFormState(formState, control);\n    return _formControl.current;\n}\n //# sourceMappingURL=index.esm.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\n");

/***/ })

};
;