"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/groups/[id]/page",{

/***/ "(app-pages-browser)/./app/groups/[id]/page.tsx":
/*!**********************************!*\
  !*** ./app/groups/[id]/page.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GroupDetail; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layouts_mobile_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layouts/mobile-layout */ \"(app-pages-browser)/./components/layouts/mobile-layout.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smile.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/reply.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _app_components_group_suggestion_form__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/components/group-suggestion-form */ \"(app-pages-browser)/./app/components/group-suggestion-form.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// Removed quote components - quotes functionality has been removed\n\n// Mock data - would be fetched based on [id] in real app\nconst groupData = {\n    id: 1,\n    name: \"Living Room Remodel Group\",\n    description: \"A collective purchase for premium living room furniture at wholesale prices.\",\n    stage: \"suggestion\",\n    suggestedProducts: [\n        {\n            id: 1,\n            name: \"Premium Leather Sofa Set\",\n            price: 3500,\n            image: \"/images/placeholder.png\",\n            description: \"Genuine leather sofa set with matching ottoman\",\n            merchant: \"Luxury Furniture Co.\",\n            source: \"internal\",\n            reactions: [\n                {\n                    id: 1,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 11:20\"\n                },\n                {\n                    id: 2,\n                    userId: 2,\n                    userName: \"John Doe\",\n                    emoji: \"❤️\",\n                    timestamp: \"2023-03-10 11:25\"\n                },\n                {\n                    id: 3,\n                    userId: 3,\n                    userName: \"Alice Johnson\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 11:30\"\n                }\n            ],\n            consensusScore: 85,\n            threadCount: 3\n        },\n        {\n            id: 2,\n            name: \"Modern Fabric Sectional\",\n            price: 2800,\n            image: \"/images/placeholder.png\",\n            description: \"L-shaped sectional with chaise lounge in premium fabric\",\n            merchant: \"Contemporary Home\",\n            source: \"internal\",\n            reactions: [\n                {\n                    id: 4,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 11:35\"\n                },\n                {\n                    id: 5,\n                    userId: 4,\n                    userName: \"Bob Williams\",\n                    emoji: \"\\uD83E\\uDD14\",\n                    timestamp: \"2023-03-10 11:40\"\n                }\n            ],\n            consensusScore: 65,\n            threadCount: 2\n        },\n        {\n            id: 3,\n            name: \"Custom Wood Frame Sofa\",\n            price: 0,\n            image: \"/images/placeholder.png\",\n            description: \"Hand-crafted wooden frame sofa with custom upholstery\",\n            merchant: null,\n            source: \"external\",\n            reactions: [\n                {\n                    id: 6,\n                    userId: 5,\n                    userName: \"Carol Davis\",\n                    emoji: \"\\uD83D\\uDE0D\",\n                    timestamp: \"2023-03-10 12:20\"\n                },\n                {\n                    id: 7,\n                    userId: 2,\n                    userName: \"John Doe\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 12:25\"\n                }\n            ],\n            consensusScore: 70,\n            threadCount: 1\n        }\n    ],\n    selectedProduct: null,\n    product: {\n        name: \"Premium Leather Sofa Set\",\n        price: 3500,\n        image: \"/images/placeholder.png\"\n    },\n    members: [\n        {\n            id: 1,\n            name: \"Jane Smith\",\n            isAdmin: true,\n            amountPaid: 850\n        },\n        {\n            id: 2,\n            name: \"John Doe\",\n            isAdmin: false,\n            amountPaid: 700\n        },\n        {\n            id: 3,\n            name: \"Alice Johnson\",\n            isAdmin: false,\n            amountPaid: 600\n        },\n        {\n            id: 4,\n            name: \"Bob Williams\",\n            isAdmin: false,\n            amountPaid: 0\n        },\n        {\n            id: 5,\n            name: \"Carol Davis\",\n            isAdmin: false,\n            amountPaid: 0\n        }\n    ],\n    amountPaid: 2150,\n    totalAmount: 3500,\n    expiresIn: \"5 days\",\n    status: \"manufacturing\",\n    manufacturingProgress: 65,\n    manufacturingUpdates: [\n        {\n            id: 1,\n            date: \"2023-03-15\",\n            title: \"Production Started\",\n            description: \"Materials sourced and production has begun.\",\n            images: [\n                \"/images/placeholder.png\"\n            ]\n        },\n        {\n            id: 2,\n            date: \"2023-03-18\",\n            title: \"Frame Assembly\",\n            description: \"Wooden frames are assembled and ready for upholstery.\",\n            images: [\n                \"/images/placeholder.png\"\n            ]\n        },\n        {\n            id: 3,\n            date: \"2023-03-21\",\n            title: \"Upholstery Progress\",\n            description: \"Leather upholstery is being applied to the frames.\",\n            images: [\n                \"/images/placeholder.png\"\n            ]\n        }\n    ],\n    messages: [\n        {\n            id: 1,\n            user: \"Jane Smith\",\n            userId: 1,\n            content: \"Welcome everyone to our group buy!\",\n            timestamp: \"2023-03-10 10:23\",\n            type: \"text\",\n            reactions: [\n                {\n                    id: 1,\n                    userId: 2,\n                    userName: \"John Doe\",\n                    emoji: \"\\uD83D\\uDC4B\",\n                    timestamp: \"2023-03-10 10:25\"\n                },\n                {\n                    id: 2,\n                    userId: 3,\n                    userName: \"Alice Johnson\",\n                    emoji: \"\\uD83C\\uDF89\",\n                    timestamp: \"2023-03-10 10:26\"\n                }\n            ]\n        },\n        {\n            id: 2,\n            user: \"John Doe\",\n            userId: 2,\n            content: \"Thanks for organizing this!\",\n            timestamp: \"2023-03-10 10:45\",\n            type: \"text\",\n            reactions: [\n                {\n                    id: 3,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"❤️\",\n                    timestamp: \"2023-03-10 10:46\"\n                }\n            ]\n        },\n        {\n            id: 3,\n            user: \"Alice Johnson\",\n            userId: 3,\n            content: \"I added a Premium Leather Sofa Set to our product suggestions. What do you all think?\",\n            timestamp: \"2023-03-10 11:15\",\n            type: \"product-suggestion\",\n            productRef: 1,\n            detectedProducts: [\n                {\n                    text: \"Premium Leather Sofa Set\",\n                    startIndex: 9,\n                    endIndex: 33,\n                    suggestedProductId: 1,\n                    confidence: 0.95\n                }\n            ],\n            reactions: [\n                {\n                    id: 4,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 11:20\"\n                },\n                {\n                    id: 5,\n                    userId: 2,\n                    userName: \"John Doe\",\n                    emoji: \"\\uD83E\\uDD14\",\n                    timestamp: \"2023-03-10 11:22\"\n                }\n            ]\n        },\n        {\n            id: 4,\n            user: \"Jane Smith\",\n            userId: 1,\n            content: \"I like it but it's a bit pricey. I found this fabric sectional that might be more budget-friendly.\",\n            timestamp: \"2023-03-10 11:30\",\n            type: \"product-suggestion\",\n            productRef: 2,\n            parentMessageId: 3,\n            threadId: \"product-1-discussion\",\n            detectedProducts: [\n                {\n                    text: \"fabric sectional\",\n                    startIndex: 55,\n                    endIndex: 70,\n                    suggestedProductId: 2,\n                    confidence: 0.88\n                }\n            ],\n            reactions: [\n                {\n                    id: 6,\n                    userId: 4,\n                    userName: \"Bob Williams\",\n                    emoji: \"\\uD83D\\uDCB0\",\n                    timestamp: \"2023-03-10 11:35\"\n                }\n            ]\n        },\n        {\n            id: 5,\n            user: \"Bob Williams\",\n            userId: 4,\n            content: \"I saw this custom sofa at a local craftsman's shop. Uploading a photo I took.\",\n            timestamp: \"2023-03-10 12:15\",\n            type: \"product-suggestion\",\n            productRef: 3,\n            attachment: \"/images/placeholder.png\",\n            detectedProducts: [\n                {\n                    text: \"custom sofa\",\n                    startIndex: 12,\n                    endIndex: 23,\n                    suggestedProductId: 3,\n                    confidence: 0.92\n                }\n            ],\n            reactions: [\n                {\n                    id: 7,\n                    userId: 5,\n                    userName: \"Carol Davis\",\n                    emoji: \"\\uD83D\\uDCF8\",\n                    timestamp: \"2023-03-10 12:20\"\n                },\n                {\n                    id: 8,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"\\uD83D\\uDC40\",\n                    timestamp: \"2023-03-10 12:22\"\n                }\n            ]\n        },\n        {\n            id: 6,\n            user: \"John Doe\",\n            userId: 2,\n            content: \"The leather sofa looks amazing! How's the delivery time?\",\n            timestamp: \"2023-03-10 12:30\",\n            type: \"text\",\n            parentMessageId: 3,\n            threadId: \"product-1-discussion\",\n            detectedProducts: [\n                {\n                    text: \"leather sofa\",\n                    startIndex: 4,\n                    endIndex: 16,\n                    suggestedProductId: 1,\n                    confidence: 0.9\n                }\n            ]\n        },\n        {\n            id: 7,\n            user: \"Carol Davis\",\n            userId: 5,\n            content: \"I'm really interested in the custom wood frame option. Can we get more details?\",\n            timestamp: \"2023-03-10 13:00\",\n            type: \"text\",\n            parentMessageId: 5,\n            threadId: \"product-3-discussion\",\n            detectedProducts: [\n                {\n                    text: \"custom wood frame\",\n                    startIndex: 30,\n                    endIndex: 47,\n                    suggestedProductId: 3,\n                    confidence: 0.85\n                }\n            ]\n        }\n    ]\n};\n// Helper function to get the appropriate default tab based on group stage\nconst getDefaultTab = (stage)=>{\n    switch(stage){\n        case \"suggestion\":\n            return \"discussion\"; // suggestions are now part of discussion\n        case \"discussion\":\n            return \"discussion\";\n        case \"payment\":\n            return \"payment\";\n        case \"manufacturing\":\n            return \"manufacturing\";\n        case \"shipping\":\n            return \"shipping\";\n        default:\n            return \"discussion\";\n    }\n};\nfunction GroupDetail(param) {\n    let { params } = param;\n    var _groupData_suggestedProducts_find, _groupData_suggestedProducts_find1, _groupData_suggestedProducts_find2, _groupData_suggestedProducts_find3, _groupData_suggestedProducts_find4;\n    _s();\n    // In a real app, you would fetch the group data based on params.id\n    const defaultTab = getDefaultTab(groupData.stage);\n    const [selectedProductRef, setSelectedProductRef] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showProductSelector, setShowProductSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProductsOverview, setShowProductsOverview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messageText, setMessageText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Phase 2 state management\n    const [expandedThreads, setExpandedThreads] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [showEmojiPicker, setShowEmojiPicker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [replyingTo, setReplyingTo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentUserId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1); // Mock current user - Jane Smith\n    const handleSendMessage = ()=>{\n        // In a real app, this would send the message to the API\n        console.log(\"Sending message:\", {\n            content: messageText,\n            productRef: selectedProductRef\n        });\n        // Reset the form\n        setMessageText(\"\");\n        setSelectedProductRef(null);\n    };\n    const handleSelectProduct = (productId)=>{\n        setSelectedProductRef(productId);\n        setShowProductSelector(false);\n    };\n    // Phase 2 helper functions\n    const handleAddReaction = (emoji, messageId, productId)=>{\n        // In a real app, this would send the reaction to the API\n        console.log(\"Adding reaction:\", {\n            emoji,\n            messageId,\n            productId,\n            userId: currentUserId\n        });\n        setShowEmojiPicker(null);\n    };\n    const toggleThread = (threadId)=>{\n        const newExpanded = new Set(expandedThreads);\n        if (newExpanded.has(threadId)) {\n            newExpanded.delete(threadId);\n        } else {\n            newExpanded.add(threadId);\n        }\n        setExpandedThreads(newExpanded);\n    };\n    const getThreadMessages = (threadId)=>{\n        return groupData.messages.filter((msg)=>msg.threadId === threadId);\n    };\n    const getMainMessages = ()=>{\n        return groupData.messages.filter((msg)=>!msg.parentMessageId);\n    };\n    const getConsensusColor = (score)=>{\n        if (score >= 80) return \"text-green-600\";\n        if (score >= 60) return \"text-yellow-600\";\n        return \"text-red-600\";\n    };\n    const renderDetectedProducts = (content, detectedProducts)=>{\n        if (!detectedProducts || detectedProducts.length === 0) {\n            return content;\n        }\n        let result = content;\n        let offset = 0;\n        detectedProducts.sort((a, b)=>a.startIndex - b.startIndex).forEach((detected)=>{\n            const start = detected.startIndex + offset;\n            const end = detected.endIndex + offset;\n            const productName = result.substring(start, end);\n            const replacement = '<span class=\"bg-blue-100 text-blue-800 px-1 rounded cursor-pointer hover:bg-blue-200\" data-product-id=\"'.concat(detected.suggestedProductId, '\">').concat(productName, \"</span>\");\n            result = result.substring(0, start) + replacement + result.substring(end);\n            offset += replacement.length - productName.length;\n        });\n        return result;\n    };\n    const renderStageIndicator = ()=>{\n        const stages = [\n            {\n                id: \"suggestion\",\n                label: \"Suggestions\"\n            },\n            {\n                id: \"discussion\",\n                label: \"Discussion\"\n            },\n            {\n                id: \"payment\",\n                label: \"Payment\"\n            },\n            {\n                id: \"manufacturing\",\n                label: \"Manufacturing\"\n            },\n            {\n                id: \"shipping\",\n                label: \"Shipping\"\n            }\n        ];\n        const currentIndex = stages.findIndex((s)=>s.id === groupData.stage);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-sm font-medium mb-2\",\n                    children: \"Current Stage\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 587,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full bg-secondary rounded-full h-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-primary h-2 rounded-full transition-all duration-500 ease-in-out\",\n                        style: {\n                            width: \"\".concat((currentIndex + 1) / stages.length * 100, \"%\")\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 589,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 588,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between mt-1 text-xs\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-primary font-medium\",\n                            children: stages[currentIndex].label\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 595,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-muted-foreground\",\n                            children: [\n                                \"Step \",\n                                currentIndex + 1,\n                                \" of \",\n                                stages.length\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 598,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 594,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n            lineNumber: 586,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layouts_mobile_layout__WEBPACK_IMPORTED_MODULE_2__.MobileLayout, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative bg-primary text-primary-foreground p-4 pb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                        href: \"/groups\",\n                        className: \"flex items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 610,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-1\",\n                                children: \"Back to Groups\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 611,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 609,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-fluid-xl font-bold\",\n                        children: groupData.name\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 613,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mt-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                size: 16,\n                                className: \"mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 615,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    groupData.members.length,\n                                    \" members\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 616,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mx-2\",\n                                children: \"•\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 617,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                size: 16,\n                                className: \"mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 618,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Expires in \",\n                                    groupData.expiresIn\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 619,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 614,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 608,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"-mt-6 mb-6 relative z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"p-4\",\n                            children: renderStageIndicator()\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 625,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 624,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                        defaultValue: defaultTab,\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                                className: \"w-full grid grid-cols-1 mb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"discussion\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 631,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \"Discussion\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 632,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 630,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 629,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                                className: \"w-full grid grid-cols-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                        value: \"manufacturing\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 638,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"Manufacturing\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 639,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 637,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                        value: \"members\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 642,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"Members\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 643,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 641,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                        value: \"payment\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 646,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"Payment\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 645,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 636,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                value: \"discussion\",\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: getMainMessages().map((message)=>{\n                                            var _groupData_suggestedProducts_find, _groupData_suggestedProducts_find1, _groupData_suggestedProducts_find2, _groupData_suggestedProducts_find3, _groupData_suggestedProducts_find4;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"rounded-lg p-3 \".concat(message.type === \"product-suggestion\" ? \"bg-blue-50 border border-blue-200\" : \"bg-muted\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: message.user\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 668,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            message.type === \"product-suggestion\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                variant: \"secondary\",\n                                                                                className: \"text-xs\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                        className: \"h-3 w-3 mr-1\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                        lineNumber: 671,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    \"Product\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 670,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 667,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs text-muted-foreground\",\n                                                                                children: message.timestamp\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 677,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"icon\",\n                                                                                className: \"h-6 w-6\",\n                                                                                onClick: ()=>setShowEmojiPicker({\n                                                                                        messageId: message.id\n                                                                                    }),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 688,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 680,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"icon\",\n                                                                                className: \"h-6 w-6\",\n                                                                                onClick: ()=>setReplyingTo(message.id),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 696,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 690,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 676,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 666,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm mb-2\",\n                                                                dangerouslySetInnerHTML: {\n                                                                    __html: renderDetectedProducts(message.content, message.detectedProducts)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 701,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            message.reactions && message.reactions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-wrap gap-1 mb-2\",\n                                                                children: message.reactions.map((reaction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        className: \"h-6 px-2 text-xs\",\n                                                                        title: \"\".concat(reaction.userName, \" reacted with \").concat(reaction.emoji),\n                                                                        children: [\n                                                                            reaction.emoji,\n                                                                            \" 1\"\n                                                                        ]\n                                                                    }, reaction.id, true, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 715,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 713,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            message.threadId && getThreadMessages(message.threadId).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"sm\",\n                                                                className: \"text-xs text-blue-600 hover:text-blue-800 p-0 h-auto\",\n                                                                onClick: ()=>toggleThread(message.threadId),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 737,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    getThreadMessages(message.threadId).length,\n                                                                    \" replies\",\n                                                                    expandedThreads.has(message.threadId) ? \" ▼\" : \" ▶\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 731,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            message.productRef && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2 p-3 bg-background rounded border border-border\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-16 h-16 bg-muted rounded overflow-hidden flex items-center justify-center mr-3\",\n                                                                            children: ((_groupData_suggestedProducts_find = groupData.suggestedProducts.find((p)=>p.id === message.productRef)) === null || _groupData_suggestedProducts_find === void 0 ? void 0 : _groupData_suggestedProducts_find.image) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"relative w-full h-full\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                                                                    src: ((_groupData_suggestedProducts_find1 = groupData.suggestedProducts.find((p)=>p.id === message.productRef)) === null || _groupData_suggestedProducts_find1 === void 0 ? void 0 : _groupData_suggestedProducts_find1.image) || \"/images/placeholder.png\",\n                                                                                    alt: ((_groupData_suggestedProducts_find2 = groupData.suggestedProducts.find((p)=>p.id === message.productRef)) === null || _groupData_suggestedProducts_find2 === void 0 ? void 0 : _groupData_suggestedProducts_find2.name) || \"Product\",\n                                                                                    fill: true,\n                                                                                    className: \"object-cover\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 752,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 751,\n                                                                                columnNumber: 31\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"h-6 w-6 text-muted-foreground\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 768,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 747,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex justify-between items-start mb-1\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"font-medium text-sm\",\n                                                                                            children: (_groupData_suggestedProducts_find3 = groupData.suggestedProducts.find((p)=>p.id === message.productRef)) === null || _groupData_suggestedProducts_find3 === void 0 ? void 0 : _groupData_suggestedProducts_find3.name\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                            lineNumber: 773,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-1\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                                variant: \"ghost\",\n                                                                                                size: \"icon\",\n                                                                                                className: \"h-6 w-6\",\n                                                                                                onClick: ()=>setShowEmojiPicker({\n                                                                                                        productId: message.productRef\n                                                                                                    }),\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                                    className: \"h-3 w-3\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                                    lineNumber: 791,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                                lineNumber: 781,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                            lineNumber: 780,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 772,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-muted-foreground text-sm mb-2\",\n                                                                                    children: [\n                                                                                        \"$\",\n                                                                                        ((_groupData_suggestedProducts_find4 = groupData.suggestedProducts.find((p)=>p.id === message.productRef)) === null || _groupData_suggestedProducts_find4 === void 0 ? void 0 : _groupData_suggestedProducts_find4.price) || \"Price unavailable\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 795,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                (()=>{\n                                                                                    const product = groupData.suggestedProducts.find((p)=>p.id === message.productRef);\n                                                                                    return (product === null || product === void 0 ? void 0 : product.reactions) && product.reactions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex flex-wrap gap-1 mb-2\",\n                                                                                        children: product.reactions.map((reaction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                                variant: \"outline\",\n                                                                                                size: \"sm\",\n                                                                                                className: \"h-6 px-2 text-xs\",\n                                                                                                title: \"\".concat(reaction.userName, \" reacted with \").concat(reaction.emoji),\n                                                                                                children: [\n                                                                                                    reaction.emoji,\n                                                                                                    \" 1\"\n                                                                                                ]\n                                                                                            }, reaction.id, true, {\n                                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                                lineNumber: 812,\n                                                                                                columnNumber: 39\n                                                                                            }, this))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                        lineNumber: 810,\n                                                                                        columnNumber: 35\n                                                                                    }, this);\n                                                                                })(),\n                                                                                (()=>{\n                                                                                    const product = groupData.suggestedProducts.find((p)=>p.id === message.productRef);\n                                                                                    return (product === null || product === void 0 ? void 0 : product.consensusScore) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center gap-2\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex-1 bg-gray-200 rounded-full h-2\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"h-2 rounded-full \".concat(product.consensusScore >= 80 ? \"bg-green-500\" : product.consensusScore >= 60 ? \"bg-yellow-500\" : \"bg-red-500\"),\n                                                                                                    style: {\n                                                                                                        width: \"\".concat(product.consensusScore, \"%\")\n                                                                                                    }\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                                    lineNumber: 836,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                                lineNumber: 835,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"text-xs font-medium \".concat(getConsensusColor(product.consensusScore)),\n                                                                                                children: [\n                                                                                                    product.consensusScore,\n                                                                                                    \"% consensus\"\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                                lineNumber: 849,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                        lineNumber: 834,\n                                                                                        columnNumber: 35\n                                                                                    }, this);\n                                                                                })()\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 771,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 746,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 745,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            message.attachment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full max-w-[200px] h-[150px] bg-muted rounded-md overflow-hidden relative\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 flex items-center justify-center text-muted-foreground\",\n                                                                        children: \"Image Attachment\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 868,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 867,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 866,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 659,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    message.threadId && expandedThreads.has(message.threadId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"ml-4 pl-4 border-l-2 border-gray-200 space-y-2\",\n                                                        children: getThreadMessages(message.threadId).map((threadMessage)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gray-50 rounded-lg p-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-start mb-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium text-sm\",\n                                                                                children: threadMessage.user\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 887,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs text-muted-foreground\",\n                                                                                children: threadMessage.timestamp\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 890,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 886,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm\",\n                                                                        dangerouslySetInnerHTML: {\n                                                                            __html: renderDetectedProducts(threadMessage.content, threadMessage.detectedProducts)\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 894,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, threadMessage.id, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 882,\n                                                                columnNumber: 29\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 879,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, message.id, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 657,\n                                                columnNumber: 17\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 655,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"icon\",\n                                                                className: \"rounded-full\",\n                                                                type: \"button\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"file-upload\",\n                                                                    className: \"cursor-pointer\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            id: \"file-upload\",\n                                                                            type: \"file\",\n                                                                            accept: \"image/*\",\n                                                                            className: \"sr-only\",\n                                                                            \"aria-label\": \"Upload image\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 922,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 929,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 921,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 915,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"icon\",\n                                                                className: \"rounded-full\",\n                                                                type: \"button\",\n                                                                onClick: ()=>setShowProductSelector(true),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 939,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 932,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_group_suggestion_form__WEBPACK_IMPORTED_MODULE_9__.GroupSuggestionForm, {\n                                                                groupId: params.id\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 941,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 914,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-muted-foreground px-2\",\n                                                        children: \"\\uD83D\\uDCA1 Suggest products, share files, or reference existing suggestions in your messages\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 943,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    selectedProductRef && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-background rounded border border-border flex items-center p-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-12 h-12 bg-muted rounded overflow-hidden flex items-center justify-center mr-3\",\n                                                                    children: ((_groupData_suggestedProducts_find = groupData.suggestedProducts.find((p)=>p.id === selectedProductRef)) === null || _groupData_suggestedProducts_find === void 0 ? void 0 : _groupData_suggestedProducts_find.image) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative w-full h-full\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                                                            src: ((_groupData_suggestedProducts_find1 = groupData.suggestedProducts.find((p)=>p.id === selectedProductRef)) === null || _groupData_suggestedProducts_find1 === void 0 ? void 0 : _groupData_suggestedProducts_find1.image) || \"/images/placeholder.png\",\n                                                                            alt: ((_groupData_suggestedProducts_find2 = groupData.suggestedProducts.find((p)=>p.id === selectedProductRef)) === null || _groupData_suggestedProducts_find2 === void 0 ? void 0 : _groupData_suggestedProducts_find2.name) || \"Product\",\n                                                                            fill: true,\n                                                                            className: \"object-cover\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 956,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 955,\n                                                                        columnNumber: 27\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-muted-foreground\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 972,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 951,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1 text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: (_groupData_suggestedProducts_find3 = groupData.suggestedProducts.find((p)=>p.id === selectedProductRef)) === null || _groupData_suggestedProducts_find3 === void 0 ? void 0 : _groupData_suggestedProducts_find3.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 976,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-muted-foreground\",\n                                                                            children: [\n                                                                                \"$\",\n                                                                                ((_groupData_suggestedProducts_find4 = groupData.suggestedProducts.find((p)=>p.id === selectedProductRef)) === null || _groupData_suggestedProducts_find4 === void 0 ? void 0 : _groupData_suggestedProducts_find4.price) || \"Price unavailable\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 983,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 975,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"icon\",\n                                                                    className: \"h-6 w-6 rounded-full ml-1\",\n                                                                    type: \"button\",\n                                                                    onClick: ()=>setSelectedProductRef(null),\n                                                                    children: \"\\xd7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 990,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 950,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 949,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 913,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: \"Type your message...\",\n                                                        className: \"flex-1 h-10 rounded-l-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n                                                        value: messageText,\n                                                        onChange: (e)=>setMessageText(e.target.value),\n                                                        onKeyDown: (e)=>{\n                                                            if (e.key === \"Enter\" && !e.shiftKey) {\n                                                                e.preventDefault();\n                                                                handleSendMessage();\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1004,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        className: \"rounded-l-none\",\n                                                        onClick: handleSendMessage,\n                                                        children: \"Send\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1017,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1003,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 912,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 654,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                value: \"payment\",\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                className: \"pb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                    className: \"text-lg\",\n                                                    children: \"Payment Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1032,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1031,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full bg-secondary rounded-full h-3 mb-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-primary h-3 rounded-full\",\n                                                            style: {\n                                                                width: \"\".concat(groupData.amountPaid / groupData.totalAmount * 100, \"%\")\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1036,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1035,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"$\",\n                                                                    groupData.amountPaid,\n                                                                    \" raised\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1046,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"$\",\n                                                                    groupData.totalAmount,\n                                                                    \" goal\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1047,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1045,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-muted-foreground text-center\",\n                                                        children: [\n                                                            \"$\",\n                                                            groupData.totalAmount - groupData.amountPaid,\n                                                            \" remaining\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1049,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1034,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardFooter, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                    href: \"/groups/\".concat(params.id, \"/payment\"),\n                                                    className: \"w-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        className: \"w-full\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1056,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Make a Payment\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1055,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1054,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1053,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1030,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium mb-2\",\n                                        children: \"Payment History\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1063,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: groupData.members.filter((member)=>member.amountPaid > 0).map((member)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-muted p-3 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-8 h-8 rounded-full bg-secondary flex items-center justify-center\",\n                                                                    children: member.name.charAt(0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1071,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: member.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1075,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-muted-foreground\",\n                                                                            children: new Date().toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1076,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1074,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1070,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: [\n                                                                \"$\",\n                                                                member.amountPaid\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1081,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1069,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, member.id, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1068,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1064,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 1029,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                value: \"manufacturing\",\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Manufacturing Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1091,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-primary font-medium capitalize\",\n                                                        children: groupData.status\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1092,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1090,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-secondary rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-primary h-2 rounded-full\",\n                                                    style: {\n                                                        width: \"\".concat(groupData.manufacturingProgress, \"%\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1097,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1096,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mt-1 text-xs text-muted-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Production Started\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1103,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Ready for Shipping\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1104,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1102,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1089,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium\",\n                                                children: \"Latest Updates\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1109,\n                                                columnNumber: 15\n                                            }, this),\n                                            groupData.manufacturingUpdates.map((update)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                    className: \"overflow-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium\",\n                                                                        children: update.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1114,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: update.date\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1115,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1113,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm mb-2\",\n                                                                children: update.description\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1119,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-24 bg-muted flex items-center justify-center rounded\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-muted-foreground\",\n                                                                    children: \"Update Image\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1121,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1120,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1112,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, update.id, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1111,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1108,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 1088,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                value: \"members\",\n                                className: \"mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: groupData.members.map((member)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 rounded-full bg-secondary flex items-center justify-center\",\n                                                            children: member.name.charAt(0)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1139,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"ml-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: member.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1144,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        member.isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-2 px-1.5 py-0.5 bg-primary/10 text-primary text-xs rounded\",\n                                                                            children: \"Admin\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1146,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1143,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-muted-foreground\",\n                                                                    children: member.amountPaid > 0 ? \"Paid $\".concat(member.amountPaid) : \"No payment yet\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1151,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1142,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1138,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-secondary text-secondary-foreground rounded-full flex items-center justify-center\",\n                                                    children: [\n                                                        Math.round(member.amountPaid / (groupData.totalAmount / groupData.members.length) * 100),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1158,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, member.id, true, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1134,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1132,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 1131,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 628,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 623,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                open: showProductSelector,\n                onOpenChange: setShowProductSelector,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                    className: \"sm:max-w-[425px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                    children: \"Select a Product\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1177,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogDescription, {\n                                    children: \"Choose a product to reference in your message\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1178,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1176,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3 mt-4 max-h-[300px] overflow-y-auto\",\n                            children: groupData.suggestedProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border rounded-md cursor-pointer hover:bg-muted flex items-center\",\n                                    onClick: ()=>handleSelectProduct(product.id),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-muted rounded overflow-hidden mr-3 flex items-center justify-center\",\n                                            children: product.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative w-full h-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                                    src: product.image || \"/images/placeholder.png\",\n                                                    alt: product.name,\n                                                    fill: true,\n                                                    className: \"object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1192,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1191,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-6 w-6 text-muted-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1200,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1189,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium\",\n                                                    children: product.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1204,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: product.price > 0 ? \"$\".concat(product.price) : \"Price unavailable\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1205,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1203,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, product.id, true, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1184,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1182,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 1175,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 1174,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-20 right-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    onClick: ()=>setShowProductsOverview(true),\n                    className: \"rounded-full shadow-lg h-12 px-4\",\n                    size: \"sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"h-4 w-4 mr-2\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1224,\n                            columnNumber: 11\n                        }, this),\n                        groupData.suggestedProducts.length,\n                        \" Products\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 1219,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 1218,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                open: showProductsOverview,\n                onOpenChange: setShowProductsOverview,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                    className: \"sm:max-w-[500px] max-h-[80vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                    children: \"Product Suggestions\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1236,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogDescription, {\n                                    children: [\n                                        groupData.suggestedProducts.length,\n                                        \" products suggested by the group\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1237,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1235,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 gap-4 mt-4\",\n                            children: groupData.suggestedProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    className: \"overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-20 h-20 bg-muted flex items-center justify-center\",\n                                                children: product.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative w-full h-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                                        src: product.image || \"/images/placeholder.png\",\n                                                        alt: product.name,\n                                                        fill: true,\n                                                        className: \"object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1249,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1248,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-6 w-6 text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1257,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1246,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-sm\",\n                                                                children: product.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1262,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs px-1.5 py-0.5 rounded \".concat(product.source === \"internal\" ? \"bg-primary/10 text-primary\" : \"bg-secondary text-secondary-foreground\"),\n                                                                        children: product.source === \"internal\" ? \"Catalog\" : \"External\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1264,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-6 w-6\",\n                                                                        onClick: ()=>setShowEmojiPicker({\n                                                                                productId: product.id\n                                                                            }),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1283,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1275,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1263,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1261,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground mb-2\",\n                                                        children: product.description\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1287,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    product.reactions && product.reactions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1 mb-2\",\n                                                        children: product.reactions.map((reaction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                className: \"h-5 px-1.5 text-xs\",\n                                                                title: \"\".concat(reaction.userName, \" reacted with \").concat(reaction.emoji),\n                                                                children: [\n                                                                    reaction.emoji,\n                                                                    \" 1\"\n                                                                ]\n                                                            }, reaction.id, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1295,\n                                                                columnNumber: 27\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1293,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    product.consensusScore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1 bg-gray-200 rounded-full h-1.5\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-1.5 rounded-full \".concat(product.consensusScore >= 80 ? \"bg-green-500\" : product.consensusScore >= 60 ? \"bg-yellow-500\" : \"bg-red-500\"),\n                                                                            style: {\n                                                                                width: \"\".concat(product.consensusScore, \"%\")\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1313,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1312,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs font-medium \".concat(getConsensusColor(product.consensusScore)),\n                                                                        children: [\n                                                                            product.consensusScore,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1324,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1311,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: [\n                                                                    \"Group consensus • \",\n                                                                    product.threadCount || 0,\n                                                                    \" \",\n                                                                    \"discussions\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1332,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1310,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm\",\n                                                                children: product.price > 0 ? \"$\".concat(product.price) : \"Price unavailable\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1340,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>{\n                                                                    setShowProductsOverview(false);\n                                                                // In a real app, this would scroll to the product in discussion\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1353,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Discuss\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1345,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1339,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1260,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1245,\n                                        columnNumber: 17\n                                    }, this)\n                                }, product.id, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1244,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1242,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 pt-4 border-t\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_group_suggestion_form__WEBPACK_IMPORTED_MODULE_9__.GroupSuggestionForm, {\n                                groupId: params.id\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 1363,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1362,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 1234,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 1230,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                open: !!showEmojiPicker,\n                onOpenChange: ()=>setShowEmojiPicker(null),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                    className: \"sm:max-w-[300px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                    children: \"Add Reaction\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1375,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogDescription, {\n                                    children: \"Choose an emoji to react with\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1376,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1374,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-6 gap-2 mt-4\",\n                            children: [\n                                \"\\uD83D\\uDC4D\",\n                                \"\\uD83D\\uDC4E\",\n                                \"❤️\",\n                                \"\\uD83D\\uDE0D\",\n                                \"\\uD83E\\uDD14\",\n                                \"\\uD83D\\uDE15\",\n                                \"\\uD83D\\uDCB0\",\n                                \"\\uD83D\\uDCB8\",\n                                \"\\uD83C\\uDF89\",\n                                \"❌\",\n                                \"\\uD83D\\uDC40\",\n                                \"\\uD83D\\uDCF8\",\n                                \"\\uD83D\\uDC4B\",\n                                \"\\uD83D\\uDD25\",\n                                \"\\uD83D\\uDCA1\",\n                                \"✨\",\n                                \"⚠️\",\n                                \"\\uD83D\\uDEAB\"\n                            ].map((emoji)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    className: \"h-12 w-12 text-2xl hover:bg-accent\",\n                                    onClick: ()=>handleAddReaction(emoji, showEmojiPicker === null || showEmojiPicker === void 0 ? void 0 : showEmojiPicker.messageId, showEmojiPicker === null || showEmojiPicker === void 0 ? void 0 : showEmojiPicker.productId),\n                                    children: emoji\n                                }, emoji, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1399,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1378,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 1373,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 1369,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n        lineNumber: 607,\n        columnNumber: 5\n    }, this);\n}\n_s(GroupDetail, \"vMnZ29SNPzEGWKW4OXNTQsVdCeI=\");\n_c = GroupDetail;\nvar _c;\n$RefreshReg$(_c, \"GroupDetail\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/groups/[id]/page.tsx\n"));

/***/ })

});