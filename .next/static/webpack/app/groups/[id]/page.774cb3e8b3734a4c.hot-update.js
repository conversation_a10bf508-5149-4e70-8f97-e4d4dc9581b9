"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/groups/[id]/page",{

/***/ "(app-pages-browser)/./app/groups/[id]/page.tsx":
/*!**********************************!*\
  !*** ./app/groups/[id]/page.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GroupDetail; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layouts_mobile_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layouts/mobile-layout */ \"(app-pages-browser)/./components/layouts/mobile-layout.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smile.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/reply.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _app_components_group_suggestion_form__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/components/group-suggestion-form */ \"(app-pages-browser)/./app/components/group-suggestion-form.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// Removed quote components - quotes functionality has been removed\n\n// Mock data - would be fetched based on [id] in real app\nconst groupData = {\n    id: 1,\n    name: \"Living Room Remodel Group\",\n    description: \"A collective purchase for premium living room furniture at wholesale prices.\",\n    stage: \"suggestion\",\n    suggestedProducts: [\n        {\n            id: 1,\n            name: \"Premium Leather Sofa Set\",\n            price: 3500,\n            image: \"/images/placeholder.png\",\n            description: \"Genuine leather sofa set with matching ottoman\",\n            merchant: \"Luxury Furniture Co.\",\n            source: \"internal\",\n            reactions: [\n                {\n                    id: 1,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 11:20\"\n                },\n                {\n                    id: 2,\n                    userId: 2,\n                    userName: \"John Doe\",\n                    emoji: \"❤️\",\n                    timestamp: \"2023-03-10 11:25\"\n                },\n                {\n                    id: 3,\n                    userId: 3,\n                    userName: \"Alice Johnson\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 11:30\"\n                }\n            ],\n            consensusScore: 85,\n            threadCount: 3\n        },\n        {\n            id: 2,\n            name: \"Modern Fabric Sectional\",\n            price: 2800,\n            image: \"/images/placeholder.png\",\n            description: \"L-shaped sectional with chaise lounge in premium fabric\",\n            merchant: \"Contemporary Home\",\n            source: \"internal\",\n            reactions: [\n                {\n                    id: 4,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 11:35\"\n                },\n                {\n                    id: 5,\n                    userId: 4,\n                    userName: \"Bob Williams\",\n                    emoji: \"\\uD83E\\uDD14\",\n                    timestamp: \"2023-03-10 11:40\"\n                },\n                {\n                    id: 8,\n                    userId: 5,\n                    userName: \"Carol Davis\",\n                    emoji: \"\\uD83D\\uDC4E\",\n                    timestamp: \"2023-03-10 11:45\"\n                },\n                {\n                    id: 9,\n                    userId: 6,\n                    userName: \"Mike Johnson\",\n                    emoji: \"\\uD83D\\uDCB8\",\n                    timestamp: \"2023-03-10 11:50\"\n                }\n            ],\n            consensusScore: 45,\n            threadCount: 2\n        },\n        {\n            id: 3,\n            name: \"Custom Wood Frame Sofa\",\n            price: 0,\n            image: \"/images/placeholder.png\",\n            description: \"Hand-crafted wooden frame sofa with custom upholstery\",\n            merchant: null,\n            source: \"external\",\n            reactions: [\n                {\n                    id: 6,\n                    userId: 5,\n                    userName: \"Carol Davis\",\n                    emoji: \"\\uD83D\\uDE0D\",\n                    timestamp: \"2023-03-10 12:20\"\n                },\n                {\n                    id: 7,\n                    userId: 2,\n                    userName: \"John Doe\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 12:25\"\n                }\n            ],\n            consensusScore: 70,\n            threadCount: 1\n        }\n    ],\n    selectedProduct: null,\n    product: {\n        name: \"Premium Leather Sofa Set\",\n        price: 3500,\n        image: \"/images/placeholder.png\"\n    },\n    members: [\n        {\n            id: 1,\n            name: \"Jane Smith\",\n            isAdmin: true,\n            amountPaid: 850\n        },\n        {\n            id: 2,\n            name: \"John Doe\",\n            isAdmin: false,\n            amountPaid: 700\n        },\n        {\n            id: 3,\n            name: \"Alice Johnson\",\n            isAdmin: false,\n            amountPaid: 600\n        },\n        {\n            id: 4,\n            name: \"Bob Williams\",\n            isAdmin: false,\n            amountPaid: 0\n        },\n        {\n            id: 5,\n            name: \"Carol Davis\",\n            isAdmin: false,\n            amountPaid: 0\n        }\n    ],\n    amountPaid: 2150,\n    totalAmount: 3500,\n    expiresIn: \"5 days\",\n    status: \"manufacturing\",\n    manufacturingProgress: 65,\n    manufacturingUpdates: [\n        {\n            id: 1,\n            date: \"2023-03-15\",\n            title: \"Production Started\",\n            description: \"Materials sourced and production has begun.\",\n            images: [\n                \"/images/placeholder.png\"\n            ]\n        },\n        {\n            id: 2,\n            date: \"2023-03-18\",\n            title: \"Frame Assembly\",\n            description: \"Wooden frames are assembled and ready for upholstery.\",\n            images: [\n                \"/images/placeholder.png\"\n            ]\n        },\n        {\n            id: 3,\n            date: \"2023-03-21\",\n            title: \"Upholstery Progress\",\n            description: \"Leather upholstery is being applied to the frames.\",\n            images: [\n                \"/images/placeholder.png\"\n            ]\n        }\n    ],\n    messages: [\n        {\n            id: 1,\n            user: \"Jane Smith\",\n            userId: 1,\n            content: \"Welcome everyone to our group buy!\",\n            timestamp: \"2023-03-10 10:23\",\n            type: \"text\",\n            reactions: [\n                {\n                    id: 1,\n                    userId: 2,\n                    userName: \"John Doe\",\n                    emoji: \"\\uD83D\\uDC4B\",\n                    timestamp: \"2023-03-10 10:25\"\n                },\n                {\n                    id: 2,\n                    userId: 3,\n                    userName: \"Alice Johnson\",\n                    emoji: \"\\uD83C\\uDF89\",\n                    timestamp: \"2023-03-10 10:26\"\n                }\n            ]\n        },\n        {\n            id: 2,\n            user: \"John Doe\",\n            userId: 2,\n            content: \"Thanks for organizing this!\",\n            timestamp: \"2023-03-10 10:45\",\n            type: \"text\",\n            reactions: [\n                {\n                    id: 3,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"❤️\",\n                    timestamp: \"2023-03-10 10:46\"\n                }\n            ]\n        },\n        {\n            id: 3,\n            user: \"Alice Johnson\",\n            userId: 3,\n            content: \"I added a Premium Leather Sofa Set to our product suggestions. What do you all think?\",\n            timestamp: \"2023-03-10 11:15\",\n            type: \"product-suggestion\",\n            productRef: 1,\n            detectedProducts: [\n                {\n                    text: \"Premium Leather Sofa Set\",\n                    startIndex: 9,\n                    endIndex: 33,\n                    suggestedProductId: 1,\n                    confidence: 0.95\n                }\n            ],\n            reactions: [\n                {\n                    id: 4,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 11:20\"\n                },\n                {\n                    id: 5,\n                    userId: 2,\n                    userName: \"John Doe\",\n                    emoji: \"\\uD83E\\uDD14\",\n                    timestamp: \"2023-03-10 11:22\"\n                }\n            ]\n        },\n        {\n            id: 4,\n            user: \"Jane Smith\",\n            userId: 1,\n            content: \"I like it but it's a bit pricey. I found this fabric sectional that might be more budget-friendly.\",\n            timestamp: \"2023-03-10 11:30\",\n            type: \"product-suggestion\",\n            productRef: 2,\n            parentMessageId: 3,\n            threadId: \"product-1-discussion\",\n            detectedProducts: [\n                {\n                    text: \"fabric sectional\",\n                    startIndex: 55,\n                    endIndex: 70,\n                    suggestedProductId: 2,\n                    confidence: 0.88\n                }\n            ],\n            reactions: [\n                {\n                    id: 6,\n                    userId: 4,\n                    userName: \"Bob Williams\",\n                    emoji: \"\\uD83D\\uDCB0\",\n                    timestamp: \"2023-03-10 11:35\"\n                }\n            ]\n        },\n        {\n            id: 5,\n            user: \"Bob Williams\",\n            userId: 4,\n            content: \"I saw this custom sofa at a local craftsman's shop. Uploading a photo I took.\",\n            timestamp: \"2023-03-10 12:15\",\n            type: \"product-suggestion\",\n            productRef: 3,\n            attachment: \"/images/placeholder.png\",\n            detectedProducts: [\n                {\n                    text: \"custom sofa\",\n                    startIndex: 12,\n                    endIndex: 23,\n                    suggestedProductId: 3,\n                    confidence: 0.92\n                }\n            ],\n            reactions: [\n                {\n                    id: 7,\n                    userId: 5,\n                    userName: \"Carol Davis\",\n                    emoji: \"\\uD83D\\uDCF8\",\n                    timestamp: \"2023-03-10 12:20\"\n                },\n                {\n                    id: 8,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"\\uD83D\\uDC40\",\n                    timestamp: \"2023-03-10 12:22\"\n                }\n            ]\n        },\n        {\n            id: 6,\n            user: \"John Doe\",\n            userId: 2,\n            content: \"The leather sofa looks amazing! How's the delivery time?\",\n            timestamp: \"2023-03-10 12:30\",\n            type: \"text\",\n            parentMessageId: 3,\n            threadId: \"product-1-discussion\",\n            detectedProducts: [\n                {\n                    text: \"leather sofa\",\n                    startIndex: 4,\n                    endIndex: 16,\n                    suggestedProductId: 1,\n                    confidence: 0.9\n                }\n            ]\n        },\n        {\n            id: 7,\n            user: \"Carol Davis\",\n            userId: 5,\n            content: \"I'm really interested in the custom wood frame option. Can we get more details?\",\n            timestamp: \"2023-03-10 13:00\",\n            type: \"text\",\n            parentMessageId: 5,\n            threadId: \"product-3-discussion\",\n            detectedProducts: [\n                {\n                    text: \"custom wood frame\",\n                    startIndex: 30,\n                    endIndex: 47,\n                    suggestedProductId: 3,\n                    confidence: 0.85\n                }\n            ]\n        }\n    ]\n};\n// Helper function to get the appropriate default tab based on group stage\nconst getDefaultTab = (stage)=>{\n    switch(stage){\n        case \"suggestion\":\n            return \"discussion\"; // suggestions are now part of discussion\n        case \"discussion\":\n            return \"discussion\";\n        case \"payment\":\n            return \"payment\";\n        case \"manufacturing\":\n            return \"manufacturing\";\n        case \"shipping\":\n            return \"shipping\";\n        default:\n            return \"discussion\";\n    }\n};\nfunction GroupDetail(param) {\n    let { params } = param;\n    var _groupData_suggestedProducts_find, _groupData_suggestedProducts_find1, _groupData_suggestedProducts_find2, _groupData_suggestedProducts_find3, _groupData_suggestedProducts_find4;\n    _s();\n    // In a real app, you would fetch the group data based on params.id\n    const defaultTab = getDefaultTab(groupData.stage);\n    const [selectedProductRef, setSelectedProductRef] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showProductSelector, setShowProductSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProductsOverview, setShowProductsOverview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messageText, setMessageText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Phase 2 state management\n    const [expandedThreads, setExpandedThreads] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [showEmojiPicker, setShowEmojiPicker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [replyingTo, setReplyingTo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentUserId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1); // Mock current user - Jane Smith\n    const handleSendMessage = ()=>{\n        // In a real app, this would send the message to the API\n        console.log(\"Sending message:\", {\n            content: messageText,\n            productRef: selectedProductRef\n        });\n        // Reset the form\n        setMessageText(\"\");\n        setSelectedProductRef(null);\n    };\n    const handleSelectProduct = (productId)=>{\n        setSelectedProductRef(productId);\n        setShowProductSelector(false);\n    };\n    // Phase 2 helper functions\n    const handleAddReaction = (emoji, messageId, productId)=>{\n        // In a real app, this would send the reaction to the API\n        console.log(\"Adding reaction:\", {\n            emoji,\n            messageId,\n            productId,\n            userId: currentUserId\n        });\n        setShowEmojiPicker(null);\n    };\n    const toggleThread = (threadId)=>{\n        const newExpanded = new Set(expandedThreads);\n        if (newExpanded.has(threadId)) {\n            newExpanded.delete(threadId);\n        } else {\n            newExpanded.add(threadId);\n        }\n        setExpandedThreads(newExpanded);\n    };\n    const getThreadMessages = (threadId)=>{\n        return groupData.messages.filter((msg)=>msg.threadId === threadId);\n    };\n    const getMainMessages = ()=>{\n        return groupData.messages.filter((msg)=>!msg.parentMessageId);\n    };\n    const getConsensusColor = (score)=>{\n        if (score >= 80) return \"text-green-600\";\n        if (score >= 60) return \"text-yellow-600\";\n        return \"text-red-600\";\n    };\n    const calculateConsensusScore = (reactions)=>{\n        if (!reactions || reactions.length === 0) return 0;\n        const positiveEmojis = [\n            \"\\uD83D\\uDC4D\",\n            \"❤️\",\n            \"\\uD83D\\uDE0D\",\n            \"\\uD83C\\uDF89\",\n            \"\\uD83D\\uDD25\",\n            \"\\uD83D\\uDCA1\",\n            \"✨\"\n        ];\n        const negativeEmojis = [\n            \"\\uD83D\\uDC4E\",\n            \"\\uD83D\\uDE15\",\n            \"❌\",\n            \"\\uD83D\\uDCB8\",\n            \"⚠️\",\n            \"\\uD83D\\uDEAB\"\n        ];\n        const neutralEmojis = [\n            \"\\uD83E\\uDD14\",\n            \"\\uD83D\\uDC40\",\n            \"\\uD83D\\uDCF8\",\n            \"\\uD83D\\uDC4B\"\n        ];\n        let positiveCount = 0;\n        let negativeCount = 0;\n        let neutralCount = 0;\n        reactions.forEach((reaction)=>{\n            if (positiveEmojis.includes(reaction.emoji)) {\n                positiveCount++;\n            } else if (negativeEmojis.includes(reaction.emoji)) {\n                negativeCount++;\n            } else {\n                neutralCount++;\n            }\n        });\n        const totalReactions = reactions.length;\n        const positiveWeight = positiveCount * 1.0;\n        const neutralWeight = neutralCount * 0.5;\n        const negativeWeight = negativeCount * 0.0;\n        const weightedScore = (positiveWeight + neutralWeight + negativeWeight) / totalReactions;\n        return Math.round(weightedScore * 100);\n    };\n    const renderDetectedProducts = (content, detectedProducts)=>{\n        if (!detectedProducts || detectedProducts.length === 0) {\n            return content;\n        }\n        let result = content;\n        let offset = 0;\n        detectedProducts.sort((a, b)=>a.startIndex - b.startIndex).forEach((detected)=>{\n            const start = detected.startIndex + offset;\n            const end = detected.endIndex + offset;\n            const productName = result.substring(start, end);\n            const replacement = '<span class=\"bg-blue-100 text-blue-800 px-1 rounded cursor-pointer hover:bg-blue-200\" data-product-id=\"'.concat(detected.suggestedProductId, '\">').concat(productName, \"</span>\");\n            result = result.substring(0, start) + replacement + result.substring(end);\n            offset += replacement.length - productName.length;\n        });\n        return result;\n    };\n    const renderStageIndicator = ()=>{\n        const stages = [\n            {\n                id: \"suggestion\",\n                label: \"Suggestions\"\n            },\n            {\n                id: \"discussion\",\n                label: \"Discussion\"\n            },\n            {\n                id: \"payment\",\n                label: \"Payment\"\n            },\n            {\n                id: \"manufacturing\",\n                label: \"Manufacturing\"\n            },\n            {\n                id: \"shipping\",\n                label: \"Shipping\"\n            }\n        ];\n        const currentIndex = stages.findIndex((s)=>s.id === groupData.stage);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-sm font-medium mb-2\",\n                    children: \"Current Stage\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 632,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full bg-secondary rounded-full h-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-primary h-2 rounded-full transition-all duration-500 ease-in-out\",\n                        style: {\n                            width: \"\".concat((currentIndex + 1) / stages.length * 100, \"%\")\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 634,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 633,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between mt-1 text-xs\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-primary font-medium\",\n                            children: stages[currentIndex].label\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 640,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-muted-foreground\",\n                            children: [\n                                \"Step \",\n                                currentIndex + 1,\n                                \" of \",\n                                stages.length\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 643,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 639,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n            lineNumber: 631,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layouts_mobile_layout__WEBPACK_IMPORTED_MODULE_2__.MobileLayout, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative bg-primary text-primary-foreground p-4 pb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                        href: \"/groups\",\n                        className: \"flex items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 655,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-1\",\n                                children: \"Back to Groups\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 656,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 654,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-fluid-xl font-bold\",\n                        children: groupData.name\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 658,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mt-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                size: 16,\n                                className: \"mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 660,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    groupData.members.length,\n                                    \" members\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 661,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mx-2\",\n                                children: \"•\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 662,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                size: 16,\n                                className: \"mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 663,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Expires in \",\n                                    groupData.expiresIn\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 664,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 659,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 653,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"-mt-6 mb-6 relative z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"p-4\",\n                            children: renderStageIndicator()\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 670,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 669,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                        defaultValue: defaultTab,\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                                className: \"w-full grid grid-cols-1 mb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"discussion\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 676,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \"Discussion\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 677,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 675,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 674,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                                className: \"w-full grid grid-cols-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                        value: \"manufacturing\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 683,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"Manufacturing\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 684,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 682,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                        value: \"members\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 687,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"Members\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 688,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 686,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                        value: \"payment\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 691,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"Payment\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 692,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 690,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 681,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                value: \"discussion\",\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: getMainMessages().map((message)=>{\n                                            var _groupData_suggestedProducts_find, _groupData_suggestedProducts_find1, _groupData_suggestedProducts_find2, _groupData_suggestedProducts_find3, _groupData_suggestedProducts_find4;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"rounded-lg p-3 \".concat(message.type === \"product-suggestion\" ? \"bg-blue-50 border border-blue-200\" : \"bg-muted\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: message.user\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 713,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            message.type === \"product-suggestion\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                variant: \"secondary\",\n                                                                                className: \"text-xs\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                        className: \"h-3 w-3 mr-1\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                        lineNumber: 716,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    \"Product\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 715,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 712,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs text-muted-foreground\",\n                                                                                children: message.timestamp\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 722,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"icon\",\n                                                                                className: \"h-6 w-6\",\n                                                                                onClick: ()=>setShowEmojiPicker({\n                                                                                        messageId: message.id\n                                                                                    }),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 733,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 725,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"icon\",\n                                                                                className: \"h-6 w-6\",\n                                                                                onClick: ()=>setReplyingTo(message.id),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 741,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 735,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 721,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 711,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm mb-2\",\n                                                                dangerouslySetInnerHTML: {\n                                                                    __html: renderDetectedProducts(message.content, message.detectedProducts)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 746,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            message.reactions && message.reactions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-wrap gap-1 mb-2\",\n                                                                children: message.reactions.map((reaction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        className: \"h-6 px-2 text-xs\",\n                                                                        title: \"\".concat(reaction.userName, \" reacted with \").concat(reaction.emoji),\n                                                                        children: [\n                                                                            reaction.emoji,\n                                                                            \" 1\"\n                                                                        ]\n                                                                    }, reaction.id, true, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 760,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 758,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            message.threadId && getThreadMessages(message.threadId).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"sm\",\n                                                                className: \"text-xs text-blue-600 hover:text-blue-800 p-0 h-auto\",\n                                                                onClick: ()=>toggleThread(message.threadId),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 782,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    getThreadMessages(message.threadId).length,\n                                                                    \" replies\",\n                                                                    expandedThreads.has(message.threadId) ? \" ▼\" : \" ▶\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 776,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            message.productRef && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2 p-3 bg-background rounded border border-border\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-16 h-16 bg-muted rounded overflow-hidden flex items-center justify-center mr-3\",\n                                                                            children: ((_groupData_suggestedProducts_find = groupData.suggestedProducts.find((p)=>p.id === message.productRef)) === null || _groupData_suggestedProducts_find === void 0 ? void 0 : _groupData_suggestedProducts_find.image) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"relative w-full h-full\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                                                                    src: ((_groupData_suggestedProducts_find1 = groupData.suggestedProducts.find((p)=>p.id === message.productRef)) === null || _groupData_suggestedProducts_find1 === void 0 ? void 0 : _groupData_suggestedProducts_find1.image) || \"/images/placeholder.png\",\n                                                                                    alt: ((_groupData_suggestedProducts_find2 = groupData.suggestedProducts.find((p)=>p.id === message.productRef)) === null || _groupData_suggestedProducts_find2 === void 0 ? void 0 : _groupData_suggestedProducts_find2.name) || \"Product\",\n                                                                                    fill: true,\n                                                                                    className: \"object-cover\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 797,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 796,\n                                                                                columnNumber: 31\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"h-6 w-6 text-muted-foreground\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 813,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 792,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex justify-between items-start mb-1\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"font-medium text-sm\",\n                                                                                            children: (_groupData_suggestedProducts_find3 = groupData.suggestedProducts.find((p)=>p.id === message.productRef)) === null || _groupData_suggestedProducts_find3 === void 0 ? void 0 : _groupData_suggestedProducts_find3.name\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                            lineNumber: 818,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-1\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                                variant: \"ghost\",\n                                                                                                size: \"icon\",\n                                                                                                className: \"h-6 w-6\",\n                                                                                                onClick: ()=>setShowEmojiPicker({\n                                                                                                        productId: message.productRef\n                                                                                                    }),\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                                    className: \"h-3 w-3\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                                    lineNumber: 836,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                                lineNumber: 826,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                            lineNumber: 825,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 817,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-muted-foreground text-sm mb-2\",\n                                                                                    children: [\n                                                                                        \"$\",\n                                                                                        ((_groupData_suggestedProducts_find4 = groupData.suggestedProducts.find((p)=>p.id === message.productRef)) === null || _groupData_suggestedProducts_find4 === void 0 ? void 0 : _groupData_suggestedProducts_find4.price) || \"Price unavailable\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 840,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                (()=>{\n                                                                                    const product = groupData.suggestedProducts.find((p)=>p.id === message.productRef);\n                                                                                    return (product === null || product === void 0 ? void 0 : product.reactions) && product.reactions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex flex-wrap gap-1 mb-2\",\n                                                                                        children: product.reactions.map((reaction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                                variant: \"outline\",\n                                                                                                size: \"sm\",\n                                                                                                className: \"h-6 px-2 text-xs\",\n                                                                                                title: \"\".concat(reaction.userName, \" reacted with \").concat(reaction.emoji),\n                                                                                                children: [\n                                                                                                    reaction.emoji,\n                                                                                                    \" 1\"\n                                                                                                ]\n                                                                                            }, reaction.id, true, {\n                                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                                lineNumber: 857,\n                                                                                                columnNumber: 39\n                                                                                            }, this))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                        lineNumber: 855,\n                                                                                        columnNumber: 35\n                                                                                    }, this);\n                                                                                })(),\n                                                                                (()=>{\n                                                                                    const product = groupData.suggestedProducts.find((p)=>p.id === message.productRef);\n                                                                                    return (product === null || product === void 0 ? void 0 : product.consensusScore) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center gap-2\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex-1 bg-gray-200 rounded-full h-2\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"h-2 rounded-full \".concat(product.consensusScore >= 80 ? \"bg-green-500\" : product.consensusScore >= 60 ? \"bg-yellow-500\" : \"bg-red-500\"),\n                                                                                                    style: {\n                                                                                                        width: \"\".concat(product.consensusScore, \"%\")\n                                                                                                    }\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                                    lineNumber: 881,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                                lineNumber: 880,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"text-xs font-medium \".concat(getConsensusColor(product.consensusScore)),\n                                                                                                children: [\n                                                                                                    product.consensusScore,\n                                                                                                    \"% consensus\"\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                                lineNumber: 894,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                        lineNumber: 879,\n                                                                                        columnNumber: 35\n                                                                                    }, this);\n                                                                                })()\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 816,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 791,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 790,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            message.attachment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full max-w-[200px] h-[150px] bg-muted rounded-md overflow-hidden relative\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 flex items-center justify-center text-muted-foreground\",\n                                                                        children: \"Image Attachment\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 913,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 912,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 911,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 704,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    message.threadId && expandedThreads.has(message.threadId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"ml-4 pl-4 border-l-2 border-gray-200 space-y-2\",\n                                                        children: getThreadMessages(message.threadId).map((threadMessage)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gray-50 rounded-lg p-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-start mb-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium text-sm\",\n                                                                                children: threadMessage.user\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 932,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs text-muted-foreground\",\n                                                                                children: threadMessage.timestamp\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 935,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 931,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm\",\n                                                                        dangerouslySetInnerHTML: {\n                                                                            __html: renderDetectedProducts(threadMessage.content, threadMessage.detectedProducts)\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 939,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, threadMessage.id, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 927,\n                                                                columnNumber: 29\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 924,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, message.id, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 702,\n                                                columnNumber: 17\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 700,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"icon\",\n                                                                className: \"rounded-full\",\n                                                                type: \"button\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"file-upload\",\n                                                                    className: \"cursor-pointer\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            id: \"file-upload\",\n                                                                            type: \"file\",\n                                                                            accept: \"image/*\",\n                                                                            className: \"sr-only\",\n                                                                            \"aria-label\": \"Upload image\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 967,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 974,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 966,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 960,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"icon\",\n                                                                className: \"rounded-full\",\n                                                                type: \"button\",\n                                                                onClick: ()=>setShowProductSelector(true),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 984,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 977,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_group_suggestion_form__WEBPACK_IMPORTED_MODULE_9__.GroupSuggestionForm, {\n                                                                groupId: params.id\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 986,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 959,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-muted-foreground px-2\",\n                                                        children: \"\\uD83D\\uDCA1 Suggest products, share files, or reference existing suggestions in your messages\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 988,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    selectedProductRef && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-background rounded border border-border flex items-center p-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-12 h-12 bg-muted rounded overflow-hidden flex items-center justify-center mr-3\",\n                                                                    children: ((_groupData_suggestedProducts_find = groupData.suggestedProducts.find((p)=>p.id === selectedProductRef)) === null || _groupData_suggestedProducts_find === void 0 ? void 0 : _groupData_suggestedProducts_find.image) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative w-full h-full\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                                                            src: ((_groupData_suggestedProducts_find1 = groupData.suggestedProducts.find((p)=>p.id === selectedProductRef)) === null || _groupData_suggestedProducts_find1 === void 0 ? void 0 : _groupData_suggestedProducts_find1.image) || \"/images/placeholder.png\",\n                                                                            alt: ((_groupData_suggestedProducts_find2 = groupData.suggestedProducts.find((p)=>p.id === selectedProductRef)) === null || _groupData_suggestedProducts_find2 === void 0 ? void 0 : _groupData_suggestedProducts_find2.name) || \"Product\",\n                                                                            fill: true,\n                                                                            className: \"object-cover\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1001,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1000,\n                                                                        columnNumber: 27\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-muted-foreground\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1017,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 996,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1 text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: (_groupData_suggestedProducts_find3 = groupData.suggestedProducts.find((p)=>p.id === selectedProductRef)) === null || _groupData_suggestedProducts_find3 === void 0 ? void 0 : _groupData_suggestedProducts_find3.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1021,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-muted-foreground\",\n                                                                            children: [\n                                                                                \"$\",\n                                                                                ((_groupData_suggestedProducts_find4 = groupData.suggestedProducts.find((p)=>p.id === selectedProductRef)) === null || _groupData_suggestedProducts_find4 === void 0 ? void 0 : _groupData_suggestedProducts_find4.price) || \"Price unavailable\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1028,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1020,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"icon\",\n                                                                    className: \"h-6 w-6 rounded-full ml-1\",\n                                                                    type: \"button\",\n                                                                    onClick: ()=>setSelectedProductRef(null),\n                                                                    children: \"\\xd7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1035,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 995,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 994,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 958,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: \"Type your message...\",\n                                                        className: \"flex-1 h-10 rounded-l-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n                                                        value: messageText,\n                                                        onChange: (e)=>setMessageText(e.target.value),\n                                                        onKeyDown: (e)=>{\n                                                            if (e.key === \"Enter\" && !e.shiftKey) {\n                                                                e.preventDefault();\n                                                                handleSendMessage();\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1049,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        className: \"rounded-l-none\",\n                                                        onClick: handleSendMessage,\n                                                        children: \"Send\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1062,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1048,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 957,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 699,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                value: \"payment\",\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                className: \"pb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                    className: \"text-lg\",\n                                                    children: \"Payment Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1077,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1076,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full bg-secondary rounded-full h-3 mb-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-primary h-3 rounded-full\",\n                                                            style: {\n                                                                width: \"\".concat(groupData.amountPaid / groupData.totalAmount * 100, \"%\")\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1081,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1080,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"$\",\n                                                                    groupData.amountPaid,\n                                                                    \" raised\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1091,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"$\",\n                                                                    groupData.totalAmount,\n                                                                    \" goal\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1092,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1090,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-muted-foreground text-center\",\n                                                        children: [\n                                                            \"$\",\n                                                            groupData.totalAmount - groupData.amountPaid,\n                                                            \" remaining\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1094,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1079,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardFooter, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                    href: \"/groups/\".concat(params.id, \"/payment\"),\n                                                    className: \"w-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        className: \"w-full\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1101,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Make a Payment\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1100,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1099,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1098,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1075,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium mb-2\",\n                                        children: \"Payment History\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1108,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: groupData.members.filter((member)=>member.amountPaid > 0).map((member)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-muted p-3 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-8 h-8 rounded-full bg-secondary flex items-center justify-center\",\n                                                                    children: member.name.charAt(0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1116,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: member.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1120,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-muted-foreground\",\n                                                                            children: new Date().toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1121,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1119,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1115,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: [\n                                                                \"$\",\n                                                                member.amountPaid\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1126,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1114,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, member.id, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1113,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1109,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 1074,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                value: \"manufacturing\",\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Manufacturing Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1136,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-primary font-medium capitalize\",\n                                                        children: groupData.status\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1137,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1135,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-secondary rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-primary h-2 rounded-full\",\n                                                    style: {\n                                                        width: \"\".concat(groupData.manufacturingProgress, \"%\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1142,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1141,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mt-1 text-xs text-muted-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Production Started\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1148,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Ready for Shipping\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1149,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1147,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1134,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium\",\n                                                children: \"Latest Updates\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1154,\n                                                columnNumber: 15\n                                            }, this),\n                                            groupData.manufacturingUpdates.map((update)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                    className: \"overflow-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium\",\n                                                                        children: update.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1159,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: update.date\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1160,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1158,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm mb-2\",\n                                                                children: update.description\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1164,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-24 bg-muted flex items-center justify-center rounded\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-muted-foreground\",\n                                                                    children: \"Update Image\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1166,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1165,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1157,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, update.id, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1156,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1153,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 1133,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                value: \"members\",\n                                className: \"mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: groupData.members.map((member)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 rounded-full bg-secondary flex items-center justify-center\",\n                                                            children: member.name.charAt(0)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1184,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"ml-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: member.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1189,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        member.isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-2 px-1.5 py-0.5 bg-primary/10 text-primary text-xs rounded\",\n                                                                            children: \"Admin\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1191,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1188,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-muted-foreground\",\n                                                                    children: member.amountPaid > 0 ? \"Paid $\".concat(member.amountPaid) : \"No payment yet\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1196,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1187,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1183,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-secondary text-secondary-foreground rounded-full flex items-center justify-center\",\n                                                    children: [\n                                                        Math.round(member.amountPaid / (groupData.totalAmount / groupData.members.length) * 100),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1203,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, member.id, true, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1179,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1177,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 1176,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 673,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 668,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                open: showProductSelector,\n                onOpenChange: setShowProductSelector,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                    className: \"sm:max-w-[425px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                    children: \"Select a Product\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1222,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogDescription, {\n                                    children: \"Choose a product to reference in your message\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1223,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1221,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3 mt-4 max-h-[300px] overflow-y-auto\",\n                            children: groupData.suggestedProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border rounded-md cursor-pointer hover:bg-muted flex items-center\",\n                                    onClick: ()=>handleSelectProduct(product.id),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-muted rounded overflow-hidden mr-3 flex items-center justify-center\",\n                                            children: product.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative w-full h-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                                    src: product.image || \"/images/placeholder.png\",\n                                                    alt: product.name,\n                                                    fill: true,\n                                                    className: \"object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1237,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1236,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-6 w-6 text-muted-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1245,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1234,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium\",\n                                                    children: product.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1249,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: product.price > 0 ? \"$\".concat(product.price) : \"Price unavailable\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1250,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1248,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, product.id, true, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1229,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1227,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 1220,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 1219,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-20 right-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    onClick: ()=>setShowProductsOverview(true),\n                    className: \"rounded-full shadow-lg h-12 px-4\",\n                    size: \"sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"h-4 w-4 mr-2\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1269,\n                            columnNumber: 11\n                        }, this),\n                        groupData.suggestedProducts.length,\n                        \" Products\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 1264,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 1263,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                open: showProductsOverview,\n                onOpenChange: setShowProductsOverview,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                    className: \"sm:max-w-[500px] max-h-[80vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                    children: \"Product Suggestions\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1281,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogDescription, {\n                                    children: [\n                                        groupData.suggestedProducts.length,\n                                        \" products suggested by the group\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1282,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1280,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 gap-4 mt-4\",\n                            children: groupData.suggestedProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    className: \"overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-20 h-20 bg-muted flex items-center justify-center\",\n                                                children: product.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative w-full h-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                                        src: product.image || \"/images/placeholder.png\",\n                                                        alt: product.name,\n                                                        fill: true,\n                                                        className: \"object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1294,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1293,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-6 w-6 text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1302,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1291,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-sm\",\n                                                                children: product.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1307,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs px-1.5 py-0.5 rounded \".concat(product.source === \"internal\" ? \"bg-primary/10 text-primary\" : \"bg-secondary text-secondary-foreground\"),\n                                                                        children: product.source === \"internal\" ? \"Catalog\" : \"External\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1309,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-6 w-6\",\n                                                                        onClick: ()=>setShowEmojiPicker({\n                                                                                productId: product.id\n                                                                            }),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1328,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1320,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1308,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1306,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground mb-2\",\n                                                        children: product.description\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1332,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    product.reactions && product.reactions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1 mb-2\",\n                                                        children: product.reactions.map((reaction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                className: \"h-5 px-1.5 text-xs\",\n                                                                title: \"\".concat(reaction.userName, \" reacted with \").concat(reaction.emoji),\n                                                                children: [\n                                                                    reaction.emoji,\n                                                                    \" 1\"\n                                                                ]\n                                                            }, reaction.id, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1340,\n                                                                columnNumber: 27\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1338,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    product.consensusScore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1 bg-gray-200 rounded-full h-1.5\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-1.5 rounded-full \".concat(product.consensusScore >= 80 ? \"bg-green-500\" : product.consensusScore >= 60 ? \"bg-yellow-500\" : \"bg-red-500\"),\n                                                                            style: {\n                                                                                width: \"\".concat(product.consensusScore, \"%\")\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1358,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1357,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs font-medium \".concat(getConsensusColor(product.consensusScore)),\n                                                                        children: [\n                                                                            product.consensusScore,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1369,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1356,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: [\n                                                                    \"Group consensus • \",\n                                                                    product.threadCount || 0,\n                                                                    \" \",\n                                                                    \"discussions\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1377,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1355,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm\",\n                                                                children: product.price > 0 ? \"$\".concat(product.price) : \"Price unavailable\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1385,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>{\n                                                                    setShowProductsOverview(false);\n                                                                // In a real app, this would scroll to the product in discussion\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1398,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Discuss\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1390,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1384,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1305,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1290,\n                                        columnNumber: 17\n                                    }, this)\n                                }, product.id, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1289,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1287,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 pt-4 border-t\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_group_suggestion_form__WEBPACK_IMPORTED_MODULE_9__.GroupSuggestionForm, {\n                                groupId: params.id\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 1408,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1407,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 1279,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 1275,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                open: !!showEmojiPicker,\n                onOpenChange: ()=>setShowEmojiPicker(null),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                    className: \"sm:max-w-[300px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                    children: \"Add Reaction\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1420,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogDescription, {\n                                    children: \"Choose an emoji to react with\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1421,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1419,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-6 gap-2 mt-4\",\n                            children: [\n                                \"\\uD83D\\uDC4D\",\n                                \"\\uD83D\\uDC4E\",\n                                \"❤️\",\n                                \"\\uD83D\\uDE0D\",\n                                \"\\uD83E\\uDD14\",\n                                \"\\uD83D\\uDE15\",\n                                \"\\uD83D\\uDCB0\",\n                                \"\\uD83D\\uDCB8\",\n                                \"\\uD83C\\uDF89\",\n                                \"❌\",\n                                \"\\uD83D\\uDC40\",\n                                \"\\uD83D\\uDCF8\",\n                                \"\\uD83D\\uDC4B\",\n                                \"\\uD83D\\uDD25\",\n                                \"\\uD83D\\uDCA1\",\n                                \"✨\",\n                                \"⚠️\",\n                                \"\\uD83D\\uDEAB\"\n                            ].map((emoji)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    className: \"h-12 w-12 text-2xl hover:bg-accent\",\n                                    onClick: ()=>handleAddReaction(emoji, showEmojiPicker === null || showEmojiPicker === void 0 ? void 0 : showEmojiPicker.messageId, showEmojiPicker === null || showEmojiPicker === void 0 ? void 0 : showEmojiPicker.productId),\n                                    children: emoji\n                                }, emoji, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1444,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1423,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 1418,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 1414,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n        lineNumber: 652,\n        columnNumber: 5\n    }, this);\n}\n_s(GroupDetail, \"vMnZ29SNPzEGWKW4OXNTQsVdCeI=\");\n_c = GroupDetail;\nvar _c;\n$RefreshReg$(_c, \"GroupDetail\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/groups/[id]/page.tsx\n"));

/***/ })

});