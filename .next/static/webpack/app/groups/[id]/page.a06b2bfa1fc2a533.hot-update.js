"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/groups/[id]/page",{

/***/ "(app-pages-browser)/./app/groups/[id]/page.tsx":
/*!**********************************!*\
  !*** ./app/groups/[id]/page.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GroupDetail; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layouts_mobile_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layouts/mobile-layout */ \"(app-pages-browser)/./components/layouts/mobile-layout.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageSquare_ShoppingBag_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageSquare,ShoppingBag,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageSquare_ShoppingBag_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageSquare,ShoppingBag,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageSquare_ShoppingBag_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageSquare,ShoppingBag,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageSquare_ShoppingBag_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageSquare,ShoppingBag,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageSquare_ShoppingBag_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageSquare,ShoppingBag,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageSquare_ShoppingBag_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageSquare,ShoppingBag,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageSquare_ShoppingBag_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageSquare,ShoppingBag,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageSquare_ShoppingBag_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageSquare,ShoppingBag,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _app_components_group_suggestion_form__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/components/group-suggestion-form */ \"(app-pages-browser)/./app/components/group-suggestion-form.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Removed quote components - quotes functionality has been removed\n\n// Mock data - would be fetched based on [id] in real app\nconst groupData = {\n    id: 1,\n    name: \"Living Room Remodel Group\",\n    description: \"A collective purchase for premium living room furniture at wholesale prices.\",\n    stage: \"suggestion\",\n    suggestedProducts: [\n        {\n            id: 1,\n            name: \"Premium Leather Sofa Set\",\n            price: 3500,\n            image: \"/images/placeholder.png\",\n            description: \"Genuine leather sofa set with matching ottoman\",\n            merchant: \"Luxury Furniture Co.\",\n            source: \"internal\"\n        },\n        {\n            id: 2,\n            name: \"Modern Fabric Sectional\",\n            price: 2800,\n            image: \"/images/placeholder.png\",\n            description: \"L-shaped sectional with chaise lounge in premium fabric\",\n            merchant: \"Contemporary Home\",\n            source: \"internal\"\n        },\n        {\n            id: 3,\n            name: \"Custom Wood Frame Sofa\",\n            price: 0,\n            image: \"/images/placeholder.png\",\n            description: \"Hand-crafted wooden frame sofa with custom upholstery\",\n            merchant: null,\n            source: \"external\"\n        }\n    ],\n    selectedProduct: null,\n    product: {\n        name: \"Premium Leather Sofa Set\",\n        price: 3500,\n        image: \"/images/placeholder.png\"\n    },\n    members: [\n        {\n            id: 1,\n            name: \"Jane Smith\",\n            isAdmin: true,\n            amountPaid: 850\n        },\n        {\n            id: 2,\n            name: \"John Doe\",\n            isAdmin: false,\n            amountPaid: 700\n        },\n        {\n            id: 3,\n            name: \"Alice Johnson\",\n            isAdmin: false,\n            amountPaid: 600\n        },\n        {\n            id: 4,\n            name: \"Bob Williams\",\n            isAdmin: false,\n            amountPaid: 0\n        },\n        {\n            id: 5,\n            name: \"Carol Davis\",\n            isAdmin: false,\n            amountPaid: 0\n        }\n    ],\n    amountPaid: 2150,\n    totalAmount: 3500,\n    expiresIn: \"5 days\",\n    status: \"manufacturing\",\n    manufacturingProgress: 65,\n    manufacturingUpdates: [\n        {\n            id: 1,\n            date: \"2023-03-15\",\n            title: \"Production Started\",\n            description: \"Materials sourced and production has begun.\",\n            images: [\n                \"/images/placeholder.png\"\n            ]\n        },\n        {\n            id: 2,\n            date: \"2023-03-18\",\n            title: \"Frame Assembly\",\n            description: \"Wooden frames are assembled and ready for upholstery.\",\n            images: [\n                \"/images/placeholder.png\"\n            ]\n        },\n        {\n            id: 3,\n            date: \"2023-03-21\",\n            title: \"Upholstery Progress\",\n            description: \"Leather upholstery is being applied to the frames.\",\n            images: [\n                \"/images/placeholder.png\"\n            ]\n        }\n    ],\n    messages: [\n        {\n            id: 1,\n            user: \"Jane Smith\",\n            content: \"Welcome everyone to our group buy!\",\n            timestamp: \"2023-03-10 10:23\"\n        },\n        {\n            id: 2,\n            user: \"John Doe\",\n            content: \"Thanks for organizing this!\",\n            timestamp: \"2023-03-10 10:45\"\n        },\n        {\n            id: 3,\n            user: \"Alice Johnson\",\n            content: \"I added a leather sofa set to our product suggestions. What do you all think?\",\n            timestamp: \"2023-03-10 11:15\",\n            productRef: 1\n        },\n        {\n            id: 4,\n            user: \"Jane Smith\",\n            content: \"I like it but it's a bit pricey. I found this fabric sectional that might be more budget-friendly.\",\n            timestamp: \"2023-03-10 11:30\",\n            productRef: 2\n        },\n        {\n            id: 5,\n            user: \"Bob Williams\",\n            content: \"I saw this custom sofa at a local craftsman's shop. Uploading a photo I took.\",\n            timestamp: \"2023-03-10 12:15\",\n            productRef: 3,\n            attachment: \"/images/placeholder.png\"\n        }\n    ]\n};\n// Helper function to get the appropriate default tab based on group stage\nconst getDefaultTab = (stage)=>{\n    switch(stage){\n        case \"suggestion\":\n            return \"suggestions\";\n        case \"discussion\":\n            return \"discussion\";\n        case \"quote\":\n            return \"quotes\";\n        case \"payment\":\n            return \"payment\";\n        case \"manufacturing\":\n            return \"manufacturing\";\n        case \"shipping\":\n            return \"shipping\";\n        default:\n            return \"discussion\";\n    }\n};\nfunction GroupDetail(param) {\n    let { params } = param;\n    var _groupData_suggestedProducts_find, _groupData_suggestedProducts_find1, _groupData_suggestedProducts_find2, _groupData_suggestedProducts_find3, _groupData_suggestedProducts_find4;\n    _s();\n    // In a real app, you would fetch the group data based on params.id\n    const defaultTab = getDefaultTab(groupData.stage);\n    const [selectedProductRef, setSelectedProductRef] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showProductSelector, setShowProductSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messageText, setMessageText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleSendMessage = ()=>{\n        // In a real app, this would send the message to the API\n        console.log(\"Sending message:\", {\n            content: messageText,\n            productRef: selectedProductRef\n        });\n        // Reset the form\n        setMessageText(\"\");\n        setSelectedProductRef(null);\n    };\n    const handleSelectProduct = (productId)=>{\n        setSelectedProductRef(productId);\n        setShowProductSelector(false);\n    };\n    const renderStageIndicator = ()=>{\n        const stages = [\n            {\n                id: \"suggestion\",\n                label: \"Suggestions\"\n            },\n            {\n                id: \"discussion\",\n                label: \"Discussion\"\n            },\n            {\n                id: \"quote\",\n                label: \"Quotes\"\n            },\n            {\n                id: \"payment\",\n                label: \"Payment\"\n            },\n            {\n                id: \"manufacturing\",\n                label: \"Manufacturing\"\n            },\n            {\n                id: \"shipping\",\n                label: \"Shipping\"\n            }\n        ];\n        const currentIndex = stages.findIndex((s)=>s.id === groupData.stage);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-sm font-medium mb-2\",\n                    children: \"Current Stage\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full bg-secondary rounded-full h-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-primary h-2 rounded-full transition-all duration-500 ease-in-out\",\n                        style: {\n                            width: \"\".concat((currentIndex + 1) / stages.length * 100, \"%\")\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between mt-1 text-xs\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-primary font-medium\",\n                            children: stages[currentIndex].label\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-muted-foreground\",\n                            children: [\n                                \"Step \",\n                                currentIndex + 1,\n                                \" of \",\n                                stages.length\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 264,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n            lineNumber: 256,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layouts_mobile_layout__WEBPACK_IMPORTED_MODULE_2__.MobileLayout, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative bg-primary text-primary-foreground p-4 pb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                        href: \"/groups\",\n                        className: \"flex items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageSquare_ShoppingBag_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-1\",\n                                children: \"Back to Groups\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-fluid-xl font-bold\",\n                        children: groupData.name\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mt-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageSquare_ShoppingBag_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                size: 16,\n                                className: \"mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    groupData.members.length,\n                                    \" members\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mx-2\",\n                                children: \"•\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageSquare_ShoppingBag_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                size: 16,\n                                className: \"mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Expires in \",\n                                    groupData.expiresIn\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 278,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"-mt-6 mb-6 relative z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-4\",\n                            children: renderStageIndicator()\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                        defaultValue: defaultTab,\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsList, {\n                                className: \"w-full grid grid-cols-3 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                        value: \"suggestions\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageSquare_ShoppingBag_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"Suggestions\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                        value: \"discussion\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageSquare_ShoppingBag_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"Discussion\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                        value: \"quotes\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FileText, {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"Quotes\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsList, {\n                                className: \"w-full grid grid-cols-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                        value: \"manufacturing\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageSquare_ShoppingBag_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"Manufacturing\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                        value: \"members\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageSquare_ShoppingBag_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"Members\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                        value: \"payment\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageSquare_ShoppingBag_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"Payment\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                                value: \"suggestions\",\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium\",\n                                                children: \"Product Suggestions\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_group_suggestion_form__WEBPACK_IMPORTED_MODULE_8__.GroupSuggestionForm, {\n                                                    groupId: params.id\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: groupData.suggestedProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                className: \"overflow-hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-1/3 bg-muted\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full h-full relative\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-24 bg-muted flex items-center justify-center rounded\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-muted-foreground\",\n                                                                        children: \"Product Image\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 345,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 344,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2/3 p-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-medium\",\n                                                                            children: product.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 353,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs px-1.5 py-0.5 rounded \".concat(product.source === \"internal\" ? \"bg-primary/10 text-primary\" : \"bg-secondary text-secondary-foreground\"),\n                                                                            children: product.source === \"internal\" ? \"Catalog\" : \"External\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 354,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 352,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground mb-1\",\n                                                                    children: product.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 366,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center mt-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: product.price > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: [\n                                                                                \"$\",\n                                                                                product.price\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 372,\n                                                                            columnNumber: 29\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-muted-foreground\",\n                                                                            children: \"Price not available\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 376,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 370,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 369,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 351,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, product.id, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                                value: \"discussion\",\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: groupData.messages.map((message)=>{\n                                            var _groupData_suggestedProducts_find, _groupData_suggestedProducts_find1, _groupData_suggestedProducts_find2, _groupData_suggestedProducts_find3, _groupData_suggestedProducts_find4;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-muted rounded-lg p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: message.user\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 395,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: message.timestamp\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 396,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: message.content\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    message.productRef && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 p-2 bg-background rounded border border-border flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-16 h-16 bg-muted rounded overflow-hidden flex items-center justify-center mr-3\",\n                                                                children: ((_groupData_suggestedProducts_find = groupData.suggestedProducts.find((p)=>p.id === message.productRef)) === null || _groupData_suggestedProducts_find === void 0 ? void 0 : _groupData_suggestedProducts_find.image) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative w-full h-full\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                        src: ((_groupData_suggestedProducts_find1 = groupData.suggestedProducts.find((p)=>p.id === message.productRef)) === null || _groupData_suggestedProducts_find1 === void 0 ? void 0 : _groupData_suggestedProducts_find1.image) || \"/images/placeholder.png\",\n                                                                        alt: ((_groupData_suggestedProducts_find2 = groupData.suggestedProducts.find((p)=>p.id === message.productRef)) === null || _groupData_suggestedProducts_find2 === void 0 ? void 0 : _groupData_suggestedProducts_find2.name) || \"Product\",\n                                                                        fill: true,\n                                                                        className: \"object-cover\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 409,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 408,\n                                                                    columnNumber: 27\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageSquare_ShoppingBag_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-6 w-6 text-muted-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 425,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 404,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1 text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: (_groupData_suggestedProducts_find3 = groupData.suggestedProducts.find((p)=>p.id === message.productRef)) === null || _groupData_suggestedProducts_find3 === void 0 ? void 0 : _groupData_suggestedProducts_find3.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 429,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-muted-foreground font-medium\",\n                                                                        children: [\n                                                                            \"$\",\n                                                                            ((_groupData_suggestedProducts_find4 = groupData.suggestedProducts.find((p)=>p.id === message.productRef)) === null || _groupData_suggestedProducts_find4 === void 0 ? void 0 : _groupData_suggestedProducts_find4.price) || \"Price unavailable\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 436,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 428,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    message.attachment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full max-w-[200px] h-[150px] bg-muted rounded-md overflow-hidden relative\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-0 flex items-center justify-center text-muted-foreground\",\n                                                                children: \"Image Attachment\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 449,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, message.id, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 17\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"icon\",\n                                                            className: \"rounded-full\",\n                                                            type: \"button\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"file-upload\",\n                                                                className: \"cursor-pointer\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        id: \"file-upload\",\n                                                                        type: \"file\",\n                                                                        accept: \"image/*\",\n                                                                        className: \"sr-only\",\n                                                                        \"aria-label\": \"Upload image\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 469,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageSquare_ShoppingBag_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 476,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 468,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 462,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"icon\",\n                                                            className: \"rounded-full\",\n                                                            type: \"button\",\n                                                            onClick: ()=>setShowProductSelector(true),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageSquare_ShoppingBag_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 486,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 479,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        selectedProductRef && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-background rounded border border-border flex items-center p-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-12 h-12 bg-muted rounded overflow-hidden flex items-center justify-center mr-3\",\n                                                                        children: ((_groupData_suggestedProducts_find = groupData.suggestedProducts.find((p)=>p.id === selectedProductRef)) === null || _groupData_suggestedProducts_find === void 0 ? void 0 : _groupData_suggestedProducts_find.image) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"relative w-full h-full\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                                src: ((_groupData_suggestedProducts_find1 = groupData.suggestedProducts.find((p)=>p.id === selectedProductRef)) === null || _groupData_suggestedProducts_find1 === void 0 ? void 0 : _groupData_suggestedProducts_find1.image) || \"/images/placeholder.png\",\n                                                                                alt: ((_groupData_suggestedProducts_find2 = groupData.suggestedProducts.find((p)=>p.id === selectedProductRef)) === null || _groupData_suggestedProducts_find2 === void 0 ? void 0 : _groupData_suggestedProducts_find2.name) || \"Product\",\n                                                                                fill: true,\n                                                                                className: \"object-cover\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 497,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 496,\n                                                                            columnNumber: 29\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageSquare_ShoppingBag_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-5 w-5 text-muted-foreground\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 513,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 492,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1 text-sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"font-medium\",\n                                                                                children: (_groupData_suggestedProducts_find3 = groupData.suggestedProducts.find((p)=>p.id === selectedProductRef)) === null || _groupData_suggestedProducts_find3 === void 0 ? void 0 : _groupData_suggestedProducts_find3.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 517,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-muted-foreground\",\n                                                                                children: [\n                                                                                    \"$\",\n                                                                                    ((_groupData_suggestedProducts_find4 = groupData.suggestedProducts.find((p)=>p.id === selectedProductRef)) === null || _groupData_suggestedProducts_find4 === void 0 ? void 0 : _groupData_suggestedProducts_find4.price) || \"Price unavailable\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 524,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 516,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-6 w-6 rounded-full ml-1\",\n                                                                        type: \"button\",\n                                                                        onClick: ()=>setSelectedProductRef(null),\n                                                                        children: \"\\xd7\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 531,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 491,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 490,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"Type your message...\",\n                                                            className: \"flex-1 h-10 rounded-l-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n                                                            value: messageText,\n                                                            onChange: (e)=>setMessageText(e.target.value),\n                                                            onKeyDown: (e)=>{\n                                                                if (e.key === \"Enter\" && !e.shiftKey) {\n                                                                    e.preventDefault();\n                                                                    handleSendMessage();\n                                                                }\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 545,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            className: \"rounded-l-none\",\n                                                            onClick: handleSendMessage,\n                                                            children: \"Send\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 558,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                                        open: showProductSelector,\n                                        onOpenChange: setShowProductSelector,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                                            className: \"sm:max-w-[425px]\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogHeader, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTitle, {\n                                                            children: \"Select a Product\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 574,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogDescription, {\n                                                            children: \"Choose a product to reference in your message\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 575,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 573,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3 mt-4 max-h-[300px] overflow-y-auto\",\n                                                    children: groupData.suggestedProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-3 border rounded-md cursor-pointer hover:bg-muted flex items-center\",\n                                                            onClick: ()=>handleSelectProduct(product.id),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-16 h-16 bg-muted rounded overflow-hidden mr-3 flex items-center justify-center\",\n                                                                    children: product.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative w-full h-full\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                                            src: product.image || \"/images/placeholder.png\",\n                                                                            alt: product.name,\n                                                                            fill: true,\n                                                                            className: \"object-cover\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 589,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 588,\n                                                                        columnNumber: 27\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageSquare_ShoppingBag_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-6 w-6 text-muted-foreground\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 597,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 586,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-medium\",\n                                                                            children: product.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 601,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: product.price > 0 ? \"$\".concat(product.price) : \"Price unavailable\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 602,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 600,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, product.id, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 581,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 579,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 572,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 568,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                                value: \"quotes\",\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium\",\n                                                children: \"Merchant Quotes\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 620,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QuoteRequest, {\n                                                groupId: params.id,\n                                                products: groupData.suggestedProducts\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 621,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 619,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QuoteComparison, {\n                                        groupId: params.id,\n                                        productQuotes: [\n                                            {\n                                                productName: \"Premium Leather Sofa Set\",\n                                                productId: 1,\n                                                quotes: groupData.quotes.filter((q)=>q.productId === 1)\n                                            },\n                                            {\n                                                productName: \"Modern Fabric Sectional\",\n                                                productId: 2,\n                                                quotes: groupData.quotes.filter((q)=>q.productId === 2)\n                                            }\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 626,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 618,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                                value: \"payment\",\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                className: \"pb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                    className: \"text-lg\",\n                                                    children: \"Payment Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 647,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 646,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full bg-secondary rounded-full h-3 mb-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-primary h-3 rounded-full\",\n                                                            style: {\n                                                                width: \"\".concat(groupData.amountPaid / groupData.totalAmount * 100, \"%\")\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 651,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 650,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"$\",\n                                                                    groupData.amountPaid,\n                                                                    \" raised\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 661,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"$\",\n                                                                    groupData.totalAmount,\n                                                                    \" goal\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 662,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 660,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-muted-foreground text-center\",\n                                                        children: [\n                                                            \"$\",\n                                                            groupData.totalAmount - groupData.amountPaid,\n                                                            \" remaining\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 664,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 649,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardFooter, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                                    href: \"/groups/\".concat(params.id, \"/payment\"),\n                                                    className: \"w-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        className: \"w-full\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageSquare_ShoppingBag_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 671,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Make a Payment\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 670,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 669,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 668,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 645,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium mb-2\",\n                                        children: \"Payment History\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 678,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: groupData.members.filter((member)=>member.amountPaid > 0).map((member)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-muted p-3 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-8 h-8 rounded-full bg-secondary flex items-center justify-center\",\n                                                                    children: member.name.charAt(0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 686,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: member.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 690,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-muted-foreground\",\n                                                                            children: new Date().toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 691,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 689,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 685,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: [\n                                                                \"$\",\n                                                                member.amountPaid\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 696,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 684,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, member.id, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 683,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 679,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 644,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                                value: \"manufacturing\",\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Manufacturing Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 706,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-primary font-medium capitalize\",\n                                                        children: groupData.status\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 707,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 705,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-secondary rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-primary h-2 rounded-full\",\n                                                    style: {\n                                                        width: \"\".concat(groupData.manufacturingProgress, \"%\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 712,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 711,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mt-1 text-xs text-muted-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Production Started\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 718,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Ready for Shipping\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 719,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 717,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 704,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium\",\n                                                children: \"Latest Updates\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 724,\n                                                columnNumber: 15\n                                            }, this),\n                                            groupData.manufacturingUpdates.map((update)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                    className: \"overflow-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium\",\n                                                                        children: update.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 729,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: update.date\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 730,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 728,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm mb-2\",\n                                                                children: update.description\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 734,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-24 bg-muted flex items-center justify-center rounded\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-muted-foreground\",\n                                                                    children: \"Update Image\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 736,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 735,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 727,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, update.id, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 726,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 723,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 703,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                                value: \"members\",\n                                className: \"mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: groupData.members.map((member)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 rounded-full bg-secondary flex items-center justify-center\",\n                                                            children: member.name.charAt(0)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 754,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"ml-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: member.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 759,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        member.isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-2 px-1.5 py-0.5 bg-primary/10 text-primary text-xs rounded\",\n                                                                            children: \"Admin\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 761,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 758,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-muted-foreground\",\n                                                                    children: member.amountPaid > 0 ? \"Paid $\".concat(member.amountPaid) : \"No payment yet\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 766,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 757,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 753,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-secondary text-secondary-foreground rounded-full flex items-center justify-center\",\n                                                    children: [\n                                                        Math.round(member.amountPaid / (groupData.totalAmount / groupData.members.length) * 100),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 773,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, member.id, true, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 749,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 747,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 746,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n        lineNumber: 277,\n        columnNumber: 5\n    }, this);\n}\n_s(GroupDetail, \"calCzYaF7I7m8WLjU/oh5r87n+I=\");\n_c = GroupDetail;\nvar _c;\n$RefreshReg$(_c, \"GroupDetail\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/groups/[id]/page.tsx\n"));

/***/ })

});