"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/groups/[id]/page",{

/***/ "(app-pages-browser)/./app/groups/[id]/page.tsx":
/*!**********************************!*\
  !*** ./app/groups/[id]/page.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GroupDetail; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layouts_mobile_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layouts/mobile-layout */ \"(app-pages-browser)/./components/layouts/mobile-layout.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smile.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/reply.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _app_components_group_suggestion_form__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/components/group-suggestion-form */ \"(app-pages-browser)/./app/components/group-suggestion-form.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// Removed quote components - quotes functionality has been removed\n\n// Mock data - would be fetched based on [id] in real app\nconst groupData = {\n    id: 1,\n    name: \"Living Room Remodel Group\",\n    description: \"A collective purchase for premium living room furniture at wholesale prices.\",\n    stage: \"suggestion\",\n    suggestedProducts: [\n        {\n            id: 1,\n            name: \"Premium Leather Sofa Set\",\n            price: 3500,\n            image: \"/images/placeholder.png\",\n            description: \"Genuine leather sofa set with matching ottoman\",\n            merchant: \"Luxury Furniture Co.\",\n            source: \"internal\",\n            reactions: [\n                {\n                    id: 1,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 11:20\"\n                },\n                {\n                    id: 2,\n                    userId: 2,\n                    userName: \"John Doe\",\n                    emoji: \"❤️\",\n                    timestamp: \"2023-03-10 11:25\"\n                },\n                {\n                    id: 3,\n                    userId: 3,\n                    userName: \"Alice Johnson\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 11:30\"\n                }\n            ],\n            consensusScore: 85,\n            threadCount: 3\n        },\n        {\n            id: 2,\n            name: \"Modern Fabric Sectional\",\n            price: 2800,\n            image: \"/images/placeholder.png\",\n            description: \"L-shaped sectional with chaise lounge in premium fabric\",\n            merchant: \"Contemporary Home\",\n            source: \"internal\",\n            reactions: [\n                {\n                    id: 4,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 11:35\"\n                },\n                {\n                    id: 5,\n                    userId: 4,\n                    userName: \"Bob Williams\",\n                    emoji: \"\\uD83E\\uDD14\",\n                    timestamp: \"2023-03-10 11:40\"\n                },\n                {\n                    id: 8,\n                    userId: 5,\n                    userName: \"Carol Davis\",\n                    emoji: \"\\uD83D\\uDC4E\",\n                    timestamp: \"2023-03-10 11:45\"\n                },\n                {\n                    id: 9,\n                    userId: 6,\n                    userName: \"Mike Johnson\",\n                    emoji: \"\\uD83D\\uDCB8\",\n                    timestamp: \"2023-03-10 11:50\"\n                }\n            ],\n            consensusScore: 45,\n            threadCount: 2\n        },\n        {\n            id: 3,\n            name: \"Custom Wood Frame Sofa\",\n            price: 0,\n            image: \"/images/placeholder.png\",\n            description: \"Hand-crafted wooden frame sofa with custom upholstery\",\n            merchant: null,\n            source: \"external\",\n            reactions: [\n                {\n                    id: 6,\n                    userId: 5,\n                    userName: \"Carol Davis\",\n                    emoji: \"\\uD83D\\uDE0D\",\n                    timestamp: \"2023-03-10 12:20\"\n                },\n                {\n                    id: 7,\n                    userId: 2,\n                    userName: \"John Doe\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 12:25\"\n                }\n            ],\n            consensusScore: 70,\n            threadCount: 1\n        }\n    ],\n    selectedProduct: null,\n    product: {\n        name: \"Premium Leather Sofa Set\",\n        price: 3500,\n        image: \"/images/placeholder.png\"\n    },\n    members: [\n        {\n            id: 1,\n            name: \"Jane Smith\",\n            isAdmin: true,\n            amountPaid: 850\n        },\n        {\n            id: 2,\n            name: \"John Doe\",\n            isAdmin: false,\n            amountPaid: 700\n        },\n        {\n            id: 3,\n            name: \"Alice Johnson\",\n            isAdmin: false,\n            amountPaid: 600\n        },\n        {\n            id: 4,\n            name: \"Bob Williams\",\n            isAdmin: false,\n            amountPaid: 0\n        },\n        {\n            id: 5,\n            name: \"Carol Davis\",\n            isAdmin: false,\n            amountPaid: 0\n        }\n    ],\n    amountPaid: 2150,\n    totalAmount: 3500,\n    expiresIn: \"5 days\",\n    status: \"manufacturing\",\n    manufacturingProgress: 65,\n    manufacturingUpdates: [\n        {\n            id: 1,\n            date: \"2023-03-15\",\n            title: \"Production Started\",\n            description: \"Materials sourced and production has begun.\",\n            images: [\n                \"/images/placeholder.png\"\n            ]\n        },\n        {\n            id: 2,\n            date: \"2023-03-18\",\n            title: \"Frame Assembly\",\n            description: \"Wooden frames are assembled and ready for upholstery.\",\n            images: [\n                \"/images/placeholder.png\"\n            ]\n        },\n        {\n            id: 3,\n            date: \"2023-03-21\",\n            title: \"Upholstery Progress\",\n            description: \"Leather upholstery is being applied to the frames.\",\n            images: [\n                \"/images/placeholder.png\"\n            ]\n        }\n    ],\n    messages: [\n        {\n            id: 1,\n            user: \"Jane Smith\",\n            userId: 1,\n            content: \"Welcome everyone to our group buy!\",\n            timestamp: \"2023-03-10 10:23\",\n            type: \"text\",\n            reactions: [\n                {\n                    id: 1,\n                    userId: 2,\n                    userName: \"John Doe\",\n                    emoji: \"\\uD83D\\uDC4B\",\n                    timestamp: \"2023-03-10 10:25\"\n                },\n                {\n                    id: 2,\n                    userId: 3,\n                    userName: \"Alice Johnson\",\n                    emoji: \"\\uD83C\\uDF89\",\n                    timestamp: \"2023-03-10 10:26\"\n                }\n            ]\n        },\n        {\n            id: 2,\n            user: \"John Doe\",\n            userId: 2,\n            content: \"Thanks for organizing this!\",\n            timestamp: \"2023-03-10 10:45\",\n            type: \"text\",\n            reactions: [\n                {\n                    id: 3,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"❤️\",\n                    timestamp: \"2023-03-10 10:46\"\n                }\n            ]\n        },\n        {\n            id: 3,\n            user: \"Alice Johnson\",\n            userId: 3,\n            content: \"I added a Premium Leather Sofa Set to our product suggestions. What do you all think?\",\n            timestamp: \"2023-03-10 11:15\",\n            type: \"product-suggestion\",\n            productRef: 1,\n            detectedProducts: [\n                {\n                    text: \"Premium Leather Sofa Set\",\n                    startIndex: 9,\n                    endIndex: 33,\n                    suggestedProductId: 1,\n                    confidence: 0.95\n                }\n            ],\n            reactions: [\n                {\n                    id: 4,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 11:20\"\n                },\n                {\n                    id: 5,\n                    userId: 2,\n                    userName: \"John Doe\",\n                    emoji: \"\\uD83E\\uDD14\",\n                    timestamp: \"2023-03-10 11:22\"\n                }\n            ]\n        },\n        {\n            id: 4,\n            user: \"Jane Smith\",\n            userId: 1,\n            content: \"I like it but it's a bit pricey. I found this fabric sectional that might be more budget-friendly.\",\n            timestamp: \"2023-03-10 11:30\",\n            type: \"product-suggestion\",\n            productRef: 2,\n            parentMessageId: 3,\n            threadId: \"product-1-discussion\",\n            detectedProducts: [\n                {\n                    text: \"fabric sectional\",\n                    startIndex: 55,\n                    endIndex: 70,\n                    suggestedProductId: 2,\n                    confidence: 0.88\n                }\n            ],\n            reactions: [\n                {\n                    id: 6,\n                    userId: 4,\n                    userName: \"Bob Williams\",\n                    emoji: \"\\uD83D\\uDCB0\",\n                    timestamp: \"2023-03-10 11:35\"\n                },\n                {\n                    id: 10,\n                    userId: 5,\n                    userName: \"Carol Davis\",\n                    emoji: \"\\uD83D\\uDC4E\",\n                    timestamp: \"2023-03-10 11:37\"\n                }\n            ]\n        },\n        {\n            id: 5,\n            user: \"Bob Williams\",\n            userId: 4,\n            content: \"I saw this custom sofa at a local craftsman's shop. Uploading a photo I took.\",\n            timestamp: \"2023-03-10 12:15\",\n            type: \"product-suggestion\",\n            productRef: 3,\n            attachment: \"/images/placeholder.png\",\n            detectedProducts: [\n                {\n                    text: \"custom sofa\",\n                    startIndex: 12,\n                    endIndex: 23,\n                    suggestedProductId: 3,\n                    confidence: 0.92\n                }\n            ],\n            reactions: [\n                {\n                    id: 7,\n                    userId: 5,\n                    userName: \"Carol Davis\",\n                    emoji: \"\\uD83D\\uDCF8\",\n                    timestamp: \"2023-03-10 12:20\"\n                },\n                {\n                    id: 8,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"\\uD83D\\uDC40\",\n                    timestamp: \"2023-03-10 12:22\"\n                }\n            ]\n        },\n        {\n            id: 6,\n            user: \"John Doe\",\n            userId: 2,\n            content: \"The leather sofa looks amazing! How's the delivery time?\",\n            timestamp: \"2023-03-10 12:30\",\n            type: \"text\",\n            parentMessageId: 3,\n            threadId: \"product-1-discussion\",\n            detectedProducts: [\n                {\n                    text: \"leather sofa\",\n                    startIndex: 4,\n                    endIndex: 16,\n                    suggestedProductId: 1,\n                    confidence: 0.9\n                }\n            ]\n        },\n        {\n            id: 7,\n            user: \"Carol Davis\",\n            userId: 5,\n            content: \"I'm really interested in the custom wood frame option. Can we get more details?\",\n            timestamp: \"2023-03-10 13:00\",\n            type: \"text\",\n            parentMessageId: 5,\n            threadId: \"product-3-discussion\",\n            detectedProducts: [\n                {\n                    text: \"custom wood frame\",\n                    startIndex: 30,\n                    endIndex: 47,\n                    suggestedProductId: 3,\n                    confidence: 0.85\n                }\n            ]\n        }\n    ]\n};\n// Helper function to get the appropriate default tab based on group stage\nconst getDefaultTab = (stage)=>{\n    switch(stage){\n        case \"suggestion\":\n            return \"discussion\"; // suggestions are now part of discussion\n        case \"discussion\":\n            return \"discussion\";\n        case \"payment\":\n            return \"payment\";\n        case \"manufacturing\":\n            return \"manufacturing\";\n        case \"shipping\":\n            return \"shipping\";\n        default:\n            return \"discussion\";\n    }\n};\nfunction GroupDetail(param) {\n    let { params } = param;\n    var _localProducts_find, _localProducts_find1, _localProducts_find2, _localProducts_find3, _localProducts_find4;\n    _s();\n    // In a real app, you would fetch the group data based on params.id\n    const defaultTab = getDefaultTab(groupData.stage);\n    const [selectedProductRef, setSelectedProductRef] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showProductSelector, setShowProductSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProductsOverview, setShowProductsOverview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messageText, setMessageText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Phase 2 state management\n    const [expandedThreads, setExpandedThreads] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [showEmojiPicker, setShowEmojiPicker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [replyingTo, setReplyingTo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentUserId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1); // Mock current user - Jane Smith\n    // Local state for dynamic updates (in a real app, this would be managed by a state management system)\n    const [localMessages, setLocalMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(groupData.messages);\n    const [localProducts, setLocalProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(groupData.suggestedProducts);\n    const [replyText, setReplyText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleSendMessage = ()=>{\n        if (!messageText.trim()) return;\n        const newMessage = {\n            id: Date.now(),\n            user: \"Jane Smith\",\n            userId: currentUserId,\n            content: messageText,\n            timestamp: new Date().toLocaleString(),\n            type: selectedProductRef ? \"product-suggestion\" : \"text\",\n            productRef: selectedProductRef || undefined,\n            reactions: []\n        };\n        setLocalMessages((prev)=>[\n                ...prev,\n                newMessage\n            ]);\n        setMessageText(\"\");\n        setSelectedProductRef(null);\n    };\n    const handleSendReply = (parentMessageId)=>{\n        if (!replyText.trim()) return;\n        const parentMessage = localMessages.find((m)=>m.id === parentMessageId);\n        const newReply = {\n            id: Date.now(),\n            user: \"Jane Smith\",\n            userId: currentUserId,\n            content: replyText,\n            timestamp: new Date().toLocaleString(),\n            type: \"text\",\n            parentMessageId: parentMessageId,\n            threadId: (parentMessage === null || parentMessage === void 0 ? void 0 : parentMessage.threadId) || \"thread-\".concat(parentMessageId),\n            reactions: []\n        };\n        console.log(\"Creating reply:\", newReply);\n        console.log(\"Current messages before:\", localMessages.length);\n        setLocalMessages((prev)=>{\n            const updated = [\n                ...prev,\n                newReply\n            ];\n            console.log(\"Updated messages after:\", updated.length);\n            return updated;\n        });\n        // Auto-expand the thread to show the new reply\n        setExpandedThreads((prev)=>{\n            const newSet = new Set(prev);\n            newSet.add(\"thread-\".concat(parentMessageId));\n            return newSet;\n        });\n        setReplyText(\"\");\n        setReplyingTo(null);\n    };\n    const handleSelectProduct = (productId)=>{\n        setSelectedProductRef(productId);\n        setShowProductSelector(false);\n    };\n    // Phase 2 helper functions\n    const handleAddReaction = (emoji, messageId, productId)=>{\n        if (messageId) {\n            var _message_reactions;\n            // Check if user already has a reaction on this message\n            const message = localMessages.find((m)=>m.id === messageId);\n            const existingReaction = message === null || message === void 0 ? void 0 : (_message_reactions = message.reactions) === null || _message_reactions === void 0 ? void 0 : _message_reactions.find((r)=>r.userId === currentUserId);\n            setLocalMessages((prev)=>prev.map((msg)=>{\n                    if (msg.id === messageId) {\n                        let newReactions = msg.reactions || [];\n                        if (existingReaction) {\n                            // Replace existing reaction\n                            newReactions = newReactions.map((r)=>r.userId === currentUserId ? {\n                                    ...r,\n                                    emoji,\n                                    timestamp: new Date().toLocaleString()\n                                } : r);\n                        } else {\n                            // Add new reaction\n                            const newReaction = {\n                                id: Date.now(),\n                                userId: currentUserId,\n                                userName: \"Jane Smith\",\n                                emoji,\n                                timestamp: new Date().toLocaleString()\n                            };\n                            newReactions = [\n                                ...newReactions,\n                                newReaction\n                            ];\n                        }\n                        return {\n                            ...msg,\n                            reactions: newReactions\n                        };\n                    }\n                    return msg;\n                }));\n        }\n        if (productId) {\n            var _product_reactions;\n            // Check if user already has a reaction on this product\n            const product = localProducts.find((p)=>p.id === productId);\n            const existingReaction = product === null || product === void 0 ? void 0 : (_product_reactions = product.reactions) === null || _product_reactions === void 0 ? void 0 : _product_reactions.find((r)=>r.userId === currentUserId);\n            setLocalProducts((prev)=>prev.map((product)=>{\n                    if (product.id === productId) {\n                        let newReactions = product.reactions || [];\n                        if (existingReaction) {\n                            // Replace existing reaction\n                            newReactions = newReactions.map((r)=>r.userId === currentUserId ? {\n                                    ...r,\n                                    emoji,\n                                    timestamp: new Date().toLocaleString()\n                                } : r);\n                        } else {\n                            // Add new reaction\n                            const newReaction = {\n                                id: Date.now(),\n                                userId: currentUserId,\n                                userName: \"Jane Smith\",\n                                emoji,\n                                timestamp: new Date().toLocaleString()\n                            };\n                            newReactions = [\n                                ...newReactions,\n                                newReaction\n                            ];\n                        }\n                        return {\n                            ...product,\n                            reactions: newReactions,\n                            consensusScore: calculateConsensusScore(newReactions)\n                        };\n                    }\n                    return product;\n                }));\n        }\n        setShowEmojiPicker(null);\n    };\n    const handleDeleteMessage = (messageId)=>{\n        // Remove the message and any replies to it\n        setLocalMessages((prev)=>prev.filter((msg)=>msg.id !== messageId && msg.parentMessageId !== messageId));\n    };\n    const handleDeleteReaction = (reactionId, messageId, productId)=>{\n        if (messageId) {\n            setLocalMessages((prev)=>prev.map((msg)=>{\n                    var _msg_reactions;\n                    return msg.id === messageId ? {\n                        ...msg,\n                        reactions: ((_msg_reactions = msg.reactions) === null || _msg_reactions === void 0 ? void 0 : _msg_reactions.filter((r)=>r.id !== reactionId)) || []\n                    } : msg;\n                }));\n        }\n        if (productId) {\n            setLocalProducts((prev)=>prev.map((product)=>{\n                    if (product.id === productId) {\n                        var _product_reactions;\n                        const newReactions = ((_product_reactions = product.reactions) === null || _product_reactions === void 0 ? void 0 : _product_reactions.filter((r)=>r.id !== reactionId)) || [];\n                        return {\n                            ...product,\n                            reactions: newReactions,\n                            consensusScore: calculateConsensusScore(newReactions)\n                        };\n                    }\n                    return product;\n                }));\n        }\n    };\n    const toggleThread = (threadId)=>{\n        const newExpanded = new Set(expandedThreads);\n        if (newExpanded.has(threadId)) {\n            newExpanded.delete(threadId);\n        } else {\n            newExpanded.add(threadId);\n        }\n        setExpandedThreads(newExpanded);\n    };\n    const getThreadMessages = (parentMessageId)=>{\n        return localMessages.filter((msg)=>msg.parentMessageId === parentMessageId);\n    };\n    const getMainMessages = ()=>{\n        return localMessages.filter((msg)=>!msg.parentMessageId);\n    };\n    const getConsensusColor = (score)=>{\n        if (score >= 80) return \"text-green-600\";\n        if (score >= 60) return \"text-yellow-600\";\n        return \"text-red-600\";\n    };\n    const calculateConsensusScore = (reactions)=>{\n        if (!reactions || reactions.length === 0) return 0;\n        const positiveEmojis = [\n            \"\\uD83D\\uDC4D\",\n            \"❤️\",\n            \"\\uD83D\\uDE0D\",\n            \"\\uD83C\\uDF89\",\n            \"\\uD83D\\uDD25\",\n            \"\\uD83D\\uDCA1\",\n            \"✨\"\n        ];\n        const negativeEmojis = [\n            \"\\uD83D\\uDC4E\",\n            \"\\uD83D\\uDE15\",\n            \"❌\",\n            \"\\uD83D\\uDCB8\",\n            \"⚠️\",\n            \"\\uD83D\\uDEAB\"\n        ];\n        // neutralEmojis: [\"🤔\", \"👀\", \"📸\", \"👋\", \"💰\"] - treated as neutral (0.5 weight)\n        let positiveCount = 0;\n        let negativeCount = 0;\n        let neutralCount = 0;\n        reactions.forEach((reaction)=>{\n            if (positiveEmojis.includes(reaction.emoji)) {\n                positiveCount++;\n            } else if (negativeEmojis.includes(reaction.emoji)) {\n                negativeCount++;\n            } else {\n                neutralCount++;\n            }\n        });\n        const totalReactions = reactions.length;\n        const positiveWeight = positiveCount * 1.0;\n        const neutralWeight = neutralCount * 0.5;\n        const negativeWeight = negativeCount * 0.0;\n        const weightedScore = (positiveWeight + neutralWeight + negativeWeight) / totalReactions;\n        return Math.round(weightedScore * 100);\n    };\n    const renderDetectedProducts = (content, detectedProducts)=>{\n        if (!detectedProducts || detectedProducts.length === 0) {\n            return content;\n        }\n        let result = content;\n        let offset = 0;\n        detectedProducts.sort((a, b)=>a.startIndex - b.startIndex).forEach((detected)=>{\n            const start = detected.startIndex + offset;\n            const end = detected.endIndex + offset;\n            const productName = result.substring(start, end);\n            const replacement = '<span class=\"bg-blue-100 text-blue-800 px-1 rounded cursor-pointer hover:bg-blue-200\" data-product-id=\"'.concat(detected.suggestedProductId, '\">').concat(productName, \"</span>\");\n            result = result.substring(0, start) + replacement + result.substring(end);\n            offset += replacement.length - productName.length;\n        });\n        return result;\n    };\n    const renderStageIndicator = ()=>{\n        const stages = [\n            {\n                id: \"suggestion\",\n                label: \"Suggestions\"\n            },\n            {\n                id: \"discussion\",\n                label: \"Discussion\"\n            },\n            {\n                id: \"payment\",\n                label: \"Payment\"\n            },\n            {\n                id: \"manufacturing\",\n                label: \"Manufacturing\"\n            },\n            {\n                id: \"shipping\",\n                label: \"Shipping\"\n            }\n        ];\n        const currentIndex = stages.findIndex((s)=>s.id === groupData.stage);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-sm font-medium mb-2\",\n                    children: \"Current Stage\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 815,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full bg-secondary rounded-full h-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-primary h-2 rounded-full transition-all duration-500 ease-in-out\",\n                        style: {\n                            width: \"\".concat((currentIndex + 1) / stages.length * 100, \"%\")\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 817,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 816,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between mt-1 text-xs\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-primary font-medium\",\n                            children: stages[currentIndex].label\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 823,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-muted-foreground\",\n                            children: [\n                                \"Step \",\n                                currentIndex + 1,\n                                \" of \",\n                                stages.length\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 826,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 822,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n            lineNumber: 814,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layouts_mobile_layout__WEBPACK_IMPORTED_MODULE_2__.MobileLayout, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative bg-primary text-primary-foreground p-4 pb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                        href: \"/groups\",\n                        className: \"flex items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 838,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-1\",\n                                children: \"Back to Groups\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 839,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 837,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-fluid-xl font-bold\",\n                        children: groupData.name\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 841,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mt-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                size: 16,\n                                className: \"mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 843,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    groupData.members.length,\n                                    \" members\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 844,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mx-2\",\n                                children: \"•\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 845,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                size: 16,\n                                className: \"mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 846,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Expires in \",\n                                    groupData.expiresIn\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 847,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 842,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 836,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-4 pb-32\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"-mt-6 mb-6 relative z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"p-4\",\n                            children: renderStageIndicator()\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 853,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 852,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                        defaultValue: defaultTab,\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                                className: \"w-full grid grid-cols-1 mb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"discussion\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 859,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \"Discussion\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 860,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 858,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 857,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                                className: \"w-full grid grid-cols-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                        value: \"manufacturing\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 866,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"Manufacturing\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 867,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 865,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                        value: \"members\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 870,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"Members\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 871,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 869,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                        value: \"payment\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 874,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"Payment\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 875,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 873,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 864,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                value: \"discussion\",\n                                className: \"mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: getMainMessages().map((message)=>{\n                                        var _localProducts_find, _localProducts_find1, _localProducts_find2, _localProducts_find3, _localProducts_find4;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"rounded-lg p-3 \".concat(message.type === \"product-suggestion\" ? \"bg-blue-50 border border-blue-200\" : \"bg-muted\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-start mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: message.user\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 896,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        message.type === \"product-suggestion\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                            variant: \"secondary\",\n                                                                            className: \"text-xs\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                    className: \"h-3 w-3 mr-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 899,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                \"Product\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 898,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 895,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-muted-foreground\",\n                                                                            children: message.timestamp\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 905,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                            variant: \"ghost\",\n                                                                            size: \"icon\",\n                                                                            className: \"h-6 w-6\",\n                                                                            onClick: ()=>setShowEmojiPicker({\n                                                                                    messageId: message.id\n                                                                                }),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                className: \"h-3 w-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 916,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 908,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                            variant: \"ghost\",\n                                                                            size: \"icon\",\n                                                                            className: \"h-6 w-6\",\n                                                                            onClick: ()=>setReplyingTo(message.id),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                className: \"h-3 w-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 924,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 918,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        message.userId === currentUserId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                            variant: \"ghost\",\n                                                                            size: \"icon\",\n                                                                            className: \"h-6 w-6 text-red-500 hover:text-red-700\",\n                                                                            onClick: ()=>handleDeleteMessage(message.id),\n                                                                            title: \"Delete message\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                className: \"h-3 w-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 934,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 927,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 904,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 894,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm mb-2\",\n                                                            dangerouslySetInnerHTML: {\n                                                                __html: renderDetectedProducts(message.content, message.detectedProducts)\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 940,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        message.reactions && message.reactions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-1 mb-2\",\n                                                            children: Object.entries(message.reactions.reduce((acc, reaction)=>{\n                                                                if (!acc[reaction.emoji]) {\n                                                                    acc[reaction.emoji] = [];\n                                                                }\n                                                                acc[reaction.emoji].push(reaction);\n                                                                return acc;\n                                                            }, {})).map((param)=>{\n                                                                let [emoji, reactions] = param;\n                                                                const userReaction = reactions.find((r)=>r.userId === currentUserId);\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"h-6 px-2 text-xs relative group \".concat(userReaction ? \"bg-blue-50 border-blue-200\" : \"\"),\n                                                                    title: \"\".concat(reactions.map((r)=>r.userName).join(\", \"), \" reacted with \").concat(emoji),\n                                                                    onClick: ()=>{\n                                                                        if (userReaction) {\n                                                                            handleDeleteReaction(userReaction.id, message.id);\n                                                                        }\n                                                                    },\n                                                                    children: [\n                                                                        emoji,\n                                                                        \" \",\n                                                                        reactions.length,\n                                                                        userReaction && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-3 h-3 text-xs flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                                            children: \"\\xd7\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 988,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, emoji, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 967,\n                                                                    columnNumber: 29\n                                                                }, this);\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 952,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        getThreadMessages(message.id).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            className: \"text-xs text-blue-600 hover:text-blue-800 p-0 h-auto\",\n                                                            onClick: ()=>toggleThread(\"thread-\".concat(message.id)),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-3 w-3 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1006,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                getThreadMessages(message.id).length,\n                                                                \" replies\",\n                                                                expandedThreads.has(\"thread-\".concat(message.id)) ? \" ▼\" : \" ▶\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1000,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        message.productRef && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-2 p-3 bg-background rounded border border-border\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-16 h-16 bg-muted rounded overflow-hidden flex items-center justify-center mr-3\",\n                                                                        children: ((_localProducts_find = localProducts.find((p)=>p.id === message.productRef)) === null || _localProducts_find === void 0 ? void 0 : _localProducts_find.image) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"relative w-full h-full\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                                                                src: ((_localProducts_find1 = localProducts.find((p)=>p.id === message.productRef)) === null || _localProducts_find1 === void 0 ? void 0 : _localProducts_find1.image) || \"/images/placeholder.png\",\n                                                                                alt: ((_localProducts_find2 = localProducts.find((p)=>p.id === message.productRef)) === null || _localProducts_find2 === void 0 ? void 0 : _localProducts_find2.name) || \"Product\",\n                                                                                fill: true,\n                                                                                className: \"object-cover\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 1023,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1022,\n                                                                            columnNumber: 31\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"h-6 w-6 text-muted-foreground\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1039,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1018,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-start mb-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"font-medium text-sm\",\n                                                                                        children: (_localProducts_find3 = localProducts.find((p)=>p.id === message.productRef)) === null || _localProducts_find3 === void 0 ? void 0 : _localProducts_find3.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                        lineNumber: 1044,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center gap-1\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                            variant: \"ghost\",\n                                                                                            size: \"icon\",\n                                                                                            className: \"h-6 w-6\",\n                                                                                            onClick: ()=>setShowEmojiPicker({\n                                                                                                    productId: message.productRef\n                                                                                                }),\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                                className: \"h-3 w-3\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                                lineNumber: 1062,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                            lineNumber: 1052,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                        lineNumber: 1051,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 1043,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-muted-foreground text-sm mb-2\",\n                                                                                children: [\n                                                                                    \"$\",\n                                                                                    ((_localProducts_find4 = localProducts.find((p)=>p.id === message.productRef)) === null || _localProducts_find4 === void 0 ? void 0 : _localProducts_find4.price) || \"Price unavailable\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 1066,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            (()=>{\n                                                                                const product = localProducts.find((p)=>p.id === message.productRef);\n                                                                                return (product === null || product === void 0 ? void 0 : product.reactions) && product.reactions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex flex-wrap gap-1 mb-2\",\n                                                                                    children: Object.entries(product.reactions.reduce((acc, reaction)=>{\n                                                                                        if (!acc[reaction.emoji]) {\n                                                                                            acc[reaction.emoji] = [];\n                                                                                        }\n                                                                                        acc[reaction.emoji].push(reaction);\n                                                                                        return acc;\n                                                                                    }, {})).map((param)=>{\n                                                                                        let [emoji, reactions] = param;\n                                                                                        const userReaction = reactions.find((r)=>r.userId === currentUserId);\n                                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                            variant: \"outline\",\n                                                                                            size: \"sm\",\n                                                                                            className: \"h-6 px-2 text-xs relative group \".concat(userReaction ? \"bg-blue-50 border-blue-200\" : \"\"),\n                                                                                            title: \"\".concat(reactions.map((r)=>r.userName).join(\", \"), \" reacted with \").concat(emoji),\n                                                                                            onClick: ()=>{\n                                                                                                if (userReaction) {\n                                                                                                    handleDeleteReaction(userReaction.id, undefined, product.id);\n                                                                                                }\n                                                                                            },\n                                                                                            children: [\n                                                                                                emoji,\n                                                                                                \" \",\n                                                                                                reactions.length,\n                                                                                                userReaction && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-3 h-3 text-xs flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                                                                    children: \"\\xd7\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                                    lineNumber: 1126,\n                                                                                                    columnNumber: 45\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, emoji, true, {\n                                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                            lineNumber: 1102,\n                                                                                            columnNumber: 41\n                                                                                        }, this);\n                                                                                    })\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 1081,\n                                                                                    columnNumber: 35\n                                                                                }, this);\n                                                                            })(),\n                                                                            (()=>{\n                                                                                const product = localProducts.find((p)=>p.id === message.productRef);\n                                                                                return (product === null || product === void 0 ? void 0 : product.consensusScore) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center gap-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex-1 bg-gray-200 rounded-full h-2\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"h-2 rounded-full \".concat(product.consensusScore >= 80 ? \"bg-green-500\" : product.consensusScore >= 60 ? \"bg-yellow-500\" : \"bg-red-500\"),\n                                                                                                style: {\n                                                                                                    width: \"\".concat(product.consensusScore, \"%\")\n                                                                                                }\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                                lineNumber: 1147,\n                                                                                                columnNumber: 39\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                            lineNumber: 1146,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-xs font-medium \".concat(getConsensusColor(product.consensusScore)),\n                                                                                            children: [\n                                                                                                product.consensusScore,\n                                                                                                \"% consensus\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                            lineNumber: 1160,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 1145,\n                                                                                    columnNumber: 35\n                                                                                }, this);\n                                                                            })()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1042,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1017,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1016,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        message.attachment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full max-w-[200px] h-[150px] bg-muted rounded-md overflow-hidden relative\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 flex items-center justify-center text-muted-foreground\",\n                                                                    children: \"Image Attachment\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1179,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1178,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1177,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 887,\n                                                    columnNumber: 19\n                                                }, this),\n                                                expandedThreads.has(\"thread-\".concat(message.id)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-4 pl-4 border-l-2 border-gray-200 space-y-2\",\n                                                    children: getThreadMessages(message.id).map((threadMessage)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gray-50 rounded-lg p-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-start mb-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-sm\",\n                                                                            children: threadMessage.user\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1196,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-muted-foreground\",\n                                                                                    children: threadMessage.timestamp\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 1200,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                threadMessage.userId === currentUserId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                    variant: \"ghost\",\n                                                                                    size: \"icon\",\n                                                                                    className: \"h-4 w-4 text-red-500 hover:text-red-700\",\n                                                                                    onClick: ()=>handleDeleteMessage(threadMessage.id),\n                                                                                    title: \"Delete reply\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                        className: \"h-2 w-2\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                        lineNumber: 1213,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 1204,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1199,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1195,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm\",\n                                                                    dangerouslySetInnerHTML: {\n                                                                        __html: renderDetectedProducts(threadMessage.content, threadMessage.detectedProducts)\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1218,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, threadMessage.id, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1191,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1189,\n                                                    columnNumber: 21\n                                                }, this),\n                                                replyingTo === message.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-2 p-2 bg-gray-50 rounded border\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-muted-foreground mb-2\",\n                                                            children: [\n                                                                \"Replying to \",\n                                                                message.user\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1235,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    placeholder: \"Type your reply...\",\n                                                                    className: \"flex-1 h-8 rounded-l border border-input bg-background px-2 text-sm\",\n                                                                    value: replyText,\n                                                                    onChange: (e)=>setReplyText(e.target.value),\n                                                                    onKeyDown: (e)=>{\n                                                                        if (e.key === \"Enter\" && !e.shiftKey) {\n                                                                            e.preventDefault();\n                                                                            handleSendReply(message.id);\n                                                                        }\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1239,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>handleSendReply(message.id),\n                                                                    disabled: !replyText.trim(),\n                                                                    className: \"rounded-l-none\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1258,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1252,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    size: \"sm\",\n                                                                    variant: \"outline\",\n                                                                    onClick: ()=>{\n                                                                        setReplyingTo(null);\n                                                                        setReplyText(\"\");\n                                                                    },\n                                                                    children: \"Cancel\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1260,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1238,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1234,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, message.id, true, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 885,\n                                            columnNumber: 17\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 883,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 882,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                value: \"payment\",\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                className: \"pb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                    className: \"text-lg\",\n                                                    children: \"Payment Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1288,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1287,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full bg-secondary rounded-full h-3 mb-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-primary h-3 rounded-full\",\n                                                            style: {\n                                                                width: \"\".concat(groupData.amountPaid / groupData.totalAmount * 100, \"%\")\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1292,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1291,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"$\",\n                                                                    groupData.amountPaid,\n                                                                    \" raised\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1302,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"$\",\n                                                                    groupData.totalAmount,\n                                                                    \" goal\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1303,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1301,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-muted-foreground text-center\",\n                                                        children: [\n                                                            \"$\",\n                                                            groupData.totalAmount - groupData.amountPaid,\n                                                            \" remaining\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1305,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1290,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardFooter, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                    href: \"/groups/\".concat(params.id, \"/payment\"),\n                                                    className: \"w-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        className: \"w-full\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1312,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Make a Payment\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1311,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1310,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1309,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1286,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium mb-2\",\n                                        children: \"Payment History\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1319,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: groupData.members.filter((member)=>member.amountPaid > 0).map((member)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-muted p-3 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-8 h-8 rounded-full bg-secondary flex items-center justify-center\",\n                                                                    children: member.name.charAt(0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1327,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: member.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1331,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-muted-foreground\",\n                                                                            children: new Date().toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1332,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1330,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1326,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: [\n                                                                \"$\",\n                                                                member.amountPaid\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1337,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1325,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, member.id, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1324,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1320,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 1285,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                value: \"manufacturing\",\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Manufacturing Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1347,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-primary font-medium capitalize\",\n                                                        children: groupData.status\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1348,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1346,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-secondary rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-primary h-2 rounded-full\",\n                                                    style: {\n                                                        width: \"\".concat(groupData.manufacturingProgress, \"%\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1353,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1352,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mt-1 text-xs text-muted-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Production Started\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1359,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Ready for Shipping\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1360,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1358,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1345,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium\",\n                                                children: \"Latest Updates\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1365,\n                                                columnNumber: 15\n                                            }, this),\n                                            groupData.manufacturingUpdates.map((update)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                    className: \"overflow-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium\",\n                                                                        children: update.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1370,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: update.date\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1371,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1369,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm mb-2\",\n                                                                children: update.description\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1375,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-24 bg-muted flex items-center justify-center rounded\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-muted-foreground\",\n                                                                    children: \"Update Image\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1377,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1376,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1368,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, update.id, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1367,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1364,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 1344,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                value: \"members\",\n                                className: \"mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: groupData.members.map((member)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 rounded-full bg-secondary flex items-center justify-center\",\n                                                            children: member.name.charAt(0)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1395,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"ml-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: member.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1400,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        member.isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-2 px-1.5 py-0.5 bg-primary/10 text-primary text-xs rounded\",\n                                                                            children: \"Admin\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1402,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1399,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-muted-foreground\",\n                                                                    children: member.amountPaid > 0 ? \"Paid $\".concat(member.amountPaid) : \"No payment yet\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1407,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1398,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1394,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-secondary text-secondary-foreground rounded-full flex items-center justify-center\",\n                                                    children: [\n                                                        Math.round(member.amountPaid / (groupData.totalAmount / groupData.members.length) * 100),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1414,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, member.id, true, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1390,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1388,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 1387,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 856,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 851,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                open: showProductSelector,\n                onOpenChange: setShowProductSelector,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                    className: \"sm:max-w-[425px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                    children: \"Select a Product\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1433,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogDescription, {\n                                    children: \"Choose a product to reference in your message\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1434,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1432,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3 mt-4 max-h-[300px] overflow-y-auto\",\n                            children: groupData.suggestedProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border rounded-md cursor-pointer hover:bg-muted flex items-center\",\n                                    onClick: ()=>handleSelectProduct(product.id),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-muted rounded overflow-hidden mr-3 flex items-center justify-center\",\n                                            children: product.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative w-full h-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                                    src: product.image || \"/images/placeholder.png\",\n                                                    alt: product.name,\n                                                    fill: true,\n                                                    className: \"object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1448,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1447,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-6 w-6 text-muted-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1456,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1445,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium\",\n                                                    children: product.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1460,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: product.price > 0 ? \"$\".concat(product.price) : \"Price unavailable\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1461,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1459,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, product.id, true, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1440,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1438,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 1431,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 1430,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-4 left-4 right-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        selectedProductRef && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-2 bg-white rounded-lg border border-border shadow-lg p-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-muted rounded overflow-hidden flex items-center justify-center mr-2\",\n                                        children: ((_localProducts_find = localProducts.find((p)=>p.id === selectedProductRef)) === null || _localProducts_find === void 0 ? void 0 : _localProducts_find.image) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative w-full h-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                                src: ((_localProducts_find1 = localProducts.find((p)=>p.id === selectedProductRef)) === null || _localProducts_find1 === void 0 ? void 0 : _localProducts_find1.image) || \"/images/placeholder.png\",\n                                                alt: ((_localProducts_find2 = localProducts.find((p)=>p.id === selectedProductRef)) === null || _localProducts_find2 === void 0 ? void 0 : _localProducts_find2.name) || \"Product\",\n                                                fill: true,\n                                                className: \"object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1484,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1483,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1498,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1480,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-xs\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium\",\n                                                children: (_localProducts_find3 = localProducts.find((p)=>p.id === selectedProductRef)) === null || _localProducts_find3 === void 0 ? void 0 : _localProducts_find3.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1502,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-muted-foreground\",\n                                                children: [\n                                                    \"$\",\n                                                    ((_localProducts_find4 = localProducts.find((p)=>p.id === selectedProductRef)) === null || _localProducts_find4 === void 0 ? void 0 : _localProducts_find4.price) || \"Price unavailable\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1508,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1501,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        className: \"h-6 w-6 rounded-full\",\n                                        type: \"button\",\n                                        onClick: ()=>setSelectedProductRef(null),\n                                        children: \"\\xd7\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1514,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 1479,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1478,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"icon\",\n                                            className: \"rounded-full h-10 w-10 bg-white shadow-lg border\",\n                                            type: \"button\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"file-upload\",\n                                                className: \"cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        id: \"file-upload\",\n                                                        type: \"file\",\n                                                        accept: \"image/*\",\n                                                        className: \"sr-only\",\n                                                        \"aria-label\": \"Upload image\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1538,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1545,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1537,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1531,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"icon\",\n                                            className: \"rounded-full h-10 w-10 bg-white shadow-lg border\",\n                                            type: \"button\",\n                                            onClick: ()=>setShowProductSelector(true),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1555,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1548,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1530,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Type your message...\",\n                                            className: \"w-full h-10 rounded-full border border-input bg-white px-4 pr-12 text-sm shadow-lg ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n                                            value: messageText,\n                                            onChange: (e)=>setMessageText(e.target.value),\n                                            onKeyDown: (e)=>{\n                                                if (e.key === \"Enter\" && !e.shiftKey) {\n                                                    e.preventDefault();\n                                                    handleSendMessage();\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1561,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            size: \"icon\",\n                                            className: \"absolute right-1 top-1 h-8 w-8 rounded-full\",\n                                            onClick: handleSendMessage,\n                                            disabled: !messageText.trim(),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1580,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1574,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1560,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>setShowProductsOverview(true),\n                                    className: \"rounded-full shadow-lg h-10 px-4 bg-white border text-foreground hover:bg-accent\",\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1591,\n                                            columnNumber: 15\n                                        }, this),\n                                        localProducts.length\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1585,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1528,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2 flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-full px-3 py-1 shadow-lg border\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_group_suggestion_form__WEBPACK_IMPORTED_MODULE_9__.GroupSuggestionForm, {\n                                    groupId: params.id\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1599,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 1598,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1597,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 1475,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 1474,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                open: showProductsOverview,\n                onOpenChange: setShowProductsOverview,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                    className: \"sm:max-w-[500px] max-h-[80vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                    children: \"Product Suggestions\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1612,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogDescription, {\n                                    children: [\n                                        localProducts.length,\n                                        \" products suggested by the group\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1613,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1611,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 gap-4 mt-4\",\n                            children: localProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    className: \"overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-20 h-20 bg-muted flex items-center justify-center\",\n                                                children: product.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative w-full h-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                                        src: product.image || \"/images/placeholder.png\",\n                                                        alt: product.name,\n                                                        fill: true,\n                                                        className: \"object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1624,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1623,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-6 w-6 text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1632,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1621,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-sm\",\n                                                                children: product.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1637,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs px-1.5 py-0.5 rounded \".concat(product.source === \"internal\" ? \"bg-primary/10 text-primary\" : \"bg-secondary text-secondary-foreground\"),\n                                                                        children: product.source === \"internal\" ? \"Catalog\" : \"External\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1639,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-6 w-6\",\n                                                                        onClick: ()=>setShowEmojiPicker({\n                                                                                productId: product.id\n                                                                            }),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1658,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1650,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1638,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1636,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground mb-2\",\n                                                        children: product.description\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1662,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    product.reactions && product.reactions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1 mb-2\",\n                                                        children: Object.entries(product.reactions.reduce((acc, reaction)=>{\n                                                            if (!acc[reaction.emoji]) {\n                                                                acc[reaction.emoji] = [];\n                                                            }\n                                                            acc[reaction.emoji].push(reaction);\n                                                            return acc;\n                                                        }, {})).map((param)=>{\n                                                            let [emoji, reactions] = param;\n                                                            const userReaction = reactions.find((r)=>r.userId === currentUserId);\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                className: \"h-5 px-1.5 text-xs relative group \".concat(userReaction ? \"bg-blue-50 border-blue-200\" : \"\"),\n                                                                title: \"\".concat(reactions.map((r)=>r.userName).join(\", \"), \" reacted with \").concat(emoji),\n                                                                onClick: ()=>{\n                                                                    if (userReaction) {\n                                                                        handleDeleteReaction(userReaction.id, undefined, product.id);\n                                                                    }\n                                                                },\n                                                                children: [\n                                                                    emoji,\n                                                                    \" \",\n                                                                    reactions.length,\n                                                                    userReaction && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-2 h-2 text-xs flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                                        children: \"\\xd7\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1705,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                ]\n                                                            }, emoji, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1683,\n                                                                columnNumber: 29\n                                                            }, this);\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1668,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    product.consensusScore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1 bg-gray-200 rounded-full h-1.5\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-1.5 rounded-full \".concat(product.consensusScore >= 80 ? \"bg-green-500\" : product.consensusScore >= 60 ? \"bg-yellow-500\" : \"bg-red-500\"),\n                                                                            style: {\n                                                                                width: \"\".concat(product.consensusScore, \"%\")\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1720,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1719,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs font-medium \".concat(getConsensusColor(product.consensusScore)),\n                                                                        children: [\n                                                                            product.consensusScore,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1731,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1718,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: [\n                                                                    \"Group consensus • \",\n                                                                    product.threadCount || 0,\n                                                                    \" \",\n                                                                    \"discussions\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1739,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1717,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm\",\n                                                                children: product.price > 0 ? \"$\".concat(product.price) : \"Price unavailable\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1747,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>{\n                                                                    setShowProductsOverview(false);\n                                                                // In a real app, this would scroll to the product in discussion\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1760,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Discuss\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1752,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1746,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1635,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1620,\n                                        columnNumber: 17\n                                    }, this)\n                                }, product.id, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1619,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1617,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 1610,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 1606,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                open: !!showEmojiPicker,\n                onOpenChange: ()=>setShowEmojiPicker(null),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                    className: \"sm:max-w-[300px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                    children: \"Add Reaction\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1779,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogDescription, {\n                                    children: \"Choose an emoji to react with\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1780,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1778,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-6 gap-2 mt-4\",\n                            children: [\n                                \"\\uD83D\\uDC4D\",\n                                \"\\uD83D\\uDC4E\",\n                                \"❤️\",\n                                \"\\uD83D\\uDE0D\",\n                                \"\\uD83E\\uDD14\",\n                                \"\\uD83D\\uDE15\",\n                                \"\\uD83D\\uDCB0\",\n                                \"\\uD83D\\uDCB8\",\n                                \"\\uD83C\\uDF89\",\n                                \"❌\",\n                                \"\\uD83D\\uDC40\",\n                                \"\\uD83D\\uDCF8\",\n                                \"\\uD83D\\uDC4B\",\n                                \"\\uD83D\\uDD25\",\n                                \"\\uD83D\\uDCA1\",\n                                \"✨\",\n                                \"⚠️\",\n                                \"\\uD83D\\uDEAB\"\n                            ].map((emoji)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    className: \"h-12 w-12 text-2xl hover:bg-accent\",\n                                    onClick: ()=>handleAddReaction(emoji, showEmojiPicker === null || showEmojiPicker === void 0 ? void 0 : showEmojiPicker.messageId, showEmojiPicker === null || showEmojiPicker === void 0 ? void 0 : showEmojiPicker.productId),\n                                    children: emoji\n                                }, emoji, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1803,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1782,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 1777,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 1773,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n        lineNumber: 835,\n        columnNumber: 5\n    }, this);\n}\n_s(GroupDetail, \"lttP2OCiiFkan0rYv93pEyGTQuM=\");\n_c = GroupDetail;\nvar _c;\n$RefreshReg$(_c, \"GroupDetail\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/groups/[id]/page.tsx\n"));

/***/ })

});