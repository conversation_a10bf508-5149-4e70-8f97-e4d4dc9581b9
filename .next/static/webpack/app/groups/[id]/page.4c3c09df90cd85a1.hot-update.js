"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/groups/[id]/page",{

/***/ "(app-pages-browser)/./app/groups/[id]/page.tsx":
/*!**********************************!*\
  !*** ./app/groups/[id]/page.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GroupDetail; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layouts_mobile_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layouts/mobile-layout */ \"(app-pages-browser)/./components/layouts/mobile-layout.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smile.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/reply.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _app_components_group_suggestion_form__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/components/group-suggestion-form */ \"(app-pages-browser)/./app/components/group-suggestion-form.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// Removed quote components - quotes functionality has been removed\n\n// Mock data - would be fetched based on [id] in real app\nconst groupData = {\n    id: 1,\n    name: \"Living Room Remodel Group\",\n    description: \"A collective purchase for premium living room furniture at wholesale prices.\",\n    stage: \"suggestion\",\n    suggestedProducts: [\n        {\n            id: 1,\n            name: \"Premium Leather Sofa Set\",\n            price: 3500,\n            image: \"/images/placeholder.png\",\n            description: \"Genuine leather sofa set with matching ottoman\",\n            merchant: \"Luxury Furniture Co.\",\n            source: \"internal\",\n            reactions: [\n                {\n                    id: 1,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 11:20\"\n                },\n                {\n                    id: 2,\n                    userId: 2,\n                    userName: \"John Doe\",\n                    emoji: \"❤️\",\n                    timestamp: \"2023-03-10 11:25\"\n                },\n                {\n                    id: 3,\n                    userId: 3,\n                    userName: \"Alice Johnson\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 11:30\"\n                }\n            ],\n            consensusScore: 85,\n            threadCount: 3\n        },\n        {\n            id: 2,\n            name: \"Modern Fabric Sectional\",\n            price: 2800,\n            image: \"/images/placeholder.png\",\n            description: \"L-shaped sectional with chaise lounge in premium fabric\",\n            merchant: \"Contemporary Home\",\n            source: \"internal\",\n            reactions: [\n                {\n                    id: 4,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 11:35\"\n                },\n                {\n                    id: 5,\n                    userId: 4,\n                    userName: \"Bob Williams\",\n                    emoji: \"\\uD83E\\uDD14\",\n                    timestamp: \"2023-03-10 11:40\"\n                }\n            ],\n            consensusScore: 65,\n            threadCount: 2\n        },\n        {\n            id: 3,\n            name: \"Custom Wood Frame Sofa\",\n            price: 0,\n            image: \"/images/placeholder.png\",\n            description: \"Hand-crafted wooden frame sofa with custom upholstery\",\n            merchant: null,\n            source: \"external\",\n            reactions: [\n                {\n                    id: 6,\n                    userId: 5,\n                    userName: \"Carol Davis\",\n                    emoji: \"\\uD83D\\uDE0D\",\n                    timestamp: \"2023-03-10 12:20\"\n                },\n                {\n                    id: 7,\n                    userId: 2,\n                    userName: \"John Doe\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 12:25\"\n                }\n            ],\n            consensusScore: 70,\n            threadCount: 1\n        }\n    ],\n    selectedProduct: null,\n    product: {\n        name: \"Premium Leather Sofa Set\",\n        price: 3500,\n        image: \"/images/placeholder.png\"\n    },\n    members: [\n        {\n            id: 1,\n            name: \"Jane Smith\",\n            isAdmin: true,\n            amountPaid: 850\n        },\n        {\n            id: 2,\n            name: \"John Doe\",\n            isAdmin: false,\n            amountPaid: 700\n        },\n        {\n            id: 3,\n            name: \"Alice Johnson\",\n            isAdmin: false,\n            amountPaid: 600\n        },\n        {\n            id: 4,\n            name: \"Bob Williams\",\n            isAdmin: false,\n            amountPaid: 0\n        },\n        {\n            id: 5,\n            name: \"Carol Davis\",\n            isAdmin: false,\n            amountPaid: 0\n        }\n    ],\n    amountPaid: 2150,\n    totalAmount: 3500,\n    expiresIn: \"5 days\",\n    status: \"manufacturing\",\n    manufacturingProgress: 65,\n    manufacturingUpdates: [\n        {\n            id: 1,\n            date: \"2023-03-15\",\n            title: \"Production Started\",\n            description: \"Materials sourced and production has begun.\",\n            images: [\n                \"/images/placeholder.png\"\n            ]\n        },\n        {\n            id: 2,\n            date: \"2023-03-18\",\n            title: \"Frame Assembly\",\n            description: \"Wooden frames are assembled and ready for upholstery.\",\n            images: [\n                \"/images/placeholder.png\"\n            ]\n        },\n        {\n            id: 3,\n            date: \"2023-03-21\",\n            title: \"Upholstery Progress\",\n            description: \"Leather upholstery is being applied to the frames.\",\n            images: [\n                \"/images/placeholder.png\"\n            ]\n        }\n    ],\n    messages: [\n        {\n            id: 1,\n            user: \"Jane Smith\",\n            userId: 1,\n            content: \"Welcome everyone to our group buy!\",\n            timestamp: \"2023-03-10 10:23\",\n            type: \"text\",\n            reactions: [\n                {\n                    id: 1,\n                    userId: 2,\n                    userName: \"John Doe\",\n                    emoji: \"\\uD83D\\uDC4B\",\n                    timestamp: \"2023-03-10 10:25\"\n                },\n                {\n                    id: 2,\n                    userId: 3,\n                    userName: \"Alice Johnson\",\n                    emoji: \"\\uD83C\\uDF89\",\n                    timestamp: \"2023-03-10 10:26\"\n                }\n            ]\n        },\n        {\n            id: 2,\n            user: \"John Doe\",\n            userId: 2,\n            content: \"Thanks for organizing this!\",\n            timestamp: \"2023-03-10 10:45\",\n            type: \"text\",\n            reactions: [\n                {\n                    id: 3,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"❤️\",\n                    timestamp: \"2023-03-10 10:46\"\n                }\n            ]\n        },\n        {\n            id: 3,\n            user: \"Alice Johnson\",\n            userId: 3,\n            content: \"I added a Premium Leather Sofa Set to our product suggestions. What do you all think?\",\n            timestamp: \"2023-03-10 11:15\",\n            type: \"product-suggestion\",\n            productRef: 1,\n            detectedProducts: [\n                {\n                    text: \"Premium Leather Sofa Set\",\n                    startIndex: 9,\n                    endIndex: 33,\n                    suggestedProductId: 1,\n                    confidence: 0.95\n                }\n            ],\n            reactions: [\n                {\n                    id: 4,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 11:20\"\n                },\n                {\n                    id: 5,\n                    userId: 2,\n                    userName: \"John Doe\",\n                    emoji: \"\\uD83E\\uDD14\",\n                    timestamp: \"2023-03-10 11:22\"\n                }\n            ]\n        },\n        {\n            id: 4,\n            user: \"Jane Smith\",\n            userId: 1,\n            content: \"I like it but it's a bit pricey. I found this fabric sectional that might be more budget-friendly.\",\n            timestamp: \"2023-03-10 11:30\",\n            type: \"product-suggestion\",\n            productRef: 2,\n            parentMessageId: 3,\n            threadId: \"product-1-discussion\",\n            detectedProducts: [\n                {\n                    text: \"fabric sectional\",\n                    startIndex: 55,\n                    endIndex: 70,\n                    suggestedProductId: 2,\n                    confidence: 0.88\n                }\n            ],\n            reactions: [\n                {\n                    id: 6,\n                    userId: 4,\n                    userName: \"Bob Williams\",\n                    emoji: \"\\uD83D\\uDCB0\",\n                    timestamp: \"2023-03-10 11:35\"\n                }\n            ]\n        },\n        {\n            id: 5,\n            user: \"Bob Williams\",\n            userId: 4,\n            content: \"I saw this custom sofa at a local craftsman's shop. Uploading a photo I took.\",\n            timestamp: \"2023-03-10 12:15\",\n            type: \"product-suggestion\",\n            productRef: 3,\n            attachment: \"/images/placeholder.png\",\n            detectedProducts: [\n                {\n                    text: \"custom sofa\",\n                    startIndex: 12,\n                    endIndex: 23,\n                    suggestedProductId: 3,\n                    confidence: 0.92\n                }\n            ],\n            reactions: [\n                {\n                    id: 7,\n                    userId: 5,\n                    userName: \"Carol Davis\",\n                    emoji: \"\\uD83D\\uDCF8\",\n                    timestamp: \"2023-03-10 12:20\"\n                },\n                {\n                    id: 8,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"\\uD83D\\uDC40\",\n                    timestamp: \"2023-03-10 12:22\"\n                }\n            ]\n        },\n        {\n            id: 6,\n            user: \"John Doe\",\n            userId: 2,\n            content: \"The leather sofa looks amazing! How's the delivery time?\",\n            timestamp: \"2023-03-10 12:30\",\n            type: \"text\",\n            parentMessageId: 3,\n            threadId: \"product-1-discussion\",\n            detectedProducts: [\n                {\n                    text: \"leather sofa\",\n                    startIndex: 4,\n                    endIndex: 16,\n                    suggestedProductId: 1,\n                    confidence: 0.9\n                }\n            ]\n        },\n        {\n            id: 7,\n            user: \"Carol Davis\",\n            userId: 5,\n            content: \"I'm really interested in the custom wood frame option. Can we get more details?\",\n            timestamp: \"2023-03-10 13:00\",\n            type: \"text\",\n            parentMessageId: 5,\n            threadId: \"product-3-discussion\",\n            detectedProducts: [\n                {\n                    text: \"custom wood frame\",\n                    startIndex: 30,\n                    endIndex: 47,\n                    suggestedProductId: 3,\n                    confidence: 0.85\n                }\n            ]\n        }\n    ]\n};\n// Helper function to get the appropriate default tab based on group stage\nconst getDefaultTab = (stage)=>{\n    switch(stage){\n        case \"suggestion\":\n            return \"discussion\"; // suggestions are now part of discussion\n        case \"discussion\":\n            return \"discussion\";\n        case \"payment\":\n            return \"payment\";\n        case \"manufacturing\":\n            return \"manufacturing\";\n        case \"shipping\":\n            return \"shipping\";\n        default:\n            return \"discussion\";\n    }\n};\nfunction GroupDetail(param) {\n    let { params } = param;\n    var _groupData_suggestedProducts_find, _groupData_suggestedProducts_find1, _groupData_suggestedProducts_find2, _groupData_suggestedProducts_find3, _groupData_suggestedProducts_find4;\n    _s();\n    // In a real app, you would fetch the group data based on params.id\n    const defaultTab = getDefaultTab(groupData.stage);\n    const [selectedProductRef, setSelectedProductRef] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showProductSelector, setShowProductSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProductsOverview, setShowProductsOverview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messageText, setMessageText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Phase 2 state management\n    const [expandedThreads, setExpandedThreads] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [showEmojiPicker, setShowEmojiPicker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [replyingTo, setReplyingTo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentUserId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1); // Mock current user - Jane Smith\n    const handleSendMessage = ()=>{\n        // In a real app, this would send the message to the API\n        console.log(\"Sending message:\", {\n            content: messageText,\n            productRef: selectedProductRef\n        });\n        // Reset the form\n        setMessageText(\"\");\n        setSelectedProductRef(null);\n    };\n    const handleSelectProduct = (productId)=>{\n        setSelectedProductRef(productId);\n        setShowProductSelector(false);\n    };\n    // Phase 2 helper functions\n    const handleAddReaction = (emoji, messageId, productId)=>{\n        // In a real app, this would send the reaction to the API\n        console.log(\"Adding reaction:\", {\n            emoji,\n            messageId,\n            productId,\n            userId: currentUserId\n        });\n        setShowEmojiPicker(null);\n    };\n    const toggleThread = (threadId)=>{\n        const newExpanded = new Set(expandedThreads);\n        if (newExpanded.has(threadId)) {\n            newExpanded.delete(threadId);\n        } else {\n            newExpanded.add(threadId);\n        }\n        setExpandedThreads(newExpanded);\n    };\n    const getThreadMessages = (threadId)=>{\n        return groupData.messages.filter((msg)=>msg.threadId === threadId);\n    };\n    const getMainMessages = ()=>{\n        return groupData.messages.filter((msg)=>!msg.parentMessageId);\n    };\n    const getConsensusColor = (score)=>{\n        if (score >= 80) return \"text-green-600\";\n        if (score >= 60) return \"text-yellow-600\";\n        return \"text-red-600\";\n    };\n    const calculateConsensusScore = (reactions)=>{\n        if (!reactions || reactions.length === 0) return 0;\n        const positiveEmojis = [\n            \"\\uD83D\\uDC4D\",\n            \"❤️\",\n            \"\\uD83D\\uDE0D\",\n            \"\\uD83C\\uDF89\",\n            \"\\uD83D\\uDD25\",\n            \"\\uD83D\\uDCA1\",\n            \"✨\"\n        ];\n        const negativeEmojis = [\n            \"\\uD83D\\uDC4E\",\n            \"\\uD83D\\uDE15\",\n            \"❌\",\n            \"\\uD83D\\uDCB8\",\n            \"⚠️\",\n            \"\\uD83D\\uDEAB\"\n        ];\n        const neutralEmojis = [\n            \"\\uD83E\\uDD14\",\n            \"\\uD83D\\uDC40\",\n            \"\\uD83D\\uDCF8\",\n            \"\\uD83D\\uDC4B\"\n        ];\n        let positiveCount = 0;\n        let negativeCount = 0;\n        let neutralCount = 0;\n        reactions.forEach((reaction)=>{\n            if (positiveEmojis.includes(reaction.emoji)) {\n                positiveCount++;\n            } else if (negativeEmojis.includes(reaction.emoji)) {\n                negativeCount++;\n            } else {\n                neutralCount++;\n            }\n        });\n        const totalReactions = reactions.length;\n        const positiveWeight = positiveCount * 1.0;\n        const neutralWeight = neutralCount * 0.5;\n        const negativeWeight = negativeCount * 0.0;\n        const weightedScore = (positiveWeight + neutralWeight + negativeWeight) / totalReactions;\n        return Math.round(weightedScore * 100);\n    };\n    const renderDetectedProducts = (content, detectedProducts)=>{\n        if (!detectedProducts || detectedProducts.length === 0) {\n            return content;\n        }\n        let result = content;\n        let offset = 0;\n        detectedProducts.sort((a, b)=>a.startIndex - b.startIndex).forEach((detected)=>{\n            const start = detected.startIndex + offset;\n            const end = detected.endIndex + offset;\n            const productName = result.substring(start, end);\n            const replacement = '<span class=\"bg-blue-100 text-blue-800 px-1 rounded cursor-pointer hover:bg-blue-200\" data-product-id=\"'.concat(detected.suggestedProductId, '\">').concat(productName, \"</span>\");\n            result = result.substring(0, start) + replacement + result.substring(end);\n            offset += replacement.length - productName.length;\n        });\n        return result;\n    };\n    const renderStageIndicator = ()=>{\n        const stages = [\n            {\n                id: \"suggestion\",\n                label: \"Suggestions\"\n            },\n            {\n                id: \"discussion\",\n                label: \"Discussion\"\n            },\n            {\n                id: \"payment\",\n                label: \"Payment\"\n            },\n            {\n                id: \"manufacturing\",\n                label: \"Manufacturing\"\n            },\n            {\n                id: \"shipping\",\n                label: \"Shipping\"\n            }\n        ];\n        const currentIndex = stages.findIndex((s)=>s.id === groupData.stage);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-sm font-medium mb-2\",\n                    children: \"Current Stage\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 618,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full bg-secondary rounded-full h-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-primary h-2 rounded-full transition-all duration-500 ease-in-out\",\n                        style: {\n                            width: \"\".concat((currentIndex + 1) / stages.length * 100, \"%\")\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 620,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 619,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between mt-1 text-xs\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-primary font-medium\",\n                            children: stages[currentIndex].label\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 626,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-muted-foreground\",\n                            children: [\n                                \"Step \",\n                                currentIndex + 1,\n                                \" of \",\n                                stages.length\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 629,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 625,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n            lineNumber: 617,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layouts_mobile_layout__WEBPACK_IMPORTED_MODULE_2__.MobileLayout, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative bg-primary text-primary-foreground p-4 pb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                        href: \"/groups\",\n                        className: \"flex items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 641,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-1\",\n                                children: \"Back to Groups\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 642,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 640,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-fluid-xl font-bold\",\n                        children: groupData.name\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 644,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mt-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                size: 16,\n                                className: \"mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 646,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    groupData.members.length,\n                                    \" members\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 647,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mx-2\",\n                                children: \"•\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 648,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                size: 16,\n                                className: \"mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 649,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Expires in \",\n                                    groupData.expiresIn\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 650,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 645,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 639,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"-mt-6 mb-6 relative z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"p-4\",\n                            children: renderStageIndicator()\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 656,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 655,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                        defaultValue: defaultTab,\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                                className: \"w-full grid grid-cols-1 mb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"discussion\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 662,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \"Discussion\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 663,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 661,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 660,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                                className: \"w-full grid grid-cols-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                        value: \"manufacturing\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 669,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"Manufacturing\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 670,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 668,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                        value: \"members\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 673,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"Members\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 674,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 672,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                        value: \"payment\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 677,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"Payment\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 678,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 676,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 667,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                value: \"discussion\",\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: getMainMessages().map((message)=>{\n                                            var _groupData_suggestedProducts_find, _groupData_suggestedProducts_find1, _groupData_suggestedProducts_find2, _groupData_suggestedProducts_find3, _groupData_suggestedProducts_find4;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"rounded-lg p-3 \".concat(message.type === \"product-suggestion\" ? \"bg-blue-50 border border-blue-200\" : \"bg-muted\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: message.user\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 699,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            message.type === \"product-suggestion\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                variant: \"secondary\",\n                                                                                className: \"text-xs\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                        className: \"h-3 w-3 mr-1\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                        lineNumber: 702,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    \"Product\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 701,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 698,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs text-muted-foreground\",\n                                                                                children: message.timestamp\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 708,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"icon\",\n                                                                                className: \"h-6 w-6\",\n                                                                                onClick: ()=>setShowEmojiPicker({\n                                                                                        messageId: message.id\n                                                                                    }),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 719,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 711,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"icon\",\n                                                                                className: \"h-6 w-6\",\n                                                                                onClick: ()=>setReplyingTo(message.id),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 727,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 721,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 707,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 697,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm mb-2\",\n                                                                dangerouslySetInnerHTML: {\n                                                                    __html: renderDetectedProducts(message.content, message.detectedProducts)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 732,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            message.reactions && message.reactions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-wrap gap-1 mb-2\",\n                                                                children: message.reactions.map((reaction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        className: \"h-6 px-2 text-xs\",\n                                                                        title: \"\".concat(reaction.userName, \" reacted with \").concat(reaction.emoji),\n                                                                        children: [\n                                                                            reaction.emoji,\n                                                                            \" 1\"\n                                                                        ]\n                                                                    }, reaction.id, true, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 746,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 744,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            message.threadId && getThreadMessages(message.threadId).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"sm\",\n                                                                className: \"text-xs text-blue-600 hover:text-blue-800 p-0 h-auto\",\n                                                                onClick: ()=>toggleThread(message.threadId),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 768,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    getThreadMessages(message.threadId).length,\n                                                                    \" replies\",\n                                                                    expandedThreads.has(message.threadId) ? \" ▼\" : \" ▶\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 762,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            message.productRef && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2 p-3 bg-background rounded border border-border\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-16 h-16 bg-muted rounded overflow-hidden flex items-center justify-center mr-3\",\n                                                                            children: ((_groupData_suggestedProducts_find = groupData.suggestedProducts.find((p)=>p.id === message.productRef)) === null || _groupData_suggestedProducts_find === void 0 ? void 0 : _groupData_suggestedProducts_find.image) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"relative w-full h-full\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                                                                    src: ((_groupData_suggestedProducts_find1 = groupData.suggestedProducts.find((p)=>p.id === message.productRef)) === null || _groupData_suggestedProducts_find1 === void 0 ? void 0 : _groupData_suggestedProducts_find1.image) || \"/images/placeholder.png\",\n                                                                                    alt: ((_groupData_suggestedProducts_find2 = groupData.suggestedProducts.find((p)=>p.id === message.productRef)) === null || _groupData_suggestedProducts_find2 === void 0 ? void 0 : _groupData_suggestedProducts_find2.name) || \"Product\",\n                                                                                    fill: true,\n                                                                                    className: \"object-cover\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 783,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 782,\n                                                                                columnNumber: 31\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"h-6 w-6 text-muted-foreground\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 799,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 778,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex justify-between items-start mb-1\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"font-medium text-sm\",\n                                                                                            children: (_groupData_suggestedProducts_find3 = groupData.suggestedProducts.find((p)=>p.id === message.productRef)) === null || _groupData_suggestedProducts_find3 === void 0 ? void 0 : _groupData_suggestedProducts_find3.name\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                            lineNumber: 804,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-1\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                                variant: \"ghost\",\n                                                                                                size: \"icon\",\n                                                                                                className: \"h-6 w-6\",\n                                                                                                onClick: ()=>setShowEmojiPicker({\n                                                                                                        productId: message.productRef\n                                                                                                    }),\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                                    className: \"h-3 w-3\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                                    lineNumber: 822,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                                lineNumber: 812,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                            lineNumber: 811,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 803,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-muted-foreground text-sm mb-2\",\n                                                                                    children: [\n                                                                                        \"$\",\n                                                                                        ((_groupData_suggestedProducts_find4 = groupData.suggestedProducts.find((p)=>p.id === message.productRef)) === null || _groupData_suggestedProducts_find4 === void 0 ? void 0 : _groupData_suggestedProducts_find4.price) || \"Price unavailable\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 826,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                (()=>{\n                                                                                    const product = groupData.suggestedProducts.find((p)=>p.id === message.productRef);\n                                                                                    return (product === null || product === void 0 ? void 0 : product.reactions) && product.reactions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex flex-wrap gap-1 mb-2\",\n                                                                                        children: product.reactions.map((reaction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                                variant: \"outline\",\n                                                                                                size: \"sm\",\n                                                                                                className: \"h-6 px-2 text-xs\",\n                                                                                                title: \"\".concat(reaction.userName, \" reacted with \").concat(reaction.emoji),\n                                                                                                children: [\n                                                                                                    reaction.emoji,\n                                                                                                    \" 1\"\n                                                                                                ]\n                                                                                            }, reaction.id, true, {\n                                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                                lineNumber: 843,\n                                                                                                columnNumber: 39\n                                                                                            }, this))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                        lineNumber: 841,\n                                                                                        columnNumber: 35\n                                                                                    }, this);\n                                                                                })(),\n                                                                                (()=>{\n                                                                                    const product = groupData.suggestedProducts.find((p)=>p.id === message.productRef);\n                                                                                    return (product === null || product === void 0 ? void 0 : product.consensusScore) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center gap-2\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex-1 bg-gray-200 rounded-full h-2\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"h-2 rounded-full \".concat(product.consensusScore >= 80 ? \"bg-green-500\" : product.consensusScore >= 60 ? \"bg-yellow-500\" : \"bg-red-500\"),\n                                                                                                    style: {\n                                                                                                        width: \"\".concat(product.consensusScore, \"%\")\n                                                                                                    }\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                                    lineNumber: 867,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                                lineNumber: 866,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"text-xs font-medium \".concat(getConsensusColor(product.consensusScore)),\n                                                                                                children: [\n                                                                                                    product.consensusScore,\n                                                                                                    \"% consensus\"\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                                lineNumber: 880,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                        lineNumber: 865,\n                                                                                        columnNumber: 35\n                                                                                    }, this);\n                                                                                })()\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 802,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 777,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 776,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            message.attachment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full max-w-[200px] h-[150px] bg-muted rounded-md overflow-hidden relative\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 flex items-center justify-center text-muted-foreground\",\n                                                                        children: \"Image Attachment\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 899,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 898,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 897,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 690,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    message.threadId && expandedThreads.has(message.threadId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"ml-4 pl-4 border-l-2 border-gray-200 space-y-2\",\n                                                        children: getThreadMessages(message.threadId).map((threadMessage)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gray-50 rounded-lg p-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-start mb-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium text-sm\",\n                                                                                children: threadMessage.user\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 918,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs text-muted-foreground\",\n                                                                                children: threadMessage.timestamp\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 921,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 917,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm\",\n                                                                        dangerouslySetInnerHTML: {\n                                                                            __html: renderDetectedProducts(threadMessage.content, threadMessage.detectedProducts)\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 925,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, threadMessage.id, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 913,\n                                                                columnNumber: 29\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 910,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, message.id, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 688,\n                                                columnNumber: 17\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 686,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"icon\",\n                                                                className: \"rounded-full\",\n                                                                type: \"button\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"file-upload\",\n                                                                    className: \"cursor-pointer\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            id: \"file-upload\",\n                                                                            type: \"file\",\n                                                                            accept: \"image/*\",\n                                                                            className: \"sr-only\",\n                                                                            \"aria-label\": \"Upload image\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 953,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 960,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 952,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 946,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"icon\",\n                                                                className: \"rounded-full\",\n                                                                type: \"button\",\n                                                                onClick: ()=>setShowProductSelector(true),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 970,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 963,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_group_suggestion_form__WEBPACK_IMPORTED_MODULE_9__.GroupSuggestionForm, {\n                                                                groupId: params.id\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 972,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 945,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-muted-foreground px-2\",\n                                                        children: \"\\uD83D\\uDCA1 Suggest products, share files, or reference existing suggestions in your messages\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 974,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    selectedProductRef && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-background rounded border border-border flex items-center p-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-12 h-12 bg-muted rounded overflow-hidden flex items-center justify-center mr-3\",\n                                                                    children: ((_groupData_suggestedProducts_find = groupData.suggestedProducts.find((p)=>p.id === selectedProductRef)) === null || _groupData_suggestedProducts_find === void 0 ? void 0 : _groupData_suggestedProducts_find.image) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative w-full h-full\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                                                            src: ((_groupData_suggestedProducts_find1 = groupData.suggestedProducts.find((p)=>p.id === selectedProductRef)) === null || _groupData_suggestedProducts_find1 === void 0 ? void 0 : _groupData_suggestedProducts_find1.image) || \"/images/placeholder.png\",\n                                                                            alt: ((_groupData_suggestedProducts_find2 = groupData.suggestedProducts.find((p)=>p.id === selectedProductRef)) === null || _groupData_suggestedProducts_find2 === void 0 ? void 0 : _groupData_suggestedProducts_find2.name) || \"Product\",\n                                                                            fill: true,\n                                                                            className: \"object-cover\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 987,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 986,\n                                                                        columnNumber: 27\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-muted-foreground\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1003,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 982,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1 text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: (_groupData_suggestedProducts_find3 = groupData.suggestedProducts.find((p)=>p.id === selectedProductRef)) === null || _groupData_suggestedProducts_find3 === void 0 ? void 0 : _groupData_suggestedProducts_find3.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1007,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-muted-foreground\",\n                                                                            children: [\n                                                                                \"$\",\n                                                                                ((_groupData_suggestedProducts_find4 = groupData.suggestedProducts.find((p)=>p.id === selectedProductRef)) === null || _groupData_suggestedProducts_find4 === void 0 ? void 0 : _groupData_suggestedProducts_find4.price) || \"Price unavailable\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1014,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1006,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"icon\",\n                                                                    className: \"h-6 w-6 rounded-full ml-1\",\n                                                                    type: \"button\",\n                                                                    onClick: ()=>setSelectedProductRef(null),\n                                                                    children: \"\\xd7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1021,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 981,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 980,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 944,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: \"Type your message...\",\n                                                        className: \"flex-1 h-10 rounded-l-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n                                                        value: messageText,\n                                                        onChange: (e)=>setMessageText(e.target.value),\n                                                        onKeyDown: (e)=>{\n                                                            if (e.key === \"Enter\" && !e.shiftKey) {\n                                                                e.preventDefault();\n                                                                handleSendMessage();\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1035,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        className: \"rounded-l-none\",\n                                                        onClick: handleSendMessage,\n                                                        children: \"Send\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1048,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1034,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 943,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 685,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                value: \"payment\",\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                className: \"pb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                    className: \"text-lg\",\n                                                    children: \"Payment Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1063,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1062,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full bg-secondary rounded-full h-3 mb-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-primary h-3 rounded-full\",\n                                                            style: {\n                                                                width: \"\".concat(groupData.amountPaid / groupData.totalAmount * 100, \"%\")\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1067,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1066,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"$\",\n                                                                    groupData.amountPaid,\n                                                                    \" raised\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1077,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"$\",\n                                                                    groupData.totalAmount,\n                                                                    \" goal\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1078,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1076,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-muted-foreground text-center\",\n                                                        children: [\n                                                            \"$\",\n                                                            groupData.totalAmount - groupData.amountPaid,\n                                                            \" remaining\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1080,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1065,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardFooter, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                    href: \"/groups/\".concat(params.id, \"/payment\"),\n                                                    className: \"w-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        className: \"w-full\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1087,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Make a Payment\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1086,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1085,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1084,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1061,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium mb-2\",\n                                        children: \"Payment History\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1094,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: groupData.members.filter((member)=>member.amountPaid > 0).map((member)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-muted p-3 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-8 h-8 rounded-full bg-secondary flex items-center justify-center\",\n                                                                    children: member.name.charAt(0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1102,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: member.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1106,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-muted-foreground\",\n                                                                            children: new Date().toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1107,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1105,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1101,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: [\n                                                                \"$\",\n                                                                member.amountPaid\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1112,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1100,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, member.id, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1099,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1095,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 1060,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                value: \"manufacturing\",\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Manufacturing Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1122,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-primary font-medium capitalize\",\n                                                        children: groupData.status\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1123,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1121,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-secondary rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-primary h-2 rounded-full\",\n                                                    style: {\n                                                        width: \"\".concat(groupData.manufacturingProgress, \"%\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1128,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1127,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mt-1 text-xs text-muted-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Production Started\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1134,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Ready for Shipping\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1135,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1133,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1120,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium\",\n                                                children: \"Latest Updates\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1140,\n                                                columnNumber: 15\n                                            }, this),\n                                            groupData.manufacturingUpdates.map((update)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                    className: \"overflow-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium\",\n                                                                        children: update.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1145,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: update.date\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1146,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1144,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm mb-2\",\n                                                                children: update.description\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1150,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-24 bg-muted flex items-center justify-center rounded\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-muted-foreground\",\n                                                                    children: \"Update Image\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1152,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1151,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1143,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, update.id, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1142,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1139,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 1119,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                value: \"members\",\n                                className: \"mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: groupData.members.map((member)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 rounded-full bg-secondary flex items-center justify-center\",\n                                                            children: member.name.charAt(0)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1170,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"ml-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: member.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1175,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        member.isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-2 px-1.5 py-0.5 bg-primary/10 text-primary text-xs rounded\",\n                                                                            children: \"Admin\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1177,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1174,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-muted-foreground\",\n                                                                    children: member.amountPaid > 0 ? \"Paid $\".concat(member.amountPaid) : \"No payment yet\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1182,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1173,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1169,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-secondary text-secondary-foreground rounded-full flex items-center justify-center\",\n                                                    children: [\n                                                        Math.round(member.amountPaid / (groupData.totalAmount / groupData.members.length) * 100),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1189,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, member.id, true, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1165,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1163,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 1162,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 659,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 654,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                open: showProductSelector,\n                onOpenChange: setShowProductSelector,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                    className: \"sm:max-w-[425px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                    children: \"Select a Product\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1208,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogDescription, {\n                                    children: \"Choose a product to reference in your message\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1209,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1207,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3 mt-4 max-h-[300px] overflow-y-auto\",\n                            children: groupData.suggestedProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border rounded-md cursor-pointer hover:bg-muted flex items-center\",\n                                    onClick: ()=>handleSelectProduct(product.id),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-muted rounded overflow-hidden mr-3 flex items-center justify-center\",\n                                            children: product.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative w-full h-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                                    src: product.image || \"/images/placeholder.png\",\n                                                    alt: product.name,\n                                                    fill: true,\n                                                    className: \"object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1223,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1222,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-6 w-6 text-muted-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1231,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1220,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium\",\n                                                    children: product.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1235,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: product.price > 0 ? \"$\".concat(product.price) : \"Price unavailable\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1236,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1234,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, product.id, true, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1215,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1213,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 1206,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 1205,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-20 right-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    onClick: ()=>setShowProductsOverview(true),\n                    className: \"rounded-full shadow-lg h-12 px-4\",\n                    size: \"sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"h-4 w-4 mr-2\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1255,\n                            columnNumber: 11\n                        }, this),\n                        groupData.suggestedProducts.length,\n                        \" Products\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 1250,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 1249,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                open: showProductsOverview,\n                onOpenChange: setShowProductsOverview,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                    className: \"sm:max-w-[500px] max-h-[80vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                    children: \"Product Suggestions\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1267,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogDescription, {\n                                    children: [\n                                        groupData.suggestedProducts.length,\n                                        \" products suggested by the group\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1268,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1266,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 gap-4 mt-4\",\n                            children: groupData.suggestedProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    className: \"overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-20 h-20 bg-muted flex items-center justify-center\",\n                                                children: product.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative w-full h-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                                        src: product.image || \"/images/placeholder.png\",\n                                                        alt: product.name,\n                                                        fill: true,\n                                                        className: \"object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1280,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1279,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-6 w-6 text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1288,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1277,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-sm\",\n                                                                children: product.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1293,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs px-1.5 py-0.5 rounded \".concat(product.source === \"internal\" ? \"bg-primary/10 text-primary\" : \"bg-secondary text-secondary-foreground\"),\n                                                                        children: product.source === \"internal\" ? \"Catalog\" : \"External\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1295,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-6 w-6\",\n                                                                        onClick: ()=>setShowEmojiPicker({\n                                                                                productId: product.id\n                                                                            }),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1314,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1306,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1294,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1292,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground mb-2\",\n                                                        children: product.description\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1318,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    product.reactions && product.reactions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1 mb-2\",\n                                                        children: product.reactions.map((reaction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                className: \"h-5 px-1.5 text-xs\",\n                                                                title: \"\".concat(reaction.userName, \" reacted with \").concat(reaction.emoji),\n                                                                children: [\n                                                                    reaction.emoji,\n                                                                    \" 1\"\n                                                                ]\n                                                            }, reaction.id, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1326,\n                                                                columnNumber: 27\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1324,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    product.consensusScore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1 bg-gray-200 rounded-full h-1.5\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-1.5 rounded-full \".concat(product.consensusScore >= 80 ? \"bg-green-500\" : product.consensusScore >= 60 ? \"bg-yellow-500\" : \"bg-red-500\"),\n                                                                            style: {\n                                                                                width: \"\".concat(product.consensusScore, \"%\")\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1344,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1343,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs font-medium \".concat(getConsensusColor(product.consensusScore)),\n                                                                        children: [\n                                                                            product.consensusScore,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1355,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1342,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: [\n                                                                    \"Group consensus • \",\n                                                                    product.threadCount || 0,\n                                                                    \" \",\n                                                                    \"discussions\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1363,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1341,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm\",\n                                                                children: product.price > 0 ? \"$\".concat(product.price) : \"Price unavailable\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1371,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>{\n                                                                    setShowProductsOverview(false);\n                                                                // In a real app, this would scroll to the product in discussion\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1384,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Discuss\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1376,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1370,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1291,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1276,\n                                        columnNumber: 17\n                                    }, this)\n                                }, product.id, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1275,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1273,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 pt-4 border-t\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_group_suggestion_form__WEBPACK_IMPORTED_MODULE_9__.GroupSuggestionForm, {\n                                groupId: params.id\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 1394,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1393,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 1265,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 1261,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                open: !!showEmojiPicker,\n                onOpenChange: ()=>setShowEmojiPicker(null),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                    className: \"sm:max-w-[300px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                    children: \"Add Reaction\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1406,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogDescription, {\n                                    children: \"Choose an emoji to react with\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1407,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1405,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-6 gap-2 mt-4\",\n                            children: [\n                                \"\\uD83D\\uDC4D\",\n                                \"\\uD83D\\uDC4E\",\n                                \"❤️\",\n                                \"\\uD83D\\uDE0D\",\n                                \"\\uD83E\\uDD14\",\n                                \"\\uD83D\\uDE15\",\n                                \"\\uD83D\\uDCB0\",\n                                \"\\uD83D\\uDCB8\",\n                                \"\\uD83C\\uDF89\",\n                                \"❌\",\n                                \"\\uD83D\\uDC40\",\n                                \"\\uD83D\\uDCF8\",\n                                \"\\uD83D\\uDC4B\",\n                                \"\\uD83D\\uDD25\",\n                                \"\\uD83D\\uDCA1\",\n                                \"✨\",\n                                \"⚠️\",\n                                \"\\uD83D\\uDEAB\"\n                            ].map((emoji)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    className: \"h-12 w-12 text-2xl hover:bg-accent\",\n                                    onClick: ()=>handleAddReaction(emoji, showEmojiPicker === null || showEmojiPicker === void 0 ? void 0 : showEmojiPicker.messageId, showEmojiPicker === null || showEmojiPicker === void 0 ? void 0 : showEmojiPicker.productId),\n                                    children: emoji\n                                }, emoji, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1430,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1409,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 1404,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 1400,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n        lineNumber: 638,\n        columnNumber: 5\n    }, this);\n}\n_s(GroupDetail, \"vMnZ29SNPzEGWKW4OXNTQsVdCeI=\");\n_c = GroupDetail;\nvar _c;\n$RefreshReg$(_c, \"GroupDetail\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/groups/[id]/page.tsx\n"));

/***/ })

});