"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/groups/[id]/page",{

/***/ "(app-pages-browser)/./app/groups/[id]/page.tsx":
/*!**********************************!*\
  !*** ./app/groups/[id]/page.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GroupDetail; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layouts_mobile_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layouts/mobile-layout */ \"(app-pages-browser)/./components/layouts/mobile-layout.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smile.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/reply.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,ShoppingBag,Smile,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _app_components_group_suggestion_form__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/components/group-suggestion-form */ \"(app-pages-browser)/./app/components/group-suggestion-form.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// Removed quote components - quotes functionality has been removed\n\n// Mock data - would be fetched based on [id] in real app\nconst groupData = {\n    id: 1,\n    name: \"Living Room Remodel Group\",\n    description: \"A collective purchase for premium living room furniture at wholesale prices.\",\n    stage: \"suggestion\",\n    suggestedProducts: [\n        {\n            id: 1,\n            name: \"Premium Leather Sofa Set\",\n            price: 3500,\n            image: \"/images/placeholder.png\",\n            description: \"Genuine leather sofa set with matching ottoman\",\n            merchant: \"Luxury Furniture Co.\",\n            source: \"internal\",\n            reactions: [\n                {\n                    id: 1,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 11:20\"\n                },\n                {\n                    id: 2,\n                    userId: 2,\n                    userName: \"John Doe\",\n                    emoji: \"❤️\",\n                    timestamp: \"2023-03-10 11:25\"\n                },\n                {\n                    id: 3,\n                    userId: 3,\n                    userName: \"Alice Johnson\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 11:30\"\n                }\n            ],\n            consensusScore: 85,\n            threadCount: 3\n        },\n        {\n            id: 2,\n            name: \"Modern Fabric Sectional\",\n            price: 2800,\n            image: \"/images/placeholder.png\",\n            description: \"L-shaped sectional with chaise lounge in premium fabric\",\n            merchant: \"Contemporary Home\",\n            source: \"internal\",\n            reactions: [\n                {\n                    id: 4,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 11:35\"\n                },\n                {\n                    id: 5,\n                    userId: 4,\n                    userName: \"Bob Williams\",\n                    emoji: \"\\uD83E\\uDD14\",\n                    timestamp: \"2023-03-10 11:40\"\n                },\n                {\n                    id: 8,\n                    userId: 5,\n                    userName: \"Carol Davis\",\n                    emoji: \"\\uD83D\\uDC4E\",\n                    timestamp: \"2023-03-10 11:45\"\n                },\n                {\n                    id: 9,\n                    userId: 6,\n                    userName: \"Mike Johnson\",\n                    emoji: \"\\uD83D\\uDCB8\",\n                    timestamp: \"2023-03-10 11:50\"\n                }\n            ],\n            consensusScore: 45,\n            threadCount: 2\n        },\n        {\n            id: 3,\n            name: \"Custom Wood Frame Sofa\",\n            price: 0,\n            image: \"/images/placeholder.png\",\n            description: \"Hand-crafted wooden frame sofa with custom upholstery\",\n            merchant: null,\n            source: \"external\",\n            reactions: [\n                {\n                    id: 6,\n                    userId: 5,\n                    userName: \"Carol Davis\",\n                    emoji: \"\\uD83D\\uDE0D\",\n                    timestamp: \"2023-03-10 12:20\"\n                },\n                {\n                    id: 7,\n                    userId: 2,\n                    userName: \"John Doe\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 12:25\"\n                }\n            ],\n            consensusScore: 70,\n            threadCount: 1\n        }\n    ],\n    selectedProduct: null,\n    product: {\n        name: \"Premium Leather Sofa Set\",\n        price: 3500,\n        image: \"/images/placeholder.png\"\n    },\n    members: [\n        {\n            id: 1,\n            name: \"Jane Smith\",\n            isAdmin: true,\n            amountPaid: 850\n        },\n        {\n            id: 2,\n            name: \"John Doe\",\n            isAdmin: false,\n            amountPaid: 700\n        },\n        {\n            id: 3,\n            name: \"Alice Johnson\",\n            isAdmin: false,\n            amountPaid: 600\n        },\n        {\n            id: 4,\n            name: \"Bob Williams\",\n            isAdmin: false,\n            amountPaid: 0\n        },\n        {\n            id: 5,\n            name: \"Carol Davis\",\n            isAdmin: false,\n            amountPaid: 0\n        }\n    ],\n    amountPaid: 2150,\n    totalAmount: 3500,\n    expiresIn: \"5 days\",\n    status: \"manufacturing\",\n    manufacturingProgress: 65,\n    manufacturingUpdates: [\n        {\n            id: 1,\n            date: \"2023-03-15\",\n            title: \"Production Started\",\n            description: \"Materials sourced and production has begun.\",\n            images: [\n                \"/images/placeholder.png\"\n            ]\n        },\n        {\n            id: 2,\n            date: \"2023-03-18\",\n            title: \"Frame Assembly\",\n            description: \"Wooden frames are assembled and ready for upholstery.\",\n            images: [\n                \"/images/placeholder.png\"\n            ]\n        },\n        {\n            id: 3,\n            date: \"2023-03-21\",\n            title: \"Upholstery Progress\",\n            description: \"Leather upholstery is being applied to the frames.\",\n            images: [\n                \"/images/placeholder.png\"\n            ]\n        }\n    ],\n    messages: [\n        {\n            id: 1,\n            user: \"Jane Smith\",\n            userId: 1,\n            content: \"Welcome everyone to our group buy!\",\n            timestamp: \"2023-03-10 10:23\",\n            type: \"text\",\n            reactions: [\n                {\n                    id: 1,\n                    userId: 2,\n                    userName: \"John Doe\",\n                    emoji: \"\\uD83D\\uDC4B\",\n                    timestamp: \"2023-03-10 10:25\"\n                },\n                {\n                    id: 2,\n                    userId: 3,\n                    userName: \"Alice Johnson\",\n                    emoji: \"\\uD83C\\uDF89\",\n                    timestamp: \"2023-03-10 10:26\"\n                }\n            ]\n        },\n        {\n            id: 2,\n            user: \"John Doe\",\n            userId: 2,\n            content: \"Thanks for organizing this!\",\n            timestamp: \"2023-03-10 10:45\",\n            type: \"text\",\n            reactions: [\n                {\n                    id: 3,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"❤️\",\n                    timestamp: \"2023-03-10 10:46\"\n                }\n            ]\n        },\n        {\n            id: 3,\n            user: \"Alice Johnson\",\n            userId: 3,\n            content: \"I added a Premium Leather Sofa Set to our product suggestions. What do you all think?\",\n            timestamp: \"2023-03-10 11:15\",\n            type: \"product-suggestion\",\n            productRef: 1,\n            detectedProducts: [\n                {\n                    text: \"Premium Leather Sofa Set\",\n                    startIndex: 9,\n                    endIndex: 33,\n                    suggestedProductId: 1,\n                    confidence: 0.95\n                }\n            ],\n            reactions: [\n                {\n                    id: 4,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 11:20\"\n                },\n                {\n                    id: 5,\n                    userId: 2,\n                    userName: \"John Doe\",\n                    emoji: \"\\uD83E\\uDD14\",\n                    timestamp: \"2023-03-10 11:22\"\n                }\n            ]\n        },\n        {\n            id: 4,\n            user: \"Jane Smith\",\n            userId: 1,\n            content: \"I like it but it's a bit pricey. I found this fabric sectional that might be more budget-friendly.\",\n            timestamp: \"2023-03-10 11:30\",\n            type: \"product-suggestion\",\n            productRef: 2,\n            parentMessageId: 3,\n            threadId: \"product-1-discussion\",\n            detectedProducts: [\n                {\n                    text: \"fabric sectional\",\n                    startIndex: 55,\n                    endIndex: 70,\n                    suggestedProductId: 2,\n                    confidence: 0.88\n                }\n            ],\n            reactions: [\n                {\n                    id: 6,\n                    userId: 4,\n                    userName: \"Bob Williams\",\n                    emoji: \"\\uD83D\\uDCB0\",\n                    timestamp: \"2023-03-10 11:35\"\n                },\n                {\n                    id: 10,\n                    userId: 5,\n                    userName: \"Carol Davis\",\n                    emoji: \"\\uD83D\\uDC4E\",\n                    timestamp: \"2023-03-10 11:37\"\n                }\n            ]\n        },\n        {\n            id: 5,\n            user: \"Bob Williams\",\n            userId: 4,\n            content: \"I saw this custom sofa at a local craftsman's shop. Uploading a photo I took.\",\n            timestamp: \"2023-03-10 12:15\",\n            type: \"product-suggestion\",\n            productRef: 3,\n            attachment: \"/images/placeholder.png\",\n            detectedProducts: [\n                {\n                    text: \"custom sofa\",\n                    startIndex: 12,\n                    endIndex: 23,\n                    suggestedProductId: 3,\n                    confidence: 0.92\n                }\n            ],\n            reactions: [\n                {\n                    id: 7,\n                    userId: 5,\n                    userName: \"Carol Davis\",\n                    emoji: \"\\uD83D\\uDCF8\",\n                    timestamp: \"2023-03-10 12:20\"\n                },\n                {\n                    id: 8,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"\\uD83D\\uDC40\",\n                    timestamp: \"2023-03-10 12:22\"\n                }\n            ]\n        },\n        {\n            id: 6,\n            user: \"John Doe\",\n            userId: 2,\n            content: \"The leather sofa looks amazing! How's the delivery time?\",\n            timestamp: \"2023-03-10 12:30\",\n            type: \"text\",\n            parentMessageId: 3,\n            threadId: \"product-1-discussion\",\n            detectedProducts: [\n                {\n                    text: \"leather sofa\",\n                    startIndex: 4,\n                    endIndex: 16,\n                    suggestedProductId: 1,\n                    confidence: 0.9\n                }\n            ]\n        },\n        {\n            id: 7,\n            user: \"Carol Davis\",\n            userId: 5,\n            content: \"I'm really interested in the custom wood frame option. Can we get more details?\",\n            timestamp: \"2023-03-10 13:00\",\n            type: \"text\",\n            parentMessageId: 5,\n            threadId: \"product-3-discussion\",\n            detectedProducts: [\n                {\n                    text: \"custom wood frame\",\n                    startIndex: 30,\n                    endIndex: 47,\n                    suggestedProductId: 3,\n                    confidence: 0.85\n                }\n            ]\n        }\n    ]\n};\n// Helper function to get the appropriate default tab based on group stage\nconst getDefaultTab = (stage)=>{\n    switch(stage){\n        case \"suggestion\":\n            return \"discussion\"; // suggestions are now part of discussion\n        case \"discussion\":\n            return \"discussion\";\n        case \"payment\":\n            return \"payment\";\n        case \"manufacturing\":\n            return \"manufacturing\";\n        case \"shipping\":\n            return \"shipping\";\n        default:\n            return \"discussion\";\n    }\n};\nfunction GroupDetail(param) {\n    let { params } = param;\n    var _groupData_suggestedProducts_find, _groupData_suggestedProducts_find1, _groupData_suggestedProducts_find2, _groupData_suggestedProducts_find3, _groupData_suggestedProducts_find4;\n    _s();\n    // In a real app, you would fetch the group data based on params.id\n    const defaultTab = getDefaultTab(groupData.stage);\n    const [selectedProductRef, setSelectedProductRef] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showProductSelector, setShowProductSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProductsOverview, setShowProductsOverview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messageText, setMessageText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Phase 2 state management\n    const [expandedThreads, setExpandedThreads] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [showEmojiPicker, setShowEmojiPicker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [replyingTo, setReplyingTo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentUserId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1); // Mock current user - Jane Smith\n    // Local state for dynamic updates (in a real app, this would be managed by a state management system)\n    const [localMessages, setLocalMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(groupData.messages);\n    const [localProducts, setLocalProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(groupData.suggestedProducts);\n    const handleSendMessage = ()=>{\n        if (!messageText.trim()) return;\n        const newMessage = {\n            id: Date.now(),\n            user: \"Jane Smith\",\n            userId: currentUserId,\n            content: messageText,\n            timestamp: new Date().toLocaleString(),\n            type: selectedProductRef ? \"product-suggestion\" : \"text\",\n            productRef: selectedProductRef || undefined,\n            reactions: []\n        };\n        setLocalMessages((prev)=>[\n                ...prev,\n                newMessage\n            ]);\n        setMessageText(\"\");\n        setSelectedProductRef(null);\n    };\n    const handleSelectProduct = (productId)=>{\n        setSelectedProductRef(productId);\n        setShowProductSelector(false);\n    };\n    // Phase 2 helper functions\n    const handleAddReaction = (emoji, messageId, productId)=>{\n        const newReaction = {\n            id: Date.now(),\n            userId: currentUserId,\n            userName: \"Jane Smith\",\n            emoji,\n            timestamp: new Date().toLocaleString()\n        };\n        if (messageId) {\n            // Add reaction to message\n            setLocalMessages((prev)=>prev.map((msg)=>msg.id === messageId ? {\n                        ...msg,\n                        reactions: [\n                            ...msg.reactions || [],\n                            newReaction\n                        ]\n                    } : msg));\n        }\n        if (productId) {\n            // Add reaction to product\n            setLocalProducts((prev)=>prev.map((product)=>product.id === productId ? {\n                        ...product,\n                        reactions: [\n                            ...product.reactions || [],\n                            newReaction\n                        ],\n                        consensusScore: calculateConsensusScore([\n                            ...product.reactions || [],\n                            newReaction\n                        ])\n                    } : product));\n        }\n        setShowEmojiPicker(null);\n    };\n    const toggleThread = (threadId)=>{\n        const newExpanded = new Set(expandedThreads);\n        if (newExpanded.has(threadId)) {\n            newExpanded.delete(threadId);\n        } else {\n            newExpanded.add(threadId);\n        }\n        setExpandedThreads(newExpanded);\n    };\n    const getThreadMessages = (threadId)=>{\n        return localMessages.filter((msg)=>msg.threadId === threadId);\n    };\n    const getMainMessages = ()=>{\n        return localMessages.filter((msg)=>!msg.parentMessageId);\n    };\n    const getConsensusColor = (score)=>{\n        if (score >= 80) return \"text-green-600\";\n        if (score >= 60) return \"text-yellow-600\";\n        return \"text-red-600\";\n    };\n    const calculateConsensusScore = (reactions)=>{\n        if (!reactions || reactions.length === 0) return 0;\n        const positiveEmojis = [\n            \"\\uD83D\\uDC4D\",\n            \"❤️\",\n            \"\\uD83D\\uDE0D\",\n            \"\\uD83C\\uDF89\",\n            \"\\uD83D\\uDD25\",\n            \"\\uD83D\\uDCA1\",\n            \"✨\"\n        ];\n        const negativeEmojis = [\n            \"\\uD83D\\uDC4E\",\n            \"\\uD83D\\uDE15\",\n            \"❌\",\n            \"\\uD83D\\uDCB8\",\n            \"⚠️\",\n            \"\\uD83D\\uDEAB\"\n        ];\n        const neutralEmojis = [\n            \"\\uD83E\\uDD14\",\n            \"\\uD83D\\uDC40\",\n            \"\\uD83D\\uDCF8\",\n            \"\\uD83D\\uDC4B\"\n        ];\n        let positiveCount = 0;\n        let negativeCount = 0;\n        let neutralCount = 0;\n        reactions.forEach((reaction)=>{\n            if (positiveEmojis.includes(reaction.emoji)) {\n                positiveCount++;\n            } else if (negativeEmojis.includes(reaction.emoji)) {\n                negativeCount++;\n            } else {\n                neutralCount++;\n            }\n        });\n        const totalReactions = reactions.length;\n        const positiveWeight = positiveCount * 1.0;\n        const neutralWeight = neutralCount * 0.5;\n        const negativeWeight = negativeCount * 0.0;\n        const weightedScore = (positiveWeight + neutralWeight + negativeWeight) / totalReactions;\n        return Math.round(weightedScore * 100);\n    };\n    const renderDetectedProducts = (content, detectedProducts)=>{\n        if (!detectedProducts || detectedProducts.length === 0) {\n            return content;\n        }\n        let result = content;\n        let offset = 0;\n        detectedProducts.sort((a, b)=>a.startIndex - b.startIndex).forEach((detected)=>{\n            const start = detected.startIndex + offset;\n            const end = detected.endIndex + offset;\n            const productName = result.substring(start, end);\n            const replacement = '<span class=\"bg-blue-100 text-blue-800 px-1 rounded cursor-pointer hover:bg-blue-200\" data-product-id=\"'.concat(detected.suggestedProductId, '\">').concat(productName, \"</span>\");\n            result = result.substring(0, start) + replacement + result.substring(end);\n            offset += replacement.length - productName.length;\n        });\n        return result;\n    };\n    const renderStageIndicator = ()=>{\n        const stages = [\n            {\n                id: \"suggestion\",\n                label: \"Suggestions\"\n            },\n            {\n                id: \"discussion\",\n                label: \"Discussion\"\n            },\n            {\n                id: \"payment\",\n                label: \"Payment\"\n            },\n            {\n                id: \"manufacturing\",\n                label: \"Manufacturing\"\n            },\n            {\n                id: \"shipping\",\n                label: \"Shipping\"\n            }\n        ];\n        const currentIndex = stages.findIndex((s)=>s.id === groupData.stage);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-sm font-medium mb-2\",\n                    children: \"Current Stage\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 684,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full bg-secondary rounded-full h-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-primary h-2 rounded-full transition-all duration-500 ease-in-out\",\n                        style: {\n                            width: \"\".concat((currentIndex + 1) / stages.length * 100, \"%\")\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 686,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 685,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between mt-1 text-xs\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-primary font-medium\",\n                            children: stages[currentIndex].label\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 692,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-muted-foreground\",\n                            children: [\n                                \"Step \",\n                                currentIndex + 1,\n                                \" of \",\n                                stages.length\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 695,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 691,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n            lineNumber: 683,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layouts_mobile_layout__WEBPACK_IMPORTED_MODULE_2__.MobileLayout, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative bg-primary text-primary-foreground p-4 pb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                        href: \"/groups\",\n                        className: \"flex items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 707,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-1\",\n                                children: \"Back to Groups\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 708,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 706,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-fluid-xl font-bold\",\n                        children: groupData.name\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 710,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mt-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                size: 16,\n                                className: \"mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 712,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    groupData.members.length,\n                                    \" members\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 713,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mx-2\",\n                                children: \"•\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 714,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                size: 16,\n                                className: \"mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 715,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Expires in \",\n                                    groupData.expiresIn\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 716,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 711,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 705,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"-mt-6 mb-6 relative z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"p-4\",\n                            children: renderStageIndicator()\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 722,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 721,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                        defaultValue: defaultTab,\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                                className: \"w-full grid grid-cols-1 mb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"discussion\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 728,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \"Discussion\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 729,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 727,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 726,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                                className: \"w-full grid grid-cols-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                        value: \"manufacturing\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 735,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"Manufacturing\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 736,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 734,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                        value: \"members\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 739,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"Members\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 740,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 738,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                        value: \"payment\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 743,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"Payment\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 744,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 742,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 733,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                value: \"discussion\",\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: getMainMessages().map((message)=>{\n                                            var _localProducts_find, _localProducts_find1, _localProducts_find2, _localProducts_find3, _localProducts_find4;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"rounded-lg p-3 \".concat(message.type === \"product-suggestion\" ? \"bg-blue-50 border border-blue-200\" : \"bg-muted\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: message.user\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 765,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            message.type === \"product-suggestion\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                variant: \"secondary\",\n                                                                                className: \"text-xs\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                        className: \"h-3 w-3 mr-1\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                        lineNumber: 768,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    \"Product\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 767,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 764,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs text-muted-foreground\",\n                                                                                children: message.timestamp\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 774,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"icon\",\n                                                                                className: \"h-6 w-6\",\n                                                                                onClick: ()=>setShowEmojiPicker({\n                                                                                        messageId: message.id\n                                                                                    }),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 785,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 777,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"icon\",\n                                                                                className: \"h-6 w-6\",\n                                                                                onClick: ()=>setReplyingTo(message.id),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 793,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 787,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 773,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 763,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm mb-2\",\n                                                                dangerouslySetInnerHTML: {\n                                                                    __html: renderDetectedProducts(message.content, message.detectedProducts)\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 798,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            message.reactions && message.reactions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-wrap gap-1 mb-2\",\n                                                                children: message.reactions.map((reaction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        className: \"h-6 px-2 text-xs\",\n                                                                        title: \"\".concat(reaction.userName, \" reacted with \").concat(reaction.emoji),\n                                                                        children: [\n                                                                            reaction.emoji,\n                                                                            \" 1\"\n                                                                        ]\n                                                                    }, reaction.id, true, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 812,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 810,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            message.threadId && getThreadMessages(message.threadId).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"sm\",\n                                                                className: \"text-xs text-blue-600 hover:text-blue-800 p-0 h-auto\",\n                                                                onClick: ()=>toggleThread(message.threadId),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 834,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    getThreadMessages(message.threadId).length,\n                                                                    \" replies\",\n                                                                    expandedThreads.has(message.threadId) ? \" ▼\" : \" ▶\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 828,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            message.productRef && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2 p-3 bg-background rounded border border-border\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-16 h-16 bg-muted rounded overflow-hidden flex items-center justify-center mr-3\",\n                                                                            children: ((_localProducts_find = localProducts.find((p)=>p.id === message.productRef)) === null || _localProducts_find === void 0 ? void 0 : _localProducts_find.image) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"relative w-full h-full\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                                                                    src: ((_localProducts_find1 = localProducts.find((p)=>p.id === message.productRef)) === null || _localProducts_find1 === void 0 ? void 0 : _localProducts_find1.image) || \"/images/placeholder.png\",\n                                                                                    alt: ((_localProducts_find2 = localProducts.find((p)=>p.id === message.productRef)) === null || _localProducts_find2 === void 0 ? void 0 : _localProducts_find2.name) || \"Product\",\n                                                                                    fill: true,\n                                                                                    className: \"object-cover\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 849,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 848,\n                                                                                columnNumber: 31\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"h-6 w-6 text-muted-foreground\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 865,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 844,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex justify-between items-start mb-1\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"font-medium text-sm\",\n                                                                                            children: (_localProducts_find3 = localProducts.find((p)=>p.id === message.productRef)) === null || _localProducts_find3 === void 0 ? void 0 : _localProducts_find3.name\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                            lineNumber: 870,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-1\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                                variant: \"ghost\",\n                                                                                                size: \"icon\",\n                                                                                                className: \"h-6 w-6\",\n                                                                                                onClick: ()=>setShowEmojiPicker({\n                                                                                                        productId: message.productRef\n                                                                                                    }),\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                                    className: \"h-3 w-3\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                                    lineNumber: 888,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                                lineNumber: 878,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                            lineNumber: 877,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 869,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-muted-foreground text-sm mb-2\",\n                                                                                    children: [\n                                                                                        \"$\",\n                                                                                        ((_localProducts_find4 = localProducts.find((p)=>p.id === message.productRef)) === null || _localProducts_find4 === void 0 ? void 0 : _localProducts_find4.price) || \"Price unavailable\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 892,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                (()=>{\n                                                                                    const product = localProducts.find((p)=>p.id === message.productRef);\n                                                                                    return (product === null || product === void 0 ? void 0 : product.reactions) && product.reactions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex flex-wrap gap-1 mb-2\",\n                                                                                        children: product.reactions.map((reaction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                                variant: \"outline\",\n                                                                                                size: \"sm\",\n                                                                                                className: \"h-6 px-2 text-xs\",\n                                                                                                title: \"\".concat(reaction.userName, \" reacted with \").concat(reaction.emoji),\n                                                                                                children: [\n                                                                                                    reaction.emoji,\n                                                                                                    \" 1\"\n                                                                                                ]\n                                                                                            }, reaction.id, true, {\n                                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                                lineNumber: 909,\n                                                                                                columnNumber: 39\n                                                                                            }, this))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                        lineNumber: 907,\n                                                                                        columnNumber: 35\n                                                                                    }, this);\n                                                                                })(),\n                                                                                (()=>{\n                                                                                    const product = localProducts.find((p)=>p.id === message.productRef);\n                                                                                    return (product === null || product === void 0 ? void 0 : product.consensusScore) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center gap-2\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex-1 bg-gray-200 rounded-full h-2\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"h-2 rounded-full \".concat(product.consensusScore >= 80 ? \"bg-green-500\" : product.consensusScore >= 60 ? \"bg-yellow-500\" : \"bg-red-500\"),\n                                                                                                    style: {\n                                                                                                        width: \"\".concat(product.consensusScore, \"%\")\n                                                                                                    }\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                                    lineNumber: 933,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                                lineNumber: 932,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"text-xs font-medium \".concat(getConsensusColor(product.consensusScore)),\n                                                                                                children: [\n                                                                                                    product.consensusScore,\n                                                                                                    \"% consensus\"\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                                lineNumber: 946,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                        lineNumber: 931,\n                                                                                        columnNumber: 35\n                                                                                    }, this);\n                                                                                })()\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 868,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 843,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 842,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            message.attachment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full max-w-[200px] h-[150px] bg-muted rounded-md overflow-hidden relative\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 flex items-center justify-center text-muted-foreground\",\n                                                                        children: \"Image Attachment\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 965,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 964,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 963,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 756,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    message.threadId && expandedThreads.has(message.threadId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"ml-4 pl-4 border-l-2 border-gray-200 space-y-2\",\n                                                        children: getThreadMessages(message.threadId).map((threadMessage)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gray-50 rounded-lg p-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-start mb-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium text-sm\",\n                                                                                children: threadMessage.user\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 984,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs text-muted-foreground\",\n                                                                                children: threadMessage.timestamp\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 987,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 983,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm\",\n                                                                        dangerouslySetInnerHTML: {\n                                                                            __html: renderDetectedProducts(threadMessage.content, threadMessage.detectedProducts)\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 991,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, threadMessage.id, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 979,\n                                                                columnNumber: 29\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 976,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    replyingTo === message.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 p-2 bg-gray-50 rounded border\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-muted-foreground mb-2\",\n                                                                children: [\n                                                                    \"Replying to \",\n                                                                    message.user\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1009,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        placeholder: \"Type your reply...\",\n                                                                        className: \"flex-1 h-8 rounded border border-input bg-background px-2 text-sm\",\n                                                                        onKeyDown: (e)=>{\n                                                                            if (e.key === \"Enter\" && e.currentTarget.value.trim()) {\n                                                                                // In a real app, this would add the reply to the thread\n                                                                                console.log(\"Adding reply:\", e.currentTarget.value);\n                                                                                setReplyingTo(null);\n                                                                                e.currentTarget.value = \"\";\n                                                                            }\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1013,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>setReplyingTo(null),\n                                                                        children: \"Cancel\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1032,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1012,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1008,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, message.id, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 754,\n                                                columnNumber: 17\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 752,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"icon\",\n                                                                className: \"rounded-full\",\n                                                                type: \"button\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"file-upload\",\n                                                                    className: \"cursor-pointer\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            id: \"file-upload\",\n                                                                            type: \"file\",\n                                                                            accept: \"image/*\",\n                                                                            className: \"sr-only\",\n                                                                            \"aria-label\": \"Upload image\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1056,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1063,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1055,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1049,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"icon\",\n                                                                className: \"rounded-full\",\n                                                                type: \"button\",\n                                                                onClick: ()=>setShowProductSelector(true),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1073,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1066,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_group_suggestion_form__WEBPACK_IMPORTED_MODULE_9__.GroupSuggestionForm, {\n                                                                groupId: params.id\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1075,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1048,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-muted-foreground px-2\",\n                                                        children: \"\\uD83D\\uDCA1 Suggest products, share files, or reference existing suggestions in your messages\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1077,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    selectedProductRef && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-background rounded border border-border flex items-center p-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-12 h-12 bg-muted rounded overflow-hidden flex items-center justify-center mr-3\",\n                                                                    children: ((_groupData_suggestedProducts_find = groupData.suggestedProducts.find((p)=>p.id === selectedProductRef)) === null || _groupData_suggestedProducts_find === void 0 ? void 0 : _groupData_suggestedProducts_find.image) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative w-full h-full\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                                                            src: ((_groupData_suggestedProducts_find1 = groupData.suggestedProducts.find((p)=>p.id === selectedProductRef)) === null || _groupData_suggestedProducts_find1 === void 0 ? void 0 : _groupData_suggestedProducts_find1.image) || \"/images/placeholder.png\",\n                                                                            alt: ((_groupData_suggestedProducts_find2 = groupData.suggestedProducts.find((p)=>p.id === selectedProductRef)) === null || _groupData_suggestedProducts_find2 === void 0 ? void 0 : _groupData_suggestedProducts_find2.name) || \"Product\",\n                                                                            fill: true,\n                                                                            className: \"object-cover\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1090,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1089,\n                                                                        columnNumber: 27\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-muted-foreground\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1106,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1085,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1 text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: (_groupData_suggestedProducts_find3 = groupData.suggestedProducts.find((p)=>p.id === selectedProductRef)) === null || _groupData_suggestedProducts_find3 === void 0 ? void 0 : _groupData_suggestedProducts_find3.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1110,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-muted-foreground\",\n                                                                            children: [\n                                                                                \"$\",\n                                                                                ((_groupData_suggestedProducts_find4 = groupData.suggestedProducts.find((p)=>p.id === selectedProductRef)) === null || _groupData_suggestedProducts_find4 === void 0 ? void 0 : _groupData_suggestedProducts_find4.price) || \"Price unavailable\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1117,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1109,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"icon\",\n                                                                    className: \"h-6 w-6 rounded-full ml-1\",\n                                                                    type: \"button\",\n                                                                    onClick: ()=>setSelectedProductRef(null),\n                                                                    children: \"\\xd7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1124,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1084,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1083,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1047,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: \"Type your message...\",\n                                                        className: \"flex-1 h-10 rounded-l-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n                                                        value: messageText,\n                                                        onChange: (e)=>setMessageText(e.target.value),\n                                                        onKeyDown: (e)=>{\n                                                            if (e.key === \"Enter\" && !e.shiftKey) {\n                                                                e.preventDefault();\n                                                                handleSendMessage();\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1138,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        className: \"rounded-l-none\",\n                                                        onClick: handleSendMessage,\n                                                        children: \"Send\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1151,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1137,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1046,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 751,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                value: \"payment\",\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                className: \"pb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                    className: \"text-lg\",\n                                                    children: \"Payment Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1166,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1165,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full bg-secondary rounded-full h-3 mb-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-primary h-3 rounded-full\",\n                                                            style: {\n                                                                width: \"\".concat(groupData.amountPaid / groupData.totalAmount * 100, \"%\")\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1170,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1169,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"$\",\n                                                                    groupData.amountPaid,\n                                                                    \" raised\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1180,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"$\",\n                                                                    groupData.totalAmount,\n                                                                    \" goal\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1181,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1179,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-muted-foreground text-center\",\n                                                        children: [\n                                                            \"$\",\n                                                            groupData.totalAmount - groupData.amountPaid,\n                                                            \" remaining\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1183,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1168,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardFooter, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                    href: \"/groups/\".concat(params.id, \"/payment\"),\n                                                    className: \"w-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        className: \"w-full\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1190,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Make a Payment\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1189,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1188,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1187,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1164,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium mb-2\",\n                                        children: \"Payment History\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1197,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: groupData.members.filter((member)=>member.amountPaid > 0).map((member)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-muted p-3 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-8 h-8 rounded-full bg-secondary flex items-center justify-center\",\n                                                                    children: member.name.charAt(0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1205,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: member.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1209,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-muted-foreground\",\n                                                                            children: new Date().toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1210,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1208,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1204,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: [\n                                                                \"$\",\n                                                                member.amountPaid\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1215,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1203,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, member.id, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1202,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1198,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 1163,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                value: \"manufacturing\",\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Manufacturing Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1225,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-primary font-medium capitalize\",\n                                                        children: groupData.status\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1226,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1224,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-secondary rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-primary h-2 rounded-full\",\n                                                    style: {\n                                                        width: \"\".concat(groupData.manufacturingProgress, \"%\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1231,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1230,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mt-1 text-xs text-muted-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Production Started\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1237,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Ready for Shipping\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1238,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1236,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1223,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium\",\n                                                children: \"Latest Updates\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1243,\n                                                columnNumber: 15\n                                            }, this),\n                                            groupData.manufacturingUpdates.map((update)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                    className: \"overflow-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium\",\n                                                                        children: update.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1248,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: update.date\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1249,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1247,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm mb-2\",\n                                                                children: update.description\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1253,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-24 bg-muted flex items-center justify-center rounded\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-muted-foreground\",\n                                                                    children: \"Update Image\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1255,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1254,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1246,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, update.id, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1245,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1242,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 1222,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                value: \"members\",\n                                className: \"mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: groupData.members.map((member)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 rounded-full bg-secondary flex items-center justify-center\",\n                                                            children: member.name.charAt(0)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1273,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"ml-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: member.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1278,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        member.isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-2 px-1.5 py-0.5 bg-primary/10 text-primary text-xs rounded\",\n                                                                            children: \"Admin\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1280,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1277,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-muted-foreground\",\n                                                                    children: member.amountPaid > 0 ? \"Paid $\".concat(member.amountPaid) : \"No payment yet\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1285,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1276,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1272,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-secondary text-secondary-foreground rounded-full flex items-center justify-center\",\n                                                    children: [\n                                                        Math.round(member.amountPaid / (groupData.totalAmount / groupData.members.length) * 100),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1292,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, member.id, true, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1268,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1266,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 1265,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 725,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 720,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                open: showProductSelector,\n                onOpenChange: setShowProductSelector,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                    className: \"sm:max-w-[425px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                    children: \"Select a Product\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1311,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogDescription, {\n                                    children: \"Choose a product to reference in your message\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1312,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1310,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3 mt-4 max-h-[300px] overflow-y-auto\",\n                            children: groupData.suggestedProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border rounded-md cursor-pointer hover:bg-muted flex items-center\",\n                                    onClick: ()=>handleSelectProduct(product.id),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-muted rounded overflow-hidden mr-3 flex items-center justify-center\",\n                                            children: product.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative w-full h-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                                    src: product.image || \"/images/placeholder.png\",\n                                                    alt: product.name,\n                                                    fill: true,\n                                                    className: \"object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1326,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1325,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-6 w-6 text-muted-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1334,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1323,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium\",\n                                                    children: product.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1338,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: product.price > 0 ? \"$\".concat(product.price) : \"Price unavailable\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1339,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1337,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, product.id, true, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1318,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1316,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 1309,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 1308,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-20 right-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    onClick: ()=>setShowProductsOverview(true),\n                    className: \"rounded-full shadow-lg h-12 px-4\",\n                    size: \"sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"h-4 w-4 mr-2\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1358,\n                            columnNumber: 11\n                        }, this),\n                        localProducts.length,\n                        \" Products\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 1353,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 1352,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                open: showProductsOverview,\n                onOpenChange: setShowProductsOverview,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                    className: \"sm:max-w-[500px] max-h-[80vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                    children: \"Product Suggestions\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1370,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogDescription, {\n                                    children: [\n                                        localProducts.length,\n                                        \" products suggested by the group\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1371,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1369,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 gap-4 mt-4\",\n                            children: localProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    className: \"overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-20 h-20 bg-muted flex items-center justify-center\",\n                                                children: product.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative w-full h-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                                        src: product.image || \"/images/placeholder.png\",\n                                                        alt: product.name,\n                                                        fill: true,\n                                                        className: \"object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1382,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1381,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-6 w-6 text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1390,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1379,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-sm\",\n                                                                children: product.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1395,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs px-1.5 py-0.5 rounded \".concat(product.source === \"internal\" ? \"bg-primary/10 text-primary\" : \"bg-secondary text-secondary-foreground\"),\n                                                                        children: product.source === \"internal\" ? \"Catalog\" : \"External\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1397,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-6 w-6\",\n                                                                        onClick: ()=>setShowEmojiPicker({\n                                                                                productId: product.id\n                                                                            }),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1416,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1408,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1396,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1394,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground mb-2\",\n                                                        children: product.description\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1420,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    product.reactions && product.reactions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1 mb-2\",\n                                                        children: product.reactions.map((reaction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                className: \"h-5 px-1.5 text-xs\",\n                                                                title: \"\".concat(reaction.userName, \" reacted with \").concat(reaction.emoji),\n                                                                children: [\n                                                                    reaction.emoji,\n                                                                    \" 1\"\n                                                                ]\n                                                            }, reaction.id, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1428,\n                                                                columnNumber: 27\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1426,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    product.consensusScore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1 bg-gray-200 rounded-full h-1.5\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-1.5 rounded-full \".concat(product.consensusScore >= 80 ? \"bg-green-500\" : product.consensusScore >= 60 ? \"bg-yellow-500\" : \"bg-red-500\"),\n                                                                            style: {\n                                                                                width: \"\".concat(product.consensusScore, \"%\")\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1446,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1445,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs font-medium \".concat(getConsensusColor(product.consensusScore)),\n                                                                        children: [\n                                                                            product.consensusScore,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1457,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1444,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: [\n                                                                    \"Group consensus • \",\n                                                                    product.threadCount || 0,\n                                                                    \" \",\n                                                                    \"discussions\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1465,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1443,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm\",\n                                                                children: product.price > 0 ? \"$\".concat(product.price) : \"Price unavailable\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1473,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>{\n                                                                    setShowProductsOverview(false);\n                                                                // In a real app, this would scroll to the product in discussion\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_ShoppingBag_Smile_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1486,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Discuss\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1478,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1472,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1393,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1378,\n                                        columnNumber: 17\n                                    }, this)\n                                }, product.id, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1377,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1375,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 pt-4 border-t\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_group_suggestion_form__WEBPACK_IMPORTED_MODULE_9__.GroupSuggestionForm, {\n                                groupId: params.id\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 1496,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1495,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 1368,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 1364,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                open: !!showEmojiPicker,\n                onOpenChange: ()=>setShowEmojiPicker(null),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                    className: \"sm:max-w-[300px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                    children: \"Add Reaction\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1508,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogDescription, {\n                                    children: \"Choose an emoji to react with\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1509,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1507,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-6 gap-2 mt-4\",\n                            children: [\n                                \"\\uD83D\\uDC4D\",\n                                \"\\uD83D\\uDC4E\",\n                                \"❤️\",\n                                \"\\uD83D\\uDE0D\",\n                                \"\\uD83E\\uDD14\",\n                                \"\\uD83D\\uDE15\",\n                                \"\\uD83D\\uDCB0\",\n                                \"\\uD83D\\uDCB8\",\n                                \"\\uD83C\\uDF89\",\n                                \"❌\",\n                                \"\\uD83D\\uDC40\",\n                                \"\\uD83D\\uDCF8\",\n                                \"\\uD83D\\uDC4B\",\n                                \"\\uD83D\\uDD25\",\n                                \"\\uD83D\\uDCA1\",\n                                \"✨\",\n                                \"⚠️\",\n                                \"\\uD83D\\uDEAB\"\n                            ].map((emoji)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    className: \"h-12 w-12 text-2xl hover:bg-accent\",\n                                    onClick: ()=>handleAddReaction(emoji, showEmojiPicker === null || showEmojiPicker === void 0 ? void 0 : showEmojiPicker.messageId, showEmojiPicker === null || showEmojiPicker === void 0 ? void 0 : showEmojiPicker.productId),\n                                    children: emoji\n                                }, emoji, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1532,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1511,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 1506,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 1502,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n        lineNumber: 704,\n        columnNumber: 5\n    }, this);\n}\n_s(GroupDetail, \"srinvnVsOg3E2IzA2SIR1MjhbP0=\");\n_c = GroupDetail;\nvar _c;\n$RefreshReg$(_c, \"GroupDetail\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/groups/[id]/page.tsx\n"));

/***/ })

});