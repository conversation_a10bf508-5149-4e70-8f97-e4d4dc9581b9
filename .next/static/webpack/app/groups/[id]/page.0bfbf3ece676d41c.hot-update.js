"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/groups/[id]/page",{

/***/ "(app-pages-browser)/./app/groups/[id]/page.tsx":
/*!**********************************!*\
  !*** ./app/groups/[id]/page.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GroupDetail; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layouts_mobile_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layouts/mobile-layout */ \"(app-pages-browser)/./components/layouts/mobile-layout.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smile.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/reply.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Removed quote components - quotes functionality has been removed\n\n// Mock data - would be fetched based on [id] in real app\nconst groupData = {\n    id: 1,\n    name: \"Living Room Remodel Group\",\n    description: \"A collective purchase for premium living room furniture at wholesale prices.\",\n    stage: \"suggestion\",\n    suggestedProducts: [\n        {\n            id: 1,\n            name: \"Premium Leather Sofa Set\",\n            price: 3500,\n            image: \"/images/placeholder.png\",\n            description: \"Genuine leather sofa set with matching ottoman\",\n            merchant: \"Luxury Furniture Co.\",\n            source: \"internal\",\n            reactions: [\n                {\n                    id: 1,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 11:20\"\n                },\n                {\n                    id: 2,\n                    userId: 2,\n                    userName: \"John Doe\",\n                    emoji: \"❤️\",\n                    timestamp: \"2023-03-10 11:25\"\n                },\n                {\n                    id: 3,\n                    userId: 3,\n                    userName: \"Alice Johnson\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 11:30\"\n                }\n            ],\n            consensusScore: 85,\n            threadCount: 3\n        },\n        {\n            id: 2,\n            name: \"Modern Fabric Sectional\",\n            price: 2800,\n            image: \"/images/placeholder.png\",\n            description: \"L-shaped sectional with chaise lounge in premium fabric\",\n            merchant: \"Contemporary Home\",\n            source: \"internal\",\n            reactions: [\n                {\n                    id: 4,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 11:35\"\n                },\n                {\n                    id: 5,\n                    userId: 4,\n                    userName: \"Bob Williams\",\n                    emoji: \"\\uD83E\\uDD14\",\n                    timestamp: \"2023-03-10 11:40\"\n                },\n                {\n                    id: 8,\n                    userId: 5,\n                    userName: \"Carol Davis\",\n                    emoji: \"\\uD83D\\uDC4E\",\n                    timestamp: \"2023-03-10 11:45\"\n                },\n                {\n                    id: 9,\n                    userId: 6,\n                    userName: \"Mike Johnson\",\n                    emoji: \"\\uD83D\\uDCB8\",\n                    timestamp: \"2023-03-10 11:50\"\n                }\n            ],\n            consensusScore: 45,\n            threadCount: 2\n        },\n        {\n            id: 3,\n            name: \"Custom Wood Frame Sofa\",\n            price: 0,\n            image: \"/images/placeholder.png\",\n            description: \"Hand-crafted wooden frame sofa with custom upholstery\",\n            merchant: null,\n            source: \"external\",\n            reactions: [\n                {\n                    id: 6,\n                    userId: 5,\n                    userName: \"Carol Davis\",\n                    emoji: \"\\uD83D\\uDE0D\",\n                    timestamp: \"2023-03-10 12:20\"\n                },\n                {\n                    id: 7,\n                    userId: 2,\n                    userName: \"John Doe\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 12:25\"\n                }\n            ],\n            consensusScore: 70,\n            threadCount: 1\n        }\n    ],\n    selectedProduct: null,\n    product: {\n        name: \"Premium Leather Sofa Set\",\n        price: 3500,\n        image: \"/images/placeholder.png\"\n    },\n    members: [\n        {\n            id: 1,\n            name: \"Jane Smith\",\n            isAdmin: true,\n            amountPaid: 850\n        },\n        {\n            id: 2,\n            name: \"John Doe\",\n            isAdmin: false,\n            amountPaid: 700\n        },\n        {\n            id: 3,\n            name: \"Alice Johnson\",\n            isAdmin: false,\n            amountPaid: 600\n        },\n        {\n            id: 4,\n            name: \"Bob Williams\",\n            isAdmin: false,\n            amountPaid: 0\n        },\n        {\n            id: 5,\n            name: \"Carol Davis\",\n            isAdmin: false,\n            amountPaid: 0\n        }\n    ],\n    amountPaid: 2150,\n    totalAmount: 3500,\n    expiresIn: \"5 days\",\n    status: \"manufacturing\",\n    manufacturingProgress: 65,\n    manufacturingUpdates: [\n        {\n            id: 1,\n            date: \"2023-03-15\",\n            title: \"Production Started\",\n            description: \"Materials sourced and production has begun.\",\n            images: [\n                \"/images/placeholder.png\"\n            ]\n        },\n        {\n            id: 2,\n            date: \"2023-03-18\",\n            title: \"Frame Assembly\",\n            description: \"Wooden frames are assembled and ready for upholstery.\",\n            images: [\n                \"/images/placeholder.png\"\n            ]\n        },\n        {\n            id: 3,\n            date: \"2023-03-21\",\n            title: \"Upholstery Progress\",\n            description: \"Leather upholstery is being applied to the frames.\",\n            images: [\n                \"/images/placeholder.png\"\n            ]\n        }\n    ],\n    messages: [\n        {\n            id: 1,\n            user: \"Jane Smith\",\n            userId: 1,\n            content: \"Welcome everyone to our group buy!\",\n            timestamp: \"2023-03-10 10:23\",\n            type: \"text\",\n            reactions: [\n                {\n                    id: 1,\n                    userId: 2,\n                    userName: \"John Doe\",\n                    emoji: \"\\uD83D\\uDC4B\",\n                    timestamp: \"2023-03-10 10:25\"\n                },\n                {\n                    id: 2,\n                    userId: 3,\n                    userName: \"Alice Johnson\",\n                    emoji: \"\\uD83C\\uDF89\",\n                    timestamp: \"2023-03-10 10:26\"\n                }\n            ]\n        },\n        {\n            id: 2,\n            user: \"John Doe\",\n            userId: 2,\n            content: \"Thanks for organizing this!\",\n            timestamp: \"2023-03-10 10:45\",\n            type: \"text\",\n            reactions: [\n                {\n                    id: 3,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"❤️\",\n                    timestamp: \"2023-03-10 10:46\"\n                }\n            ]\n        },\n        {\n            id: 3,\n            user: \"Alice Johnson\",\n            userId: 3,\n            content: \"I added a Premium Leather Sofa Set to our product suggestions. What do you all think?\",\n            timestamp: \"2023-03-10 11:15\",\n            type: \"product-suggestion\",\n            productRef: 1,\n            detectedProducts: [\n                {\n                    text: \"Premium Leather Sofa Set\",\n                    startIndex: 9,\n                    endIndex: 33,\n                    suggestedProductId: 1,\n                    confidence: 0.95\n                }\n            ],\n            reactions: [\n                {\n                    id: 4,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 11:20\"\n                },\n                {\n                    id: 5,\n                    userId: 2,\n                    userName: \"John Doe\",\n                    emoji: \"\\uD83E\\uDD14\",\n                    timestamp: \"2023-03-10 11:22\"\n                }\n            ]\n        },\n        {\n            id: 4,\n            user: \"Jane Smith\",\n            userId: 1,\n            content: \"I like it but it's a bit pricey. I found this fabric sectional that might be more budget-friendly.\",\n            timestamp: \"2023-03-10 11:30\",\n            type: \"product-suggestion\",\n            productRef: 2,\n            parentMessageId: 3,\n            threadId: \"product-1-discussion\",\n            detectedProducts: [\n                {\n                    text: \"fabric sectional\",\n                    startIndex: 55,\n                    endIndex: 70,\n                    suggestedProductId: 2,\n                    confidence: 0.88\n                }\n            ],\n            reactions: [\n                {\n                    id: 6,\n                    userId: 4,\n                    userName: \"Bob Williams\",\n                    emoji: \"\\uD83D\\uDCB0\",\n                    timestamp: \"2023-03-10 11:35\"\n                },\n                {\n                    id: 10,\n                    userId: 5,\n                    userName: \"Carol Davis\",\n                    emoji: \"\\uD83D\\uDC4E\",\n                    timestamp: \"2023-03-10 11:37\"\n                }\n            ]\n        },\n        {\n            id: 5,\n            user: \"Bob Williams\",\n            userId: 4,\n            content: \"I saw this custom sofa at a local craftsman's shop. Uploading a photo I took.\",\n            timestamp: \"2023-03-10 12:15\",\n            type: \"product-suggestion\",\n            productRef: 3,\n            attachment: \"/images/placeholder.png\",\n            detectedProducts: [\n                {\n                    text: \"custom sofa\",\n                    startIndex: 12,\n                    endIndex: 23,\n                    suggestedProductId: 3,\n                    confidence: 0.92\n                }\n            ],\n            reactions: [\n                {\n                    id: 7,\n                    userId: 5,\n                    userName: \"Carol Davis\",\n                    emoji: \"\\uD83D\\uDCF8\",\n                    timestamp: \"2023-03-10 12:20\"\n                },\n                {\n                    id: 8,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"\\uD83D\\uDC40\",\n                    timestamp: \"2023-03-10 12:22\"\n                }\n            ]\n        },\n        {\n            id: 6,\n            user: \"John Doe\",\n            userId: 2,\n            content: \"The leather sofa looks amazing! How's the delivery time?\",\n            timestamp: \"2023-03-10 12:30\",\n            type: \"text\",\n            parentMessageId: 3,\n            threadId: \"product-1-discussion\",\n            detectedProducts: [\n                {\n                    text: \"leather sofa\",\n                    startIndex: 4,\n                    endIndex: 16,\n                    suggestedProductId: 1,\n                    confidence: 0.9\n                }\n            ]\n        },\n        {\n            id: 7,\n            user: \"Carol Davis\",\n            userId: 5,\n            content: \"I'm really interested in the custom wood frame option. Can we get more details?\",\n            timestamp: \"2023-03-10 13:00\",\n            type: \"text\",\n            parentMessageId: 5,\n            threadId: \"product-3-discussion\",\n            detectedProducts: [\n                {\n                    text: \"custom wood frame\",\n                    startIndex: 30,\n                    endIndex: 47,\n                    suggestedProductId: 3,\n                    confidence: 0.85\n                }\n            ]\n        }\n    ]\n};\n// Helper function to get the appropriate default tab based on group stage\nconst getDefaultTab = (stage)=>{\n    switch(stage){\n        case \"suggestion\":\n            return \"discussion\"; // suggestions are now part of discussion\n        case \"discussion\":\n            return \"discussion\";\n        case \"payment\":\n            return \"payment\";\n        case \"manufacturing\":\n            return \"manufacturing\";\n        case \"shipping\":\n            return \"shipping\";\n        default:\n            return \"discussion\";\n    }\n};\nfunction GroupDetail(param) {\n    let { params } = param;\n    var _localProducts_find, _localProducts_find1, _localProducts_find2, _localProducts_find3, _localProducts_find4;\n    _s();\n    // In a real app, you would fetch the group data based on params.id\n    const defaultTab = getDefaultTab(groupData.stage);\n    const [selectedProductRef, setSelectedProductRef] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showProductSelector, setShowProductSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProductsOverview, setShowProductsOverview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messageText, setMessageText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Phase 2 state management\n    const [expandedThreads, setExpandedThreads] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [showEmojiPicker, setShowEmojiPicker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [replyingTo, setReplyingTo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentUserId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1); // Mock current user - Jane Smith\n    // Local state for dynamic updates (in a real app, this would be managed by a state management system)\n    const [localMessages, setLocalMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(groupData.messages);\n    const [localProducts, setLocalProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(groupData.suggestedProducts);\n    const [replyText, setReplyText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultTab);\n    const handleSendMessage = ()=>{\n        if (!messageText.trim()) return;\n        const newMessage = {\n            id: Date.now(),\n            user: \"Jane Smith\",\n            userId: currentUserId,\n            content: messageText,\n            timestamp: new Date().toLocaleString(),\n            type: selectedProductRef ? \"product-suggestion\" : \"text\",\n            productRef: selectedProductRef || undefined,\n            reactions: []\n        };\n        setLocalMessages((prev)=>[\n                ...prev,\n                newMessage\n            ]);\n        setMessageText(\"\");\n        setSelectedProductRef(null);\n    };\n    const handleSendReply = (parentMessageId)=>{\n        if (!replyText.trim()) return;\n        const parentMessage = localMessages.find((m)=>m.id === parentMessageId);\n        const newReply = {\n            id: Date.now(),\n            user: \"Jane Smith\",\n            userId: currentUserId,\n            content: replyText,\n            timestamp: new Date().toLocaleString(),\n            type: \"text\",\n            parentMessageId: parentMessageId,\n            threadId: (parentMessage === null || parentMessage === void 0 ? void 0 : parentMessage.threadId) || \"thread-\".concat(parentMessageId),\n            reactions: []\n        };\n        console.log(\"Creating reply:\", newReply);\n        console.log(\"Current messages before:\", localMessages.length);\n        setLocalMessages((prev)=>{\n            const updated = [\n                ...prev,\n                newReply\n            ];\n            console.log(\"Updated messages after:\", updated.length);\n            return updated;\n        });\n        // Auto-expand the thread to show the new reply\n        setExpandedThreads((prev)=>{\n            const newSet = new Set(prev);\n            newSet.add(\"thread-\".concat(parentMessageId));\n            return newSet;\n        });\n        setReplyText(\"\");\n        setReplyingTo(null);\n    };\n    const handleSelectProduct = (productId)=>{\n        setSelectedProductRef(productId);\n        setShowProductSelector(false);\n    };\n    // Phase 2 helper functions\n    const handleAddReaction = (emoji, messageId, productId)=>{\n        if (messageId) {\n            var _message_reactions;\n            // Check if user already has a reaction on this message\n            const message = localMessages.find((m)=>m.id === messageId);\n            const existingReaction = message === null || message === void 0 ? void 0 : (_message_reactions = message.reactions) === null || _message_reactions === void 0 ? void 0 : _message_reactions.find((r)=>r.userId === currentUserId);\n            setLocalMessages((prev)=>prev.map((msg)=>{\n                    if (msg.id === messageId) {\n                        let newReactions = msg.reactions || [];\n                        if (existingReaction) {\n                            // Replace existing reaction\n                            newReactions = newReactions.map((r)=>r.userId === currentUserId ? {\n                                    ...r,\n                                    emoji,\n                                    timestamp: new Date().toLocaleString()\n                                } : r);\n                        } else {\n                            // Add new reaction\n                            const newReaction = {\n                                id: Date.now(),\n                                userId: currentUserId,\n                                userName: \"Jane Smith\",\n                                emoji,\n                                timestamp: new Date().toLocaleString()\n                            };\n                            newReactions = [\n                                ...newReactions,\n                                newReaction\n                            ];\n                        }\n                        return {\n                            ...msg,\n                            reactions: newReactions\n                        };\n                    }\n                    return msg;\n                }));\n        }\n        if (productId) {\n            var _product_reactions;\n            // Check if user already has a reaction on this product\n            const product = localProducts.find((p)=>p.id === productId);\n            const existingReaction = product === null || product === void 0 ? void 0 : (_product_reactions = product.reactions) === null || _product_reactions === void 0 ? void 0 : _product_reactions.find((r)=>r.userId === currentUserId);\n            setLocalProducts((prev)=>prev.map((product)=>{\n                    if (product.id === productId) {\n                        let newReactions = product.reactions || [];\n                        if (existingReaction) {\n                            // Replace existing reaction\n                            newReactions = newReactions.map((r)=>r.userId === currentUserId ? {\n                                    ...r,\n                                    emoji,\n                                    timestamp: new Date().toLocaleString()\n                                } : r);\n                        } else {\n                            // Add new reaction\n                            const newReaction = {\n                                id: Date.now(),\n                                userId: currentUserId,\n                                userName: \"Jane Smith\",\n                                emoji,\n                                timestamp: new Date().toLocaleString()\n                            };\n                            newReactions = [\n                                ...newReactions,\n                                newReaction\n                            ];\n                        }\n                        return {\n                            ...product,\n                            reactions: newReactions,\n                            consensusScore: calculateConsensusScore(newReactions)\n                        };\n                    }\n                    return product;\n                }));\n        }\n        setShowEmojiPicker(null);\n    };\n    const handleDeleteMessage = (messageId)=>{\n        // Remove the message and any replies to it\n        setLocalMessages((prev)=>prev.filter((msg)=>msg.id !== messageId && msg.parentMessageId !== messageId));\n    };\n    const handleDeleteReaction = (reactionId, messageId, productId)=>{\n        if (messageId) {\n            setLocalMessages((prev)=>prev.map((msg)=>{\n                    var _msg_reactions;\n                    return msg.id === messageId ? {\n                        ...msg,\n                        reactions: ((_msg_reactions = msg.reactions) === null || _msg_reactions === void 0 ? void 0 : _msg_reactions.filter((r)=>r.id !== reactionId)) || []\n                    } : msg;\n                }));\n        }\n        if (productId) {\n            setLocalProducts((prev)=>prev.map((product)=>{\n                    if (product.id === productId) {\n                        var _product_reactions;\n                        const newReactions = ((_product_reactions = product.reactions) === null || _product_reactions === void 0 ? void 0 : _product_reactions.filter((r)=>r.id !== reactionId)) || [];\n                        return {\n                            ...product,\n                            reactions: newReactions,\n                            consensusScore: calculateConsensusScore(newReactions)\n                        };\n                    }\n                    return product;\n                }));\n        }\n    };\n    const toggleThread = (threadId)=>{\n        const newExpanded = new Set(expandedThreads);\n        if (newExpanded.has(threadId)) {\n            newExpanded.delete(threadId);\n        } else {\n            newExpanded.add(threadId);\n        }\n        setExpandedThreads(newExpanded);\n    };\n    const getThreadMessages = (parentMessageId)=>{\n        return localMessages.filter((msg)=>msg.parentMessageId === parentMessageId);\n    };\n    const getMainMessages = ()=>{\n        return localMessages.filter((msg)=>!msg.parentMessageId);\n    };\n    const getConsensusColor = (score)=>{\n        if (score >= 80) return \"text-green-600\";\n        if (score >= 60) return \"text-yellow-600\";\n        return \"text-red-600\";\n    };\n    const calculateConsensusScore = (reactions)=>{\n        if (!reactions || reactions.length === 0) return 0;\n        const positiveEmojis = [\n            \"\\uD83D\\uDC4D\",\n            \"❤️\",\n            \"\\uD83D\\uDE0D\",\n            \"\\uD83C\\uDF89\",\n            \"\\uD83D\\uDD25\",\n            \"\\uD83D\\uDCA1\",\n            \"✨\"\n        ];\n        const negativeEmojis = [\n            \"\\uD83D\\uDC4E\",\n            \"\\uD83D\\uDE15\",\n            \"❌\",\n            \"\\uD83D\\uDCB8\",\n            \"⚠️\",\n            \"\\uD83D\\uDEAB\"\n        ];\n        // neutralEmojis: [\"🤔\", \"👀\", \"📸\", \"👋\", \"💰\"] - treated as neutral (0.5 weight)\n        let positiveCount = 0;\n        let negativeCount = 0;\n        let neutralCount = 0;\n        reactions.forEach((reaction)=>{\n            if (positiveEmojis.includes(reaction.emoji)) {\n                positiveCount++;\n            } else if (negativeEmojis.includes(reaction.emoji)) {\n                negativeCount++;\n            } else {\n                neutralCount++;\n            }\n        });\n        const totalReactions = reactions.length;\n        const positiveWeight = positiveCount * 1.0;\n        const neutralWeight = neutralCount * 0.5;\n        const negativeWeight = negativeCount * 0.0;\n        const weightedScore = (positiveWeight + neutralWeight + negativeWeight) / totalReactions;\n        return Math.round(weightedScore * 100);\n    };\n    const renderDetectedProducts = (content, detectedProducts)=>{\n        if (!detectedProducts || detectedProducts.length === 0) {\n            return content;\n        }\n        let result = content;\n        let offset = 0;\n        detectedProducts.sort((a, b)=>a.startIndex - b.startIndex).forEach((detected)=>{\n            const start = detected.startIndex + offset;\n            const end = detected.endIndex + offset;\n            const productName = result.substring(start, end);\n            const replacement = '<span class=\"bg-blue-100 text-blue-800 px-1 rounded cursor-pointer hover:bg-blue-200\" data-product-id=\"'.concat(detected.suggestedProductId, '\">').concat(productName, \"</span>\");\n            result = result.substring(0, start) + replacement + result.substring(end);\n            offset += replacement.length - productName.length;\n        });\n        return result;\n    };\n    const renderStageIndicator = ()=>{\n        const stages = [\n            {\n                id: \"suggestion\",\n                label: \"Suggestions\"\n            },\n            {\n                id: \"discussion\",\n                label: \"Discussion\"\n            },\n            {\n                id: \"payment\",\n                label: \"Payment\"\n            },\n            {\n                id: \"manufacturing\",\n                label: \"Manufacturing\"\n            },\n            {\n                id: \"shipping\",\n                label: \"Shipping\"\n            }\n        ];\n        const currentIndex = stages.findIndex((s)=>s.id === groupData.stage);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-sm font-medium mb-2\",\n                    children: \"Current Stage\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 815,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full bg-secondary rounded-full h-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-primary h-2 rounded-full transition-all duration-500 ease-in-out\",\n                        style: {\n                            width: \"\".concat((currentIndex + 1) / stages.length * 100, \"%\")\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 817,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 816,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between mt-1 text-xs\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-primary font-medium\",\n                            children: stages[currentIndex].label\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 823,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-muted-foreground\",\n                            children: [\n                                \"Step \",\n                                currentIndex + 1,\n                                \" of \",\n                                stages.length\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 826,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 822,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n            lineNumber: 814,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layouts_mobile_layout__WEBPACK_IMPORTED_MODULE_2__.MobileLayout, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative bg-primary text-primary-foreground p-4 pb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                        href: \"/groups\",\n                        className: \"flex items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 838,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-1\",\n                                children: \"Back to Groups\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 839,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 837,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-fluid-xl font-bold\",\n                        children: groupData.name\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 841,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mt-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                size: 16,\n                                className: \"mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 843,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    groupData.members.length,\n                                    \" members\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 844,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mx-2\",\n                                children: \"•\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 845,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                size: 16,\n                                className: \"mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 846,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Expires in \",\n                                    groupData.expiresIn\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 847,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 842,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 836,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-4 pb-36\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"-mt-6 mb-6 relative z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"p-4\",\n                            children: renderStageIndicator()\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 853,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 852,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                        defaultValue: defaultTab,\n                        value: activeTab,\n                        onValueChange: setActiveTab,\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                                className: \"w-full grid grid-cols-4 gap-1 bg-transparent p-1 h-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                        value: \"discussion\",\n                                        className: \"flex flex-col items-center gap-2 p-2 h-auto bg-transparent hover:bg-muted/50 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground rounded-lg transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 rounded-full flex items-center justify-center transition-colors \".concat(activeTab === \"discussion\" ? \"bg-primary-foreground text-primary\" : \"bg-muted text-muted-foreground\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 874,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 867,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs font-medium\",\n                                                children: \"Discussion\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 876,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 863,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                        value: \"manufacturing\",\n                                        className: \"flex flex-col items-center gap-2 p-2 h-auto bg-transparent hover:bg-muted/50 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground rounded-lg transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 rounded-full flex items-center justify-center transition-colors \".concat(activeTab === \"manufacturing\" ? \"bg-primary-foreground text-primary\" : \"bg-muted text-muted-foreground\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 889,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 882,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs font-medium\",\n                                                children: \"Manufacturing\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 891,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 878,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                        value: \"members\",\n                                        className: \"flex flex-col items-center gap-2 p-2 h-auto bg-transparent hover:bg-muted/50 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground rounded-lg transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 rounded-full flex items-center justify-center transition-colors \".concat(activeTab === \"members\" ? \"bg-primary-foreground text-primary\" : \"bg-muted text-muted-foreground\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 904,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 897,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs font-medium\",\n                                                children: \"Members\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 906,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 893,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                        value: \"payment\",\n                                        className: \"flex flex-col items-center gap-2 p-2 h-auto bg-transparent hover:bg-muted/50 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground rounded-lg transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 rounded-full flex items-center justify-center transition-colors \".concat(activeTab === \"payment\" ? \"bg-primary-foreground text-primary\" : \"bg-muted text-muted-foreground\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 919,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 912,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs font-medium\",\n                                                children: \"Payment\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 921,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 908,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 862,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                value: \"discussion\",\n                                className: \"mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: getMainMessages().map((message)=>{\n                                        var _localProducts_find, _localProducts_find1, _localProducts_find2, _localProducts_find3, _localProducts_find4;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"rounded-lg p-3 \".concat(message.type === \"product-suggestion\" ? \"bg-blue-50 border border-blue-200\" : \"bg-muted\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-start mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: message.user\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 942,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        message.type === \"product-suggestion\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                            variant: \"secondary\",\n                                                                            className: \"text-xs\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                    className: \"h-3 w-3 mr-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 945,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                \"Product\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 944,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 941,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-muted-foreground\",\n                                                                            children: message.timestamp\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 951,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                            variant: \"ghost\",\n                                                                            size: \"icon\",\n                                                                            className: \"h-6 w-6\",\n                                                                            onClick: ()=>setShowEmojiPicker({\n                                                                                    messageId: message.id\n                                                                                }),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"h-3 w-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 962,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 954,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                            variant: \"ghost\",\n                                                                            size: \"icon\",\n                                                                            className: \"h-6 w-6\",\n                                                                            onClick: ()=>setReplyingTo(message.id),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                className: \"h-3 w-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 970,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 964,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        message.userId === currentUserId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                            variant: \"ghost\",\n                                                                            size: \"icon\",\n                                                                            className: \"h-6 w-6 text-red-500 hover:text-red-700\",\n                                                                            onClick: ()=>handleDeleteMessage(message.id),\n                                                                            title: \"Delete message\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                className: \"h-3 w-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 980,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 973,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 950,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 940,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm mb-2\",\n                                                            dangerouslySetInnerHTML: {\n                                                                __html: renderDetectedProducts(message.content, message.detectedProducts)\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 986,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        message.reactions && message.reactions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-1 mb-2\",\n                                                            children: Object.entries(message.reactions.reduce((acc, reaction)=>{\n                                                                if (!acc[reaction.emoji]) {\n                                                                    acc[reaction.emoji] = [];\n                                                                }\n                                                                acc[reaction.emoji].push(reaction);\n                                                                return acc;\n                                                            }, {})).map((param)=>{\n                                                                let [emoji, reactions] = param;\n                                                                const userReaction = reactions.find((r)=>r.userId === currentUserId);\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"h-6 px-2 text-xs relative group \".concat(userReaction ? \"bg-blue-50 border-blue-200\" : \"\"),\n                                                                    title: \"\".concat(reactions.map((r)=>r.userName).join(\", \"), \" reacted with \").concat(emoji),\n                                                                    onClick: ()=>{\n                                                                        if (userReaction) {\n                                                                            handleDeleteReaction(userReaction.id, message.id);\n                                                                        }\n                                                                    },\n                                                                    children: [\n                                                                        emoji,\n                                                                        \" \",\n                                                                        reactions.length,\n                                                                        userReaction && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-3 h-3 text-xs flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                                            children: \"\\xd7\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1034,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, emoji, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1013,\n                                                                    columnNumber: 29\n                                                                }, this);\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 998,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        getThreadMessages(message.id).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            className: \"text-xs text-blue-600 hover:text-blue-800 p-0 h-auto\",\n                                                            onClick: ()=>toggleThread(\"thread-\".concat(message.id)),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-3 w-3 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1052,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                getThreadMessages(message.id).length,\n                                                                \" replies\",\n                                                                expandedThreads.has(\"thread-\".concat(message.id)) ? \" ▼\" : \" ▶\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1046,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        message.productRef && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-2 p-3 bg-background rounded border border-border\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-16 h-16 bg-muted rounded overflow-hidden flex items-center justify-center mr-3\",\n                                                                        children: ((_localProducts_find = localProducts.find((p)=>p.id === message.productRef)) === null || _localProducts_find === void 0 ? void 0 : _localProducts_find.image) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"relative w-full h-full\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                                                                src: ((_localProducts_find1 = localProducts.find((p)=>p.id === message.productRef)) === null || _localProducts_find1 === void 0 ? void 0 : _localProducts_find1.image) || \"/images/placeholder.png\",\n                                                                                alt: ((_localProducts_find2 = localProducts.find((p)=>p.id === message.productRef)) === null || _localProducts_find2 === void 0 ? void 0 : _localProducts_find2.name) || \"Product\",\n                                                                                fill: true,\n                                                                                className: \"object-cover\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 1069,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1068,\n                                                                            columnNumber: 31\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-6 w-6 text-muted-foreground\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1085,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1064,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-start mb-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"font-medium text-sm\",\n                                                                                        children: (_localProducts_find3 = localProducts.find((p)=>p.id === message.productRef)) === null || _localProducts_find3 === void 0 ? void 0 : _localProducts_find3.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                        lineNumber: 1090,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center gap-1\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                            variant: \"ghost\",\n                                                                                            size: \"icon\",\n                                                                                            className: \"h-6 w-6\",\n                                                                                            onClick: ()=>setShowEmojiPicker({\n                                                                                                    productId: message.productRef\n                                                                                                }),\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                                className: \"h-3 w-3\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                                lineNumber: 1108,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                            lineNumber: 1098,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                        lineNumber: 1097,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 1089,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-muted-foreground text-sm mb-2\",\n                                                                                children: [\n                                                                                    \"$\",\n                                                                                    ((_localProducts_find4 = localProducts.find((p)=>p.id === message.productRef)) === null || _localProducts_find4 === void 0 ? void 0 : _localProducts_find4.price) || \"Price unavailable\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 1112,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            (()=>{\n                                                                                const product = localProducts.find((p)=>p.id === message.productRef);\n                                                                                return (product === null || product === void 0 ? void 0 : product.reactions) && product.reactions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex flex-wrap gap-1 mb-2\",\n                                                                                    children: Object.entries(product.reactions.reduce((acc, reaction)=>{\n                                                                                        if (!acc[reaction.emoji]) {\n                                                                                            acc[reaction.emoji] = [];\n                                                                                        }\n                                                                                        acc[reaction.emoji].push(reaction);\n                                                                                        return acc;\n                                                                                    }, {})).map((param)=>{\n                                                                                        let [emoji, reactions] = param;\n                                                                                        const userReaction = reactions.find((r)=>r.userId === currentUserId);\n                                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                            variant: \"outline\",\n                                                                                            size: \"sm\",\n                                                                                            className: \"h-6 px-2 text-xs relative group \".concat(userReaction ? \"bg-blue-50 border-blue-200\" : \"\"),\n                                                                                            title: \"\".concat(reactions.map((r)=>r.userName).join(\", \"), \" reacted with \").concat(emoji),\n                                                                                            onClick: ()=>{\n                                                                                                if (userReaction) {\n                                                                                                    handleDeleteReaction(userReaction.id, undefined, product.id);\n                                                                                                }\n                                                                                            },\n                                                                                            children: [\n                                                                                                emoji,\n                                                                                                \" \",\n                                                                                                reactions.length,\n                                                                                                userReaction && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-3 h-3 text-xs flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                                                                    children: \"\\xd7\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                                    lineNumber: 1172,\n                                                                                                    columnNumber: 45\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, emoji, true, {\n                                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                            lineNumber: 1148,\n                                                                                            columnNumber: 41\n                                                                                        }, this);\n                                                                                    })\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 1127,\n                                                                                    columnNumber: 35\n                                                                                }, this);\n                                                                            })(),\n                                                                            (()=>{\n                                                                                const product = localProducts.find((p)=>p.id === message.productRef);\n                                                                                return (product === null || product === void 0 ? void 0 : product.consensusScore) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center gap-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex-1 bg-gray-200 rounded-full h-2\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"h-2 rounded-full \".concat(product.consensusScore >= 80 ? \"bg-green-500\" : product.consensusScore >= 60 ? \"bg-yellow-500\" : \"bg-red-500\"),\n                                                                                                style: {\n                                                                                                    width: \"\".concat(product.consensusScore, \"%\")\n                                                                                                }\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                                lineNumber: 1193,\n                                                                                                columnNumber: 39\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                            lineNumber: 1192,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-xs font-medium \".concat(getConsensusColor(product.consensusScore)),\n                                                                                            children: [\n                                                                                                product.consensusScore,\n                                                                                                \"% consensus\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                            lineNumber: 1206,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 1191,\n                                                                                    columnNumber: 35\n                                                                                }, this);\n                                                                            })()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1088,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1063,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1062,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        message.attachment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full max-w-[200px] h-[150px] bg-muted rounded-md overflow-hidden relative\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 flex items-center justify-center text-muted-foreground\",\n                                                                    children: \"Image Attachment\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1225,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1224,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1223,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 933,\n                                                    columnNumber: 19\n                                                }, this),\n                                                expandedThreads.has(\"thread-\".concat(message.id)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-4 pl-4 border-l-2 border-gray-200 space-y-2\",\n                                                    children: getThreadMessages(message.id).map((threadMessage)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gray-50 rounded-lg p-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-start mb-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-sm\",\n                                                                            children: threadMessage.user\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1242,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-muted-foreground\",\n                                                                                    children: threadMessage.timestamp\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 1246,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                threadMessage.userId === currentUserId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                    variant: \"ghost\",\n                                                                                    size: \"icon\",\n                                                                                    className: \"h-4 w-4 text-red-500 hover:text-red-700\",\n                                                                                    onClick: ()=>handleDeleteMessage(threadMessage.id),\n                                                                                    title: \"Delete reply\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                        className: \"h-2 w-2\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                        lineNumber: 1259,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 1250,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1245,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1241,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm\",\n                                                                    dangerouslySetInnerHTML: {\n                                                                        __html: renderDetectedProducts(threadMessage.content, threadMessage.detectedProducts)\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1264,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, threadMessage.id, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1237,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1235,\n                                                    columnNumber: 21\n                                                }, this),\n                                                replyingTo === message.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-2 p-2 bg-gray-50 rounded border\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-muted-foreground mb-2\",\n                                                            children: [\n                                                                \"Replying to \",\n                                                                message.user\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1281,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    placeholder: \"Type your reply...\",\n                                                                    className: \"flex-1 h-8 rounded-l border border-input bg-background px-2 text-sm\",\n                                                                    value: replyText,\n                                                                    onChange: (e)=>setReplyText(e.target.value),\n                                                                    onKeyDown: (e)=>{\n                                                                        if (e.key === \"Enter\" && !e.shiftKey) {\n                                                                            e.preventDefault();\n                                                                            handleSendReply(message.id);\n                                                                        }\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1285,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>handleSendReply(message.id),\n                                                                    disabled: !replyText.trim(),\n                                                                    className: \"rounded-l-none\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1304,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1298,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    size: \"sm\",\n                                                                    variant: \"outline\",\n                                                                    onClick: ()=>{\n                                                                        setReplyingTo(null);\n                                                                        setReplyText(\"\");\n                                                                    },\n                                                                    children: \"Cancel\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1306,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1284,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1280,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, message.id, true, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 931,\n                                            columnNumber: 17\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 929,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 928,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                value: \"payment\",\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                className: \"pb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                    className: \"text-lg\",\n                                                    children: \"Payment Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1334,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1333,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full bg-secondary rounded-full h-3 mb-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-primary h-3 rounded-full\",\n                                                            style: {\n                                                                width: \"\".concat(groupData.amountPaid / groupData.totalAmount * 100, \"%\")\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1338,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1337,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"$\",\n                                                                    groupData.amountPaid,\n                                                                    \" raised\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1348,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"$\",\n                                                                    groupData.totalAmount,\n                                                                    \" goal\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1349,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1347,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-muted-foreground text-center\",\n                                                        children: [\n                                                            \"$\",\n                                                            groupData.totalAmount - groupData.amountPaid,\n                                                            \" remaining\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1351,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1336,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardFooter, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                    href: \"/groups/\".concat(params.id, \"/payment\"),\n                                                    className: \"w-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        className: \"w-full\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1358,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Make a Payment\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1357,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1356,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1355,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1332,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium mb-2\",\n                                        children: \"Payment History\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1365,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: groupData.members.filter((member)=>member.amountPaid > 0).map((member)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-muted p-3 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-8 h-8 rounded-full bg-secondary flex items-center justify-center\",\n                                                                    children: member.name.charAt(0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1373,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: member.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1377,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-muted-foreground\",\n                                                                            children: new Date().toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1378,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1376,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1372,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: [\n                                                                \"$\",\n                                                                member.amountPaid\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1383,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1371,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, member.id, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1370,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1366,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 1331,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                value: \"manufacturing\",\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Manufacturing Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1393,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-primary font-medium capitalize\",\n                                                        children: groupData.status\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1394,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1392,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-secondary rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-primary h-2 rounded-full\",\n                                                    style: {\n                                                        width: \"\".concat(groupData.manufacturingProgress, \"%\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1399,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1398,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mt-1 text-xs text-muted-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Production Started\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1405,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Ready for Shipping\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1406,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1404,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1391,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium\",\n                                                children: \"Latest Updates\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1411,\n                                                columnNumber: 15\n                                            }, this),\n                                            groupData.manufacturingUpdates.map((update)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                    className: \"overflow-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium\",\n                                                                        children: update.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1416,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: update.date\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1417,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1415,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm mb-2\",\n                                                                children: update.description\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1421,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-24 bg-muted flex items-center justify-center rounded\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-muted-foreground\",\n                                                                    children: \"Update Image\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1423,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1422,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1414,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, update.id, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1413,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1410,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 1390,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                value: \"members\",\n                                className: \"mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: groupData.members.map((member)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center p-3 bg-muted rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 rounded-full bg-secondary flex items-center justify-center\",\n                                                    children: member.name.charAt(0)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1440,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-3 flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: member.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1445,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                member.isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-2 px-2 py-1 bg-primary/10 text-primary text-xs rounded-full\",\n                                                                    children: \"Admin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1447,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1444,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-muted-foreground\",\n                                                            children: member.isAdmin ? \"Group Administrator\" : \"Member\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1452,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1443,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, member.id, true, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1436,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1434,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 1433,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 856,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 851,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                open: showProductSelector,\n                onOpenChange: setShowProductSelector,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                    className: \"sm:max-w-[425px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTitle, {\n                                    children: \"Select a Product\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1467,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogDescription, {\n                                    children: \"Choose a product to reference in your message\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1468,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1466,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3 mt-4 max-h-[300px] overflow-y-auto\",\n                            children: groupData.suggestedProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border rounded-md cursor-pointer hover:bg-muted flex items-center\",\n                                    onClick: ()=>handleSelectProduct(product.id),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-muted rounded overflow-hidden mr-3 flex items-center justify-center\",\n                                            children: product.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative w-full h-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                                    src: product.image || \"/images/placeholder.png\",\n                                                    alt: product.name,\n                                                    fill: true,\n                                                    className: \"object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1482,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1481,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-6 w-6 text-muted-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1490,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1479,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium\",\n                                                    children: product.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1494,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: product.price > 0 ? \"$\".concat(product.price) : \"Price unavailable\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1495,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1493,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, product.id, true, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1474,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1472,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 1465,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 1464,\n                columnNumber: 7\n            }, this),\n            activeTab === \"discussion\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-20 left-4 right-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        selectedProductRef && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-2 bg-white rounded-lg border border-border shadow-lg p-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-muted rounded overflow-hidden flex items-center justify-center mr-2\",\n                                        children: ((_localProducts_find = localProducts.find((p)=>p.id === selectedProductRef)) === null || _localProducts_find === void 0 ? void 0 : _localProducts_find.image) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative w-full h-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                                src: ((_localProducts_find1 = localProducts.find((p)=>p.id === selectedProductRef)) === null || _localProducts_find1 === void 0 ? void 0 : _localProducts_find1.image) || \"/images/placeholder.png\",\n                                                alt: ((_localProducts_find2 = localProducts.find((p)=>p.id === selectedProductRef)) === null || _localProducts_find2 === void 0 ? void 0 : _localProducts_find2.name) || \"Product\",\n                                                fill: true,\n                                                className: \"object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1519,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1518,\n                                            columnNumber: 23\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1535,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1515,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-xs\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium\",\n                                                children: (_localProducts_find3 = localProducts.find((p)=>p.id === selectedProductRef)) === null || _localProducts_find3 === void 0 ? void 0 : _localProducts_find3.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1539,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-muted-foreground\",\n                                                children: [\n                                                    \"$\",\n                                                    ((_localProducts_find4 = localProducts.find((p)=>p.id === selectedProductRef)) === null || _localProducts_find4 === void 0 ? void 0 : _localProducts_find4.price) || \"Price unavailable\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1545,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1538,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        className: \"h-6 w-6 rounded-full\",\n                                        type: \"button\",\n                                        onClick: ()=>setSelectedProductRef(null),\n                                        children: \"\\xd7\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1551,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 1514,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1513,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"icon\",\n                                            className: \"rounded-full h-10 w-10 bg-white shadow-lg border\",\n                                            type: \"button\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"file-upload\",\n                                                className: \"cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        id: \"file-upload\",\n                                                        type: \"file\",\n                                                        accept: \"image/*\",\n                                                        className: \"sr-only\",\n                                                        \"aria-label\": \"Upload image\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1575,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1582,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1574,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1568,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"icon\",\n                                            className: \"rounded-full h-10 w-10 bg-white shadow-lg border\",\n                                            type: \"button\",\n                                            onClick: ()=>setShowProductSelector(true),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1592,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1585,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1567,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Type your message...\",\n                                            className: \"w-full h-10 rounded-full border border-input bg-white px-4 pr-12 text-sm shadow-lg ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n                                            value: messageText,\n                                            onChange: (e)=>setMessageText(e.target.value),\n                                            onKeyDown: (e)=>{\n                                                if (e.key === \"Enter\" && !e.shiftKey) {\n                                                    e.preventDefault();\n                                                    handleSendMessage();\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1598,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            size: \"icon\",\n                                            className: \"absolute right-1 top-1 h-8 w-8 rounded-full\",\n                                            onClick: handleSendMessage,\n                                            disabled: !messageText.trim(),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1617,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1611,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1597,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>setShowProductsOverview(true),\n                                    className: \"rounded-full shadow-lg h-10 px-4 bg-white border text-foreground hover:bg-accent\",\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1628,\n                                            columnNumber: 17\n                                        }, this),\n                                        localProducts.length\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1622,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1565,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 1510,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 1509,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                open: showProductsOverview,\n                onOpenChange: setShowProductsOverview,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                    className: \"sm:max-w-[500px] max-h-[80vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTitle, {\n                                    children: \"Product Suggestions\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1643,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogDescription, {\n                                    children: [\n                                        localProducts.length,\n                                        \" products suggested by the group\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1644,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1642,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 gap-4 mt-4\",\n                            children: localProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    className: \"overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-20 h-20 bg-muted flex items-center justify-center\",\n                                                children: product.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative w-full h-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                                        src: product.image || \"/images/placeholder.png\",\n                                                        alt: product.name,\n                                                        fill: true,\n                                                        className: \"object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1655,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1654,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-6 w-6 text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1663,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1652,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-sm\",\n                                                                children: product.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1668,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs px-1.5 py-0.5 rounded \".concat(product.source === \"internal\" ? \"bg-primary/10 text-primary\" : \"bg-secondary text-secondary-foreground\"),\n                                                                        children: product.source === \"internal\" ? \"Catalog\" : \"External\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1670,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-6 w-6\",\n                                                                        onClick: ()=>setShowEmojiPicker({\n                                                                                productId: product.id\n                                                                            }),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1689,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1681,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1669,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1667,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground mb-2\",\n                                                        children: product.description\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1693,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    product.reactions && product.reactions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1 mb-2\",\n                                                        children: Object.entries(product.reactions.reduce((acc, reaction)=>{\n                                                            if (!acc[reaction.emoji]) {\n                                                                acc[reaction.emoji] = [];\n                                                            }\n                                                            acc[reaction.emoji].push(reaction);\n                                                            return acc;\n                                                        }, {})).map((param)=>{\n                                                            let [emoji, reactions] = param;\n                                                            const userReaction = reactions.find((r)=>r.userId === currentUserId);\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                className: \"h-5 px-1.5 text-xs relative group \".concat(userReaction ? \"bg-blue-50 border-blue-200\" : \"\"),\n                                                                title: \"\".concat(reactions.map((r)=>r.userName).join(\", \"), \" reacted with \").concat(emoji),\n                                                                onClick: ()=>{\n                                                                    if (userReaction) {\n                                                                        handleDeleteReaction(userReaction.id, undefined, product.id);\n                                                                    }\n                                                                },\n                                                                children: [\n                                                                    emoji,\n                                                                    \" \",\n                                                                    reactions.length,\n                                                                    userReaction && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-2 h-2 text-xs flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                                        children: \"\\xd7\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1736,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                ]\n                                                            }, emoji, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1714,\n                                                                columnNumber: 29\n                                                            }, this);\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1699,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    product.consensusScore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1 bg-gray-200 rounded-full h-1.5\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-1.5 rounded-full \".concat(product.consensusScore >= 80 ? \"bg-green-500\" : product.consensusScore >= 60 ? \"bg-yellow-500\" : \"bg-red-500\"),\n                                                                            style: {\n                                                                                width: \"\".concat(product.consensusScore, \"%\")\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1751,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1750,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs font-medium \".concat(getConsensusColor(product.consensusScore)),\n                                                                        children: [\n                                                                            product.consensusScore,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1762,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1749,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: [\n                                                                    \"Group consensus • \",\n                                                                    product.threadCount || 0,\n                                                                    \" \",\n                                                                    \"discussions\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1770,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1748,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm\",\n                                                                children: product.price > 0 ? \"$\".concat(product.price) : \"Price unavailable\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1778,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>{\n                                                                    setShowProductsOverview(false);\n                                                                // In a real app, this would scroll to the product in discussion\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1791,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Discuss\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1783,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1777,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1666,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1651,\n                                        columnNumber: 17\n                                    }, this)\n                                }, product.id, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1650,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1648,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 1641,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 1637,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                open: !!showEmojiPicker,\n                onOpenChange: ()=>setShowEmojiPicker(null),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                    className: \"sm:max-w-[300px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTitle, {\n                                    children: \"Add Reaction\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1810,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogDescription, {\n                                    children: \"Choose an emoji to react with\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1811,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1809,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-6 gap-2 mt-4\",\n                            children: [\n                                \"\\uD83D\\uDC4D\",\n                                \"\\uD83D\\uDC4E\",\n                                \"❤️\",\n                                \"\\uD83D\\uDE0D\",\n                                \"\\uD83E\\uDD14\",\n                                \"\\uD83D\\uDE15\",\n                                \"\\uD83D\\uDCB0\",\n                                \"\\uD83D\\uDCB8\",\n                                \"\\uD83C\\uDF89\",\n                                \"❌\",\n                                \"\\uD83D\\uDC40\",\n                                \"\\uD83D\\uDCF8\",\n                                \"\\uD83D\\uDC4B\",\n                                \"\\uD83D\\uDD25\",\n                                \"\\uD83D\\uDCA1\",\n                                \"✨\",\n                                \"⚠️\",\n                                \"\\uD83D\\uDEAB\"\n                            ].map((emoji)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    className: \"h-12 w-12 text-2xl hover:bg-accent\",\n                                    onClick: ()=>handleAddReaction(emoji, showEmojiPicker === null || showEmojiPicker === void 0 ? void 0 : showEmojiPicker.messageId, showEmojiPicker === null || showEmojiPicker === void 0 ? void 0 : showEmojiPicker.productId),\n                                    children: emoji\n                                }, emoji, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1834,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1813,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 1808,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 1804,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n        lineNumber: 835,\n        columnNumber: 5\n    }, this);\n}\n_s(GroupDetail, \"y5tFOxLAKt6oRJUuq3vfeDRltK4=\");\n_c = GroupDetail;\nvar _c;\n$RefreshReg$(_c, \"GroupDetail\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/groups/[id]/page.tsx\n"));

/***/ })

});