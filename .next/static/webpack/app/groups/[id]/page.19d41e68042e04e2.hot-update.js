"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/groups/[id]/page",{

/***/ "(app-pages-browser)/./app/groups/[id]/page.tsx":
/*!**********************************!*\
  !*** ./app/groups/[id]/page.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GroupDetail; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layouts_mobile_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layouts/mobile-layout */ \"(app-pages-browser)/./components/layouts/mobile-layout.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,UserMinus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,UserMinus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,UserMinus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,UserMinus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,UserMinus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,UserMinus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,UserMinus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smile.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,UserMinus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/reply.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,UserMinus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,UserMinus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,UserMinus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,UserMinus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,UserMinus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,UserMinus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,UserMinus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-minus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Clock,CreditCard,MessageCircle,MessageSquare,Reply,Send,ShoppingBag,Smile,Trash2,TrendingUp,Upload,UserMinus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Removed quote components - quotes functionality has been removed\n\n// Mock data - would be fetched based on [id] in real app\nconst groupData = {\n    id: 1,\n    name: \"Living Room Remodel Group\",\n    description: \"A collective purchase for premium living room furniture at wholesale prices.\",\n    stage: \"suggestion\",\n    suggestedProducts: [\n        {\n            id: 1,\n            name: \"Premium Leather Sofa Set\",\n            price: 3500,\n            image: \"/images/placeholder.png\",\n            description: \"Genuine leather sofa set with matching ottoman\",\n            merchant: \"Luxury Furniture Co.\",\n            source: \"internal\",\n            reactions: [\n                {\n                    id: 1,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 11:20\"\n                },\n                {\n                    id: 2,\n                    userId: 2,\n                    userName: \"John Doe\",\n                    emoji: \"❤️\",\n                    timestamp: \"2023-03-10 11:25\"\n                },\n                {\n                    id: 3,\n                    userId: 3,\n                    userName: \"Alice Johnson\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 11:30\"\n                }\n            ],\n            consensusScore: 85,\n            threadCount: 3\n        },\n        {\n            id: 2,\n            name: \"Modern Fabric Sectional\",\n            price: 2800,\n            image: \"/images/placeholder.png\",\n            description: \"L-shaped sectional with chaise lounge in premium fabric\",\n            merchant: \"Contemporary Home\",\n            source: \"internal\",\n            reactions: [\n                {\n                    id: 4,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 11:35\"\n                },\n                {\n                    id: 5,\n                    userId: 4,\n                    userName: \"Bob Williams\",\n                    emoji: \"\\uD83E\\uDD14\",\n                    timestamp: \"2023-03-10 11:40\"\n                },\n                {\n                    id: 8,\n                    userId: 5,\n                    userName: \"Carol Davis\",\n                    emoji: \"\\uD83D\\uDC4E\",\n                    timestamp: \"2023-03-10 11:45\"\n                },\n                {\n                    id: 9,\n                    userId: 6,\n                    userName: \"Mike Johnson\",\n                    emoji: \"\\uD83D\\uDCB8\",\n                    timestamp: \"2023-03-10 11:50\"\n                }\n            ],\n            consensusScore: 45,\n            threadCount: 2\n        },\n        {\n            id: 3,\n            name: \"Custom Wood Frame Sofa\",\n            price: 0,\n            image: \"/images/placeholder.png\",\n            description: \"Hand-crafted wooden frame sofa with custom upholstery\",\n            merchant: null,\n            source: \"external\",\n            reactions: [\n                {\n                    id: 6,\n                    userId: 5,\n                    userName: \"Carol Davis\",\n                    emoji: \"\\uD83D\\uDE0D\",\n                    timestamp: \"2023-03-10 12:20\"\n                },\n                {\n                    id: 7,\n                    userId: 2,\n                    userName: \"John Doe\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 12:25\"\n                }\n            ],\n            consensusScore: 70,\n            threadCount: 1\n        }\n    ],\n    selectedProduct: null,\n    product: {\n        name: \"Premium Leather Sofa Set\",\n        price: 3500,\n        image: \"/images/placeholder.png\"\n    },\n    members: [\n        {\n            id: 1,\n            name: \"Jane Smith\",\n            isAdmin: true,\n            amountPaid: 850,\n            status: \"active\"\n        },\n        {\n            id: 2,\n            name: \"John Doe\",\n            isAdmin: false,\n            amountPaid: 700,\n            status: \"active\"\n        },\n        {\n            id: 3,\n            name: \"Alice Johnson\",\n            isAdmin: false,\n            amountPaid: 600,\n            status: \"active\"\n        },\n        {\n            id: 4,\n            name: \"Bob Williams\",\n            isAdmin: false,\n            amountPaid: 0,\n            status: \"pending\"\n        },\n        {\n            id: 5,\n            name: \"Carol Davis\",\n            isAdmin: false,\n            amountPaid: 0,\n            status: \"inactive\"\n        }\n    ],\n    amountPaid: 2150,\n    totalAmount: 3500,\n    createdDate: \"2023-03-05\",\n    isActive: true,\n    status: \"manufacturing\",\n    manufacturingProgress: 65,\n    manufacturingUpdates: [\n        {\n            id: 1,\n            date: \"2023-03-15\",\n            title: \"Production Started\",\n            description: \"Materials sourced and production has begun.\",\n            images: [\n                \"/images/placeholder.png\"\n            ]\n        },\n        {\n            id: 2,\n            date: \"2023-03-18\",\n            title: \"Frame Assembly\",\n            description: \"Wooden frames are assembled and ready for upholstery.\",\n            images: [\n                \"/images/placeholder.png\"\n            ]\n        },\n        {\n            id: 3,\n            date: \"2023-03-21\",\n            title: \"Upholstery Progress\",\n            description: \"Leather upholstery is being applied to the frames.\",\n            images: [\n                \"/images/placeholder.png\"\n            ]\n        }\n    ],\n    messages: [\n        {\n            id: 1,\n            user: \"Jane Smith\",\n            userId: 1,\n            content: \"Welcome everyone to our group buy!\",\n            timestamp: \"2023-03-10 10:23\",\n            type: \"text\",\n            reactions: [\n                {\n                    id: 1,\n                    userId: 2,\n                    userName: \"John Doe\",\n                    emoji: \"\\uD83D\\uDC4B\",\n                    timestamp: \"2023-03-10 10:25\"\n                },\n                {\n                    id: 2,\n                    userId: 3,\n                    userName: \"Alice Johnson\",\n                    emoji: \"\\uD83C\\uDF89\",\n                    timestamp: \"2023-03-10 10:26\"\n                }\n            ]\n        },\n        {\n            id: 2,\n            user: \"John Doe\",\n            userId: 2,\n            content: \"Thanks for organizing this!\",\n            timestamp: \"2023-03-10 10:45\",\n            type: \"text\",\n            reactions: [\n                {\n                    id: 3,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"❤️\",\n                    timestamp: \"2023-03-10 10:46\"\n                }\n            ]\n        },\n        {\n            id: 3,\n            user: \"Alice Johnson\",\n            userId: 3,\n            content: \"I added a Premium Leather Sofa Set to our product suggestions. What do you all think?\",\n            timestamp: \"2023-03-10 11:15\",\n            type: \"product-suggestion\",\n            productRef: 1,\n            detectedProducts: [\n                {\n                    text: \"Premium Leather Sofa Set\",\n                    startIndex: 9,\n                    endIndex: 33,\n                    suggestedProductId: 1,\n                    confidence: 0.95\n                }\n            ],\n            reactions: [\n                {\n                    id: 4,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"\\uD83D\\uDC4D\",\n                    timestamp: \"2023-03-10 11:20\"\n                },\n                {\n                    id: 5,\n                    userId: 2,\n                    userName: \"John Doe\",\n                    emoji: \"\\uD83E\\uDD14\",\n                    timestamp: \"2023-03-10 11:22\"\n                }\n            ]\n        },\n        {\n            id: 4,\n            user: \"Jane Smith\",\n            userId: 1,\n            content: \"I like it but it's a bit pricey. I found this fabric sectional that might be more budget-friendly.\",\n            timestamp: \"2023-03-10 11:30\",\n            type: \"product-suggestion\",\n            productRef: 2,\n            parentMessageId: 3,\n            threadId: \"product-1-discussion\",\n            detectedProducts: [\n                {\n                    text: \"fabric sectional\",\n                    startIndex: 55,\n                    endIndex: 70,\n                    suggestedProductId: 2,\n                    confidence: 0.88\n                }\n            ],\n            reactions: [\n                {\n                    id: 6,\n                    userId: 4,\n                    userName: \"Bob Williams\",\n                    emoji: \"\\uD83D\\uDCB0\",\n                    timestamp: \"2023-03-10 11:35\"\n                },\n                {\n                    id: 10,\n                    userId: 5,\n                    userName: \"Carol Davis\",\n                    emoji: \"\\uD83D\\uDC4E\",\n                    timestamp: \"2023-03-10 11:37\"\n                }\n            ]\n        },\n        {\n            id: 5,\n            user: \"Bob Williams\",\n            userId: 4,\n            content: \"I saw this custom sofa at a local craftsman's shop. Uploading a photo I took.\",\n            timestamp: \"2023-03-10 12:15\",\n            type: \"product-suggestion\",\n            productRef: 3,\n            attachment: \"/images/placeholder.png\",\n            detectedProducts: [\n                {\n                    text: \"custom sofa\",\n                    startIndex: 12,\n                    endIndex: 23,\n                    suggestedProductId: 3,\n                    confidence: 0.92\n                }\n            ],\n            reactions: [\n                {\n                    id: 7,\n                    userId: 5,\n                    userName: \"Carol Davis\",\n                    emoji: \"\\uD83D\\uDCF8\",\n                    timestamp: \"2023-03-10 12:20\"\n                },\n                {\n                    id: 8,\n                    userId: 1,\n                    userName: \"Jane Smith\",\n                    emoji: \"\\uD83D\\uDC40\",\n                    timestamp: \"2023-03-10 12:22\"\n                }\n            ]\n        },\n        {\n            id: 6,\n            user: \"John Doe\",\n            userId: 2,\n            content: \"The leather sofa looks amazing! How's the delivery time?\",\n            timestamp: \"2023-03-10 12:30\",\n            type: \"text\",\n            parentMessageId: 3,\n            threadId: \"product-1-discussion\",\n            detectedProducts: [\n                {\n                    text: \"leather sofa\",\n                    startIndex: 4,\n                    endIndex: 16,\n                    suggestedProductId: 1,\n                    confidence: 0.9\n                }\n            ]\n        },\n        {\n            id: 7,\n            user: \"Carol Davis\",\n            userId: 5,\n            content: \"I'm really interested in the custom wood frame option. Can we get more details?\",\n            timestamp: \"2023-03-10 13:00\",\n            type: \"text\",\n            parentMessageId: 5,\n            threadId: \"product-3-discussion\",\n            detectedProducts: [\n                {\n                    text: \"custom wood frame\",\n                    startIndex: 30,\n                    endIndex: 47,\n                    suggestedProductId: 3,\n                    confidence: 0.85\n                }\n            ]\n        }\n    ]\n};\n// Helper function to get the appropriate default tab based on group stage\nconst getDefaultTab = (stage)=>{\n    switch(stage){\n        case \"suggestion\":\n            return \"discussion\"; // suggestions are now part of discussion\n        case \"discussion\":\n            return \"discussion\";\n        case \"payment\":\n            return \"payment\";\n        case \"manufacturing\":\n            return \"manufacturing\";\n        case \"shipping\":\n            return \"shipping\";\n        default:\n            return \"discussion\";\n    }\n};\nfunction GroupDetail(param) {\n    let { params } = param;\n    var _localProducts_find, _localProducts_find1, _localProducts_find2, _localProducts_find3, _localProducts_find4;\n    _s();\n    // In a real app, you would fetch the group data based on params.id\n    const defaultTab = getDefaultTab(groupData.stage);\n    const [selectedProductRef, setSelectedProductRef] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showProductSelector, setShowProductSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProductsOverview, setShowProductsOverview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messageText, setMessageText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Phase 2 state management\n    const [expandedThreads, setExpandedThreads] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [showEmojiPicker, setShowEmojiPicker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [replyingTo, setReplyingTo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentUserId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1); // Mock current user - Jane Smith\n    // Local state for dynamic updates (in a real app, this would be managed by a state management system)\n    const [localMessages, setLocalMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(groupData.messages);\n    const [localProducts, setLocalProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(groupData.suggestedProducts);\n    const [replyText, setReplyText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultTab);\n    // Local state for member management\n    const [localMembers, setLocalMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(groupData.members);\n    const handleSendMessage = ()=>{\n        if (!messageText.trim()) return;\n        const newMessage = {\n            id: Date.now(),\n            user: \"Jane Smith\",\n            userId: currentUserId,\n            content: messageText,\n            timestamp: new Date().toLocaleString(),\n            type: selectedProductRef ? \"product-suggestion\" : \"text\",\n            productRef: selectedProductRef || undefined,\n            reactions: []\n        };\n        setLocalMessages((prev)=>[\n                ...prev,\n                newMessage\n            ]);\n        setMessageText(\"\");\n        setSelectedProductRef(null);\n    };\n    const handleSendReply = (parentMessageId)=>{\n        if (!replyText.trim()) return;\n        const parentMessage = localMessages.find((m)=>m.id === parentMessageId);\n        const newReply = {\n            id: Date.now(),\n            user: \"Jane Smith\",\n            userId: currentUserId,\n            content: replyText,\n            timestamp: new Date().toLocaleString(),\n            type: \"text\",\n            parentMessageId: parentMessageId,\n            threadId: (parentMessage === null || parentMessage === void 0 ? void 0 : parentMessage.threadId) || \"thread-\".concat(parentMessageId),\n            reactions: []\n        };\n        console.log(\"Creating reply:\", newReply);\n        console.log(\"Current messages before:\", localMessages.length);\n        setLocalMessages((prev)=>{\n            const updated = [\n                ...prev,\n                newReply\n            ];\n            console.log(\"Updated messages after:\", updated.length);\n            return updated;\n        });\n        // Auto-expand the thread to show the new reply\n        setExpandedThreads((prev)=>{\n            const newSet = new Set(prev);\n            newSet.add(\"thread-\".concat(parentMessageId));\n            return newSet;\n        });\n        setReplyText(\"\");\n        setReplyingTo(null);\n    };\n    const handleSelectProduct = (productId)=>{\n        setSelectedProductRef(productId);\n        setShowProductSelector(false);\n    };\n    // Member status management (admin only)\n    const handleUpdateMemberStatus = (memberId, newStatus)=>{\n        // Only admins can update member status\n        const currentUser = localMembers.find((m)=>m.id === currentUserId);\n        if (!(currentUser === null || currentUser === void 0 ? void 0 : currentUser.isAdmin)) return;\n        setLocalMembers((prev)=>prev.map((member)=>member.id === memberId ? {\n                    ...member,\n                    status: newStatus\n                } : member));\n    };\n    // Helper function to get status styling\n    const getStatusStyling = (status)=>{\n        switch(status){\n            case \"active\":\n                return \"bg-green-500/20 text-green-700 border-green-200\";\n            case \"inactive\":\n                return \"bg-gray-500/20 text-gray-700 border-gray-200\";\n            case \"pending\":\n                return \"bg-yellow-500/20 text-yellow-700 border-yellow-200\";\n            case \"removed\":\n                return \"bg-red-500/20 text-red-700 border-red-200\";\n            default:\n                return \"bg-gray-500/20 text-gray-700 border-gray-200\";\n        }\n    };\n    // Phase 2 helper functions\n    const handleAddReaction = (emoji, messageId, productId)=>{\n        if (messageId) {\n            var _message_reactions;\n            // Check if user already has a reaction on this message\n            const message = localMessages.find((m)=>m.id === messageId);\n            const existingReaction = message === null || message === void 0 ? void 0 : (_message_reactions = message.reactions) === null || _message_reactions === void 0 ? void 0 : _message_reactions.find((r)=>r.userId === currentUserId);\n            setLocalMessages((prev)=>prev.map((msg)=>{\n                    if (msg.id === messageId) {\n                        let newReactions = msg.reactions || [];\n                        if (existingReaction) {\n                            // Replace existing reaction\n                            newReactions = newReactions.map((r)=>r.userId === currentUserId ? {\n                                    ...r,\n                                    emoji,\n                                    timestamp: new Date().toLocaleString()\n                                } : r);\n                        } else {\n                            // Add new reaction\n                            const newReaction = {\n                                id: Date.now(),\n                                userId: currentUserId,\n                                userName: \"Jane Smith\",\n                                emoji,\n                                timestamp: new Date().toLocaleString()\n                            };\n                            newReactions = [\n                                ...newReactions,\n                                newReaction\n                            ];\n                        }\n                        return {\n                            ...msg,\n                            reactions: newReactions\n                        };\n                    }\n                    return msg;\n                }));\n        }\n        if (productId) {\n            var _product_reactions;\n            // Check if user already has a reaction on this product\n            const product = localProducts.find((p)=>p.id === productId);\n            const existingReaction = product === null || product === void 0 ? void 0 : (_product_reactions = product.reactions) === null || _product_reactions === void 0 ? void 0 : _product_reactions.find((r)=>r.userId === currentUserId);\n            setLocalProducts((prev)=>prev.map((product)=>{\n                    if (product.id === productId) {\n                        let newReactions = product.reactions || [];\n                        if (existingReaction) {\n                            // Replace existing reaction\n                            newReactions = newReactions.map((r)=>r.userId === currentUserId ? {\n                                    ...r,\n                                    emoji,\n                                    timestamp: new Date().toLocaleString()\n                                } : r);\n                        } else {\n                            // Add new reaction\n                            const newReaction = {\n                                id: Date.now(),\n                                userId: currentUserId,\n                                userName: \"Jane Smith\",\n                                emoji,\n                                timestamp: new Date().toLocaleString()\n                            };\n                            newReactions = [\n                                ...newReactions,\n                                newReaction\n                            ];\n                        }\n                        return {\n                            ...product,\n                            reactions: newReactions,\n                            consensusScore: calculateConsensusScore(newReactions)\n                        };\n                    }\n                    return product;\n                }));\n        }\n        setShowEmojiPicker(null);\n    };\n    const handleDeleteMessage = (messageId)=>{\n        // Remove the message and any replies to it\n        setLocalMessages((prev)=>prev.filter((msg)=>msg.id !== messageId && msg.parentMessageId !== messageId));\n    };\n    const handleDeleteReaction = (reactionId, messageId, productId)=>{\n        if (messageId) {\n            setLocalMessages((prev)=>prev.map((msg)=>{\n                    var _msg_reactions;\n                    return msg.id === messageId ? {\n                        ...msg,\n                        reactions: ((_msg_reactions = msg.reactions) === null || _msg_reactions === void 0 ? void 0 : _msg_reactions.filter((r)=>r.id !== reactionId)) || []\n                    } : msg;\n                }));\n        }\n        if (productId) {\n            setLocalProducts((prev)=>prev.map((product)=>{\n                    if (product.id === productId) {\n                        var _product_reactions;\n                        const newReactions = ((_product_reactions = product.reactions) === null || _product_reactions === void 0 ? void 0 : _product_reactions.filter((r)=>r.id !== reactionId)) || [];\n                        return {\n                            ...product,\n                            reactions: newReactions,\n                            consensusScore: calculateConsensusScore(newReactions)\n                        };\n                    }\n                    return product;\n                }));\n        }\n    };\n    const toggleThread = (threadId)=>{\n        const newExpanded = new Set(expandedThreads);\n        if (newExpanded.has(threadId)) {\n            newExpanded.delete(threadId);\n        } else {\n            newExpanded.add(threadId);\n        }\n        setExpandedThreads(newExpanded);\n    };\n    const getThreadMessages = (parentMessageId)=>{\n        return localMessages.filter((msg)=>msg.parentMessageId === parentMessageId);\n    };\n    const getMainMessages = ()=>{\n        return localMessages.filter((msg)=>!msg.parentMessageId);\n    };\n    const getConsensusColor = (score)=>{\n        if (score >= 80) return \"text-green-600\";\n        if (score >= 60) return \"text-yellow-600\";\n        return \"text-red-600\";\n    };\n    const calculateConsensusScore = (reactions)=>{\n        if (!reactions || reactions.length === 0) return 0;\n        const positiveEmojis = [\n            \"\\uD83D\\uDC4D\",\n            \"❤️\",\n            \"\\uD83D\\uDE0D\",\n            \"\\uD83C\\uDF89\",\n            \"\\uD83D\\uDD25\",\n            \"\\uD83D\\uDCA1\",\n            \"✨\"\n        ];\n        const negativeEmojis = [\n            \"\\uD83D\\uDC4E\",\n            \"\\uD83D\\uDE15\",\n            \"❌\",\n            \"\\uD83D\\uDCB8\",\n            \"⚠️\",\n            \"\\uD83D\\uDEAB\"\n        ];\n        // neutralEmojis: [\"🤔\", \"👀\", \"📸\", \"👋\", \"💰\"] - treated as neutral (0.5 weight)\n        let positiveCount = 0;\n        let negativeCount = 0;\n        let neutralCount = 0;\n        reactions.forEach((reaction)=>{\n            if (positiveEmojis.includes(reaction.emoji)) {\n                positiveCount++;\n            } else if (negativeEmojis.includes(reaction.emoji)) {\n                negativeCount++;\n            } else {\n                neutralCount++;\n            }\n        });\n        const totalReactions = reactions.length;\n        const positiveWeight = positiveCount * 1.0;\n        const neutralWeight = neutralCount * 0.5;\n        const negativeWeight = negativeCount * 0.0;\n        const weightedScore = (positiveWeight + neutralWeight + negativeWeight) / totalReactions;\n        return Math.round(weightedScore * 100);\n    };\n    const renderDetectedProducts = (content, detectedProducts)=>{\n        if (!detectedProducts || detectedProducts.length === 0) {\n            return content;\n        }\n        let result = content;\n        let offset = 0;\n        detectedProducts.sort((a, b)=>a.startIndex - b.startIndex).forEach((detected)=>{\n            const start = detected.startIndex + offset;\n            const end = detected.endIndex + offset;\n            const productName = result.substring(start, end);\n            const replacement = '<span class=\"bg-blue-100 text-blue-800 px-1 rounded cursor-pointer hover:bg-blue-200\" data-product-id=\"'.concat(detected.suggestedProductId, '\">').concat(productName, \"</span>\");\n            result = result.substring(0, start) + replacement + result.substring(end);\n            offset += replacement.length - productName.length;\n        });\n        return result;\n    };\n    const renderStageIndicator = ()=>{\n        const stages = [\n            {\n                id: \"suggestion\",\n                label: \"Suggestions\"\n            },\n            {\n                id: \"discussion\",\n                label: \"Discussion\"\n            },\n            {\n                id: \"payment\",\n                label: \"Payment\"\n            },\n            {\n                id: \"manufacturing\",\n                label: \"Manufacturing\"\n            },\n            {\n                id: \"shipping\",\n                label: \"Shipping\"\n            }\n        ];\n        const currentIndex = stages.findIndex((s)=>s.id === groupData.stage);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-sm font-medium mb-2\",\n                    children: \"Current Stage\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 858,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full bg-secondary rounded-full h-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-primary h-2 rounded-full transition-all duration-500 ease-in-out\",\n                        style: {\n                            width: \"\".concat((currentIndex + 1) / stages.length * 100, \"%\")\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 860,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 859,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between mt-1 text-xs\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-primary font-medium\",\n                            children: stages[currentIndex].label\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 866,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-muted-foreground\",\n                            children: [\n                                \"Step \",\n                                currentIndex + 1,\n                                \" of \",\n                                stages.length\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 869,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 865,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n            lineNumber: 857,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layouts_mobile_layout__WEBPACK_IMPORTED_MODULE_2__.MobileLayout, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative bg-primary text-primary-foreground p-4 pb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-fluid-xl font-bold\",\n                        children: groupData.name\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 880,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mt-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                size: 16,\n                                className: \"mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 882,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    groupData.members.length,\n                                    \" members\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 883,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mx-2\",\n                                children: \"•\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 884,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                size: 16,\n                                className: \"mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 885,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Created\",\n                                    \" \",\n                                    new Date(groupData.createdDate).toLocaleDateString(\"en-US\", {\n                                        year: \"numeric\",\n                                        month: \"short\",\n                                        day: \"numeric\"\n                                    })\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 886,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mx-2\",\n                                children: \"•\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 894,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"px-2 py-1 rounded-full text-xs \".concat(groupData.isActive ? \"bg-green-500/20 text-green-300\" : \"bg-gray-500/20 text-gray-300\"),\n                                children: groupData.isActive ? \"Active\" : \"Inactive\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 895,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 881,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 879,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-4 pb-36\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"p-4\",\n                            children: renderStageIndicator()\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 909,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 908,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                        defaultValue: defaultTab,\n                        value: activeTab,\n                        onValueChange: setActiveTab,\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                                className: \"w-full grid grid-cols-4 gap-1 bg-transparent p-1 h-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                        value: \"discussion\",\n                                        className: \"flex flex-col items-center gap-2 p-2 h-auto bg-transparent hover:bg-muted/50 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground rounded-lg transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 rounded-full flex items-center justify-center transition-colors \".concat(activeTab === \"discussion\" ? \"bg-primary-foreground text-primary\" : \"bg-muted text-muted-foreground\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 930,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 923,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs font-medium\",\n                                                children: \"Discussion\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 932,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 919,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                        value: \"manufacturing\",\n                                        className: \"flex flex-col items-center gap-2 p-2 h-auto bg-transparent hover:bg-muted/50 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground rounded-lg transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 rounded-full flex items-center justify-center transition-colors \".concat(activeTab === \"manufacturing\" ? \"bg-primary-foreground text-primary\" : \"bg-muted text-muted-foreground\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 945,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 938,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs font-medium\",\n                                                children: \"Manufacturing\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 947,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 934,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                        value: \"members\",\n                                        className: \"flex flex-col items-center gap-2 p-2 h-auto bg-transparent hover:bg-muted/50 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground rounded-lg transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 rounded-full flex items-center justify-center transition-colors \".concat(activeTab === \"members\" ? \"bg-primary-foreground text-primary\" : \"bg-muted text-muted-foreground\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 960,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 953,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs font-medium\",\n                                                children: \"Members\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 962,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 949,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                        value: \"payment\",\n                                        className: \"flex flex-col items-center gap-2 p-2 h-auto bg-transparent hover:bg-muted/50 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground rounded-lg transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 rounded-full flex items-center justify-center transition-colors \".concat(activeTab === \"payment\" ? \"bg-primary-foreground text-primary\" : \"bg-muted text-muted-foreground\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 975,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 968,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs font-medium\",\n                                                children: \"Payment\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 977,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 964,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 918,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                value: \"discussion\",\n                                className: \"mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: getMainMessages().map((message)=>{\n                                        var _localProducts_find, _localProducts_find1, _localProducts_find2, _localProducts_find3, _localProducts_find4;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"rounded-lg p-3 \".concat(message.type === \"product-suggestion\" ? \"bg-blue-50 border border-blue-200\" : \"bg-muted\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-start mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: message.user\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 998,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        message.type === \"product-suggestion\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                            variant: \"secondary\",\n                                                                            className: \"text-xs\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                    className: \"h-3 w-3 mr-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 1001,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                \"Product\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1000,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 997,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-muted-foreground\",\n                                                                            children: message.timestamp\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1007,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                            variant: \"ghost\",\n                                                                            size: \"icon\",\n                                                                            className: \"h-6 w-6\",\n                                                                            onClick: ()=>setShowEmojiPicker({\n                                                                                    messageId: message.id\n                                                                                }),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                className: \"h-3 w-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 1018,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1010,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                            variant: \"ghost\",\n                                                                            size: \"icon\",\n                                                                            className: \"h-6 w-6\",\n                                                                            onClick: ()=>setReplyingTo(message.id),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"h-3 w-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 1026,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1020,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        message.userId === currentUserId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                            variant: \"ghost\",\n                                                                            size: \"icon\",\n                                                                            className: \"h-6 w-6 text-red-500 hover:text-red-700\",\n                                                                            onClick: ()=>handleDeleteMessage(message.id),\n                                                                            title: \"Delete message\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                className: \"h-3 w-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 1036,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1029,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1006,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 996,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm mb-2\",\n                                                            dangerouslySetInnerHTML: {\n                                                                __html: renderDetectedProducts(message.content, message.detectedProducts)\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1042,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        message.reactions && message.reactions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-1 mb-2\",\n                                                            children: Object.entries(message.reactions.reduce((acc, reaction)=>{\n                                                                if (!acc[reaction.emoji]) {\n                                                                    acc[reaction.emoji] = [];\n                                                                }\n                                                                acc[reaction.emoji].push(reaction);\n                                                                return acc;\n                                                            }, {})).map((param)=>{\n                                                                let [emoji, reactions] = param;\n                                                                const userReaction = reactions.find((r)=>r.userId === currentUserId);\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"h-6 px-2 text-xs relative group \".concat(userReaction ? \"bg-blue-50 border-blue-200\" : \"\"),\n                                                                    title: \"\".concat(reactions.map((r)=>r.userName).join(\", \"), \" reacted with \").concat(emoji),\n                                                                    onClick: ()=>{\n                                                                        if (userReaction) {\n                                                                            handleDeleteReaction(userReaction.id, message.id);\n                                                                        }\n                                                                    },\n                                                                    children: [\n                                                                        emoji,\n                                                                        \" \",\n                                                                        reactions.length,\n                                                                        userReaction && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-3 h-3 text-xs flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                                            children: \"\\xd7\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1090,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, emoji, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1069,\n                                                                    columnNumber: 29\n                                                                }, this);\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1054,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        getThreadMessages(message.id).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            className: \"text-xs text-blue-600 hover:text-blue-800 p-0 h-auto\",\n                                                            onClick: ()=>toggleThread(\"thread-\".concat(message.id)),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-3 w-3 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1108,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                getThreadMessages(message.id).length,\n                                                                \" replies\",\n                                                                expandedThreads.has(\"thread-\".concat(message.id)) ? \" ▼\" : \" ▶\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1102,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        message.productRef && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-2 p-3 bg-background rounded border border-border\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-16 h-16 bg-muted rounded overflow-hidden flex items-center justify-center mr-3\",\n                                                                        children: ((_localProducts_find = localProducts.find((p)=>p.id === message.productRef)) === null || _localProducts_find === void 0 ? void 0 : _localProducts_find.image) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"relative w-full h-full\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                                                                src: ((_localProducts_find1 = localProducts.find((p)=>p.id === message.productRef)) === null || _localProducts_find1 === void 0 ? void 0 : _localProducts_find1.image) || \"/images/placeholder.png\",\n                                                                                alt: ((_localProducts_find2 = localProducts.find((p)=>p.id === message.productRef)) === null || _localProducts_find2 === void 0 ? void 0 : _localProducts_find2.name) || \"Product\",\n                                                                                fill: true,\n                                                                                className: \"object-cover\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 1125,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1124,\n                                                                            columnNumber: 31\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"h-6 w-6 text-muted-foreground\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1141,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1120,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-start mb-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"font-medium text-sm\",\n                                                                                        children: (_localProducts_find3 = localProducts.find((p)=>p.id === message.productRef)) === null || _localProducts_find3 === void 0 ? void 0 : _localProducts_find3.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                        lineNumber: 1146,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center gap-1\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                            variant: \"ghost\",\n                                                                                            size: \"icon\",\n                                                                                            className: \"h-6 w-6\",\n                                                                                            onClick: ()=>setShowEmojiPicker({\n                                                                                                    productId: message.productRef\n                                                                                                }),\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                                className: \"h-3 w-3\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                                lineNumber: 1164,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                            lineNumber: 1154,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                        lineNumber: 1153,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 1145,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-muted-foreground text-sm mb-2\",\n                                                                                children: [\n                                                                                    \"$\",\n                                                                                    ((_localProducts_find4 = localProducts.find((p)=>p.id === message.productRef)) === null || _localProducts_find4 === void 0 ? void 0 : _localProducts_find4.price) || \"Price unavailable\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                lineNumber: 1168,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            (()=>{\n                                                                                const product = localProducts.find((p)=>p.id === message.productRef);\n                                                                                return (product === null || product === void 0 ? void 0 : product.reactions) && product.reactions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex flex-wrap gap-1 mb-2\",\n                                                                                    children: Object.entries(product.reactions.reduce((acc, reaction)=>{\n                                                                                        if (!acc[reaction.emoji]) {\n                                                                                            acc[reaction.emoji] = [];\n                                                                                        }\n                                                                                        acc[reaction.emoji].push(reaction);\n                                                                                        return acc;\n                                                                                    }, {})).map((param)=>{\n                                                                                        let [emoji, reactions] = param;\n                                                                                        const userReaction = reactions.find((r)=>r.userId === currentUserId);\n                                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                            variant: \"outline\",\n                                                                                            size: \"sm\",\n                                                                                            className: \"h-6 px-2 text-xs relative group \".concat(userReaction ? \"bg-blue-50 border-blue-200\" : \"\"),\n                                                                                            title: \"\".concat(reactions.map((r)=>r.userName).join(\", \"), \" reacted with \").concat(emoji),\n                                                                                            onClick: ()=>{\n                                                                                                if (userReaction) {\n                                                                                                    handleDeleteReaction(userReaction.id, undefined, product.id);\n                                                                                                }\n                                                                                            },\n                                                                                            children: [\n                                                                                                emoji,\n                                                                                                \" \",\n                                                                                                reactions.length,\n                                                                                                userReaction && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-3 h-3 text-xs flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                                                                    children: \"\\xd7\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                                    lineNumber: 1228,\n                                                                                                    columnNumber: 45\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, emoji, true, {\n                                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                            lineNumber: 1204,\n                                                                                            columnNumber: 41\n                                                                                        }, this);\n                                                                                    })\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 1183,\n                                                                                    columnNumber: 35\n                                                                                }, this);\n                                                                            })(),\n                                                                            (()=>{\n                                                                                const product = localProducts.find((p)=>p.id === message.productRef);\n                                                                                return (product === null || product === void 0 ? void 0 : product.consensusScore) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center gap-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex-1 bg-gray-200 rounded-full h-2\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"h-2 rounded-full \".concat(product.consensusScore >= 80 ? \"bg-green-500\" : product.consensusScore >= 60 ? \"bg-yellow-500\" : \"bg-red-500\"),\n                                                                                                style: {\n                                                                                                    width: \"\".concat(product.consensusScore, \"%\")\n                                                                                                }\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                                lineNumber: 1249,\n                                                                                                columnNumber: 39\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                            lineNumber: 1248,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-xs font-medium \".concat(getConsensusColor(product.consensusScore)),\n                                                                                            children: [\n                                                                                                product.consensusScore,\n                                                                                                \"% consensus\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                            lineNumber: 1262,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 1247,\n                                                                                    columnNumber: 35\n                                                                                }, this);\n                                                                            })()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1144,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1119,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1118,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        message.attachment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full max-w-[200px] h-[150px] bg-muted rounded-md overflow-hidden relative\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 flex items-center justify-center text-muted-foreground\",\n                                                                    children: \"Image Attachment\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1281,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1280,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1279,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 989,\n                                                    columnNumber: 19\n                                                }, this),\n                                                expandedThreads.has(\"thread-\".concat(message.id)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-4 pl-4 border-l-2 border-gray-200 space-y-2\",\n                                                    children: getThreadMessages(message.id).map((threadMessage)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gray-50 rounded-lg p-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-start mb-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-sm\",\n                                                                            children: threadMessage.user\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1298,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-muted-foreground\",\n                                                                                    children: threadMessage.timestamp\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 1302,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                threadMessage.userId === currentUserId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                    variant: \"ghost\",\n                                                                                    size: \"icon\",\n                                                                                    className: \"h-4 w-4 text-red-500 hover:text-red-700\",\n                                                                                    onClick: ()=>handleDeleteMessage(threadMessage.id),\n                                                                                    title: \"Delete reply\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                        className: \"h-2 w-2\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                        lineNumber: 1315,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                                    lineNumber: 1306,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1301,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1297,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm\",\n                                                                    dangerouslySetInnerHTML: {\n                                                                        __html: renderDetectedProducts(threadMessage.content, threadMessage.detectedProducts)\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1320,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, threadMessage.id, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1293,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1291,\n                                                    columnNumber: 21\n                                                }, this),\n                                                replyingTo === message.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-2 p-2 bg-gray-50 rounded border\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-muted-foreground mb-2\",\n                                                            children: [\n                                                                \"Replying to \",\n                                                                message.user\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1337,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    placeholder: \"Type your reply...\",\n                                                                    className: \"flex-1 h-8 rounded-l border border-input bg-background px-2 text-sm\",\n                                                                    value: replyText,\n                                                                    onChange: (e)=>setReplyText(e.target.value),\n                                                                    onKeyDown: (e)=>{\n                                                                        if (e.key === \"Enter\" && !e.shiftKey) {\n                                                                            e.preventDefault();\n                                                                            handleSendReply(message.id);\n                                                                        }\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1341,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>handleSendReply(message.id),\n                                                                    disabled: !replyText.trim(),\n                                                                    className: \"rounded-l-none\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1360,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1354,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    size: \"sm\",\n                                                                    variant: \"outline\",\n                                                                    onClick: ()=>{\n                                                                        setReplyingTo(null);\n                                                                        setReplyText(\"\");\n                                                                    },\n                                                                    children: \"Cancel\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1362,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1340,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1336,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, message.id, true, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 987,\n                                            columnNumber: 17\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 985,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 984,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                value: \"payment\",\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                className: \"pb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                    className: \"text-lg\",\n                                                    children: \"Payment Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1390,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1389,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full bg-secondary rounded-full h-3 mb-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-primary h-3 rounded-full\",\n                                                            style: {\n                                                                width: \"\".concat(groupData.amountPaid / groupData.totalAmount * 100, \"%\")\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1394,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1393,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"$\",\n                                                                    groupData.amountPaid,\n                                                                    \" raised\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1404,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"$\",\n                                                                    groupData.totalAmount,\n                                                                    \" goal\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1405,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1403,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-muted-foreground text-center\",\n                                                        children: [\n                                                            \"$\",\n                                                            groupData.totalAmount - groupData.amountPaid,\n                                                            \" remaining\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1407,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1392,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardFooter, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                                    href: \"/groups/\".concat(params.id, \"/payment\"),\n                                                    className: \"w-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        className: \"w-full\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1414,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Make a Payment\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1413,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1412,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1411,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1388,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium mb-2\",\n                                        children: \"Payment History\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1421,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: groupData.members.filter((member)=>member.amountPaid > 0).map((member)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-muted p-3 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-8 h-8 rounded-full bg-secondary flex items-center justify-center\",\n                                                                    children: member.name.charAt(0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1429,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: member.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1433,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-muted-foreground\",\n                                                                            children: new Date().toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1434,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1432,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1428,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: [\n                                                                \"$\",\n                                                                member.amountPaid\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1439,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1427,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, member.id, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1426,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1422,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 1387,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                value: \"manufacturing\",\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Manufacturing Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1449,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-primary font-medium capitalize\",\n                                                        children: groupData.status\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1450,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1448,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-secondary rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-primary h-2 rounded-full\",\n                                                    style: {\n                                                        width: \"\".concat(groupData.manufacturingProgress, \"%\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1455,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1454,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mt-1 text-xs text-muted-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Production Started\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1461,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Ready for Shipping\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1462,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1460,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1447,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium\",\n                                                children: \"Latest Updates\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1467,\n                                                columnNumber: 15\n                                            }, this),\n                                            groupData.manufacturingUpdates.map((update)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                    className: \"overflow-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium\",\n                                                                        children: update.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1472,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: update.date\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1473,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1471,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm mb-2\",\n                                                                children: update.description\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1477,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-24 bg-muted flex items-center justify-center rounded\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-muted-foreground\",\n                                                                    children: \"Update Image\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1479,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1478,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1470,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, update.id, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1469,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1466,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 1446,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                value: \"members\",\n                                className: \"mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: localMembers.map((member)=>{\n                                        const currentUser = localMembers.find((m)=>m.id === currentUserId);\n                                        const isCurrentUserAdmin = currentUser === null || currentUser === void 0 ? void 0 : currentUser.isAdmin;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center p-3 bg-muted rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 rounded-full bg-secondary flex items-center justify-center\",\n                                                    children: member.name.charAt(0)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1502,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-3 flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2 mb-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: member.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1507,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                member.isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 bg-primary/10 text-primary text-xs rounded-full\",\n                                                                    children: \"Admin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1509,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 text-xs rounded-full border \".concat(getStatusStyling(member.status)),\n                                                                    children: member.status.charAt(0).toUpperCase() + member.status.slice(1)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1513,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1506,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-muted-foreground\",\n                                                            children: member.isAdmin ? \"Group Administrator\" : \"Member\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1522,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        isCurrentUserAdmin && !member.isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-1 mt-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"h-6 px-2 text-xs\",\n                                                                    onClick: ()=>handleUpdateMemberStatus(member.id, \"active\"),\n                                                                    disabled: member.status === \"active\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            className: \"h-3 w-3 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1538,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \"Active\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1529,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"h-6 px-2 text-xs\",\n                                                                    onClick: ()=>handleUpdateMemberStatus(member.id, \"inactive\"),\n                                                                    disabled: member.status === \"inactive\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"h-3 w-3 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1550,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \"Inactive\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1541,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"h-6 px-2 text-xs\",\n                                                                    onClick: ()=>handleUpdateMemberStatus(member.id, \"pending\"),\n                                                                    disabled: member.status === \"pending\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"h-3 w-3 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1562,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \"Pending\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1553,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"h-6 px-2 text-xs text-red-600 hover:text-red-700\",\n                                                                    onClick: ()=>handleUpdateMemberStatus(member.id, \"removed\"),\n                                                                    disabled: member.status === \"removed\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            className: \"h-3 w-3 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1574,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \"Remove\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                    lineNumber: 1565,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                            lineNumber: 1528,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1505,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, member.id, true, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1498,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1490,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 1489,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                        lineNumber: 912,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 907,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                open: showProductSelector,\n                onOpenChange: setShowProductSelector,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                    className: \"sm:max-w-[425px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTitle, {\n                                    children: \"Select a Product\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1592,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogDescription, {\n                                    children: \"Choose a product to reference in your message\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1593,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1591,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3 mt-4 max-h-[300px] overflow-y-auto\",\n                            children: groupData.suggestedProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border rounded-md cursor-pointer hover:bg-muted flex items-center\",\n                                    onClick: ()=>handleSelectProduct(product.id),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-muted rounded overflow-hidden mr-3 flex items-center justify-center\",\n                                            children: product.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative w-full h-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                                    src: product.image || \"/images/placeholder.png\",\n                                                    alt: product.name,\n                                                    fill: true,\n                                                    className: \"object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1607,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1606,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-6 w-6 text-muted-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1615,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1604,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium\",\n                                                    children: product.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1619,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: product.price > 0 ? \"$\".concat(product.price) : \"Price unavailable\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1620,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1618,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, product.id, true, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1599,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1597,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 1590,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 1589,\n                columnNumber: 7\n            }, this),\n            activeTab === \"discussion\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-20 left-4 right-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        selectedProductRef && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-2 bg-white rounded-lg border border-border shadow-lg p-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-muted rounded overflow-hidden flex items-center justify-center mr-2\",\n                                        children: ((_localProducts_find = localProducts.find((p)=>p.id === selectedProductRef)) === null || _localProducts_find === void 0 ? void 0 : _localProducts_find.image) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative w-full h-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                                src: ((_localProducts_find1 = localProducts.find((p)=>p.id === selectedProductRef)) === null || _localProducts_find1 === void 0 ? void 0 : _localProducts_find1.image) || \"/images/placeholder.png\",\n                                                alt: ((_localProducts_find2 = localProducts.find((p)=>p.id === selectedProductRef)) === null || _localProducts_find2 === void 0 ? void 0 : _localProducts_find2.name) || \"Product\",\n                                                fill: true,\n                                                className: \"object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1644,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1643,\n                                            columnNumber: 23\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1660,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1640,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 text-xs\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium\",\n                                                children: (_localProducts_find3 = localProducts.find((p)=>p.id === selectedProductRef)) === null || _localProducts_find3 === void 0 ? void 0 : _localProducts_find3.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1664,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-muted-foreground\",\n                                                children: [\n                                                    \"$\",\n                                                    ((_localProducts_find4 = localProducts.find((p)=>p.id === selectedProductRef)) === null || _localProducts_find4 === void 0 ? void 0 : _localProducts_find4.price) || \"Price unavailable\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1670,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1663,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        className: \"h-6 w-6 rounded-full\",\n                                        type: \"button\",\n                                        onClick: ()=>setSelectedProductRef(null),\n                                        children: \"\\xd7\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1676,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                lineNumber: 1639,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1638,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"icon\",\n                                            className: \"rounded-full h-10 w-10 bg-white shadow-lg border\",\n                                            type: \"button\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"file-upload\",\n                                                className: \"cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        id: \"file-upload\",\n                                                        type: \"file\",\n                                                        accept: \"image/*\",\n                                                        className: \"sr-only\",\n                                                        \"aria-label\": \"Upload image\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1700,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1707,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1699,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1693,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"icon\",\n                                            className: \"rounded-full h-10 w-10 bg-white shadow-lg border\",\n                                            type: \"button\",\n                                            onClick: ()=>setShowProductSelector(true),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1717,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1710,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1692,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Type your message...\",\n                                            className: \"w-full h-10 rounded-full border border-input bg-white px-4 pr-12 text-sm shadow-lg ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n                                            value: messageText,\n                                            onChange: (e)=>setMessageText(e.target.value),\n                                            onKeyDown: (e)=>{\n                                                if (e.key === \"Enter\" && !e.shiftKey) {\n                                                    e.preventDefault();\n                                                    handleSendMessage();\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1723,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            size: \"icon\",\n                                            className: \"absolute right-1 top-1 h-8 w-8 rounded-full\",\n                                            onClick: handleSendMessage,\n                                            disabled: !messageText.trim(),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1742,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1736,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1722,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>setShowProductsOverview(true),\n                                    className: \"rounded-full shadow-lg h-10 px-4 bg-white border text-foreground hover:bg-accent\",\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                            lineNumber: 1753,\n                                            columnNumber: 17\n                                        }, this),\n                                        localProducts.length\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1747,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1690,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 1635,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 1634,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                open: showProductsOverview,\n                onOpenChange: setShowProductsOverview,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                    className: \"sm:max-w-[500px] max-h-[80vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTitle, {\n                                    children: \"Product Suggestions\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1768,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogDescription, {\n                                    children: [\n                                        localProducts.length,\n                                        \" products suggested by the group\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1769,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1767,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 gap-4 mt-4\",\n                            children: localProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    className: \"overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-20 h-20 bg-muted flex items-center justify-center\",\n                                                children: product.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative w-full h-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                                        src: product.image || \"/images/placeholder.png\",\n                                                        alt: product.name,\n                                                        fill: true,\n                                                        className: \"object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1780,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1779,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-6 w-6 text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                    lineNumber: 1788,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1777,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-sm\",\n                                                                children: product.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1793,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs px-1.5 py-0.5 rounded \".concat(product.source === \"internal\" ? \"bg-primary/10 text-primary\" : \"bg-secondary text-secondary-foreground\"),\n                                                                        children: product.source === \"internal\" ? \"Catalog\" : \"External\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1795,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-6 w-6\",\n                                                                        onClick: ()=>setShowEmojiPicker({\n                                                                                productId: product.id\n                                                                            }),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1814,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1806,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1794,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1792,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground mb-2\",\n                                                        children: product.description\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1818,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    product.reactions && product.reactions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1 mb-2\",\n                                                        children: Object.entries(product.reactions.reduce((acc, reaction)=>{\n                                                            if (!acc[reaction.emoji]) {\n                                                                acc[reaction.emoji] = [];\n                                                            }\n                                                            acc[reaction.emoji].push(reaction);\n                                                            return acc;\n                                                        }, {})).map((param)=>{\n                                                            let [emoji, reactions] = param;\n                                                            const userReaction = reactions.find((r)=>r.userId === currentUserId);\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                className: \"h-5 px-1.5 text-xs relative group \".concat(userReaction ? \"bg-blue-50 border-blue-200\" : \"\"),\n                                                                title: \"\".concat(reactions.map((r)=>r.userName).join(\", \"), \" reacted with \").concat(emoji),\n                                                                onClick: ()=>{\n                                                                    if (userReaction) {\n                                                                        handleDeleteReaction(userReaction.id, undefined, product.id);\n                                                                    }\n                                                                },\n                                                                children: [\n                                                                    emoji,\n                                                                    \" \",\n                                                                    reactions.length,\n                                                                    userReaction && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-2 h-2 text-xs flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                                        children: \"\\xd7\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1861,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                ]\n                                                            }, emoji, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1839,\n                                                                columnNumber: 29\n                                                            }, this);\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1824,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    product.consensusScore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1 bg-gray-200 rounded-full h-1.5\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-1.5 rounded-full \".concat(product.consensusScore >= 80 ? \"bg-green-500\" : product.consensusScore >= 60 ? \"bg-yellow-500\" : \"bg-red-500\"),\n                                                                            style: {\n                                                                                width: \"\".concat(product.consensusScore, \"%\")\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                            lineNumber: 1876,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1875,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs font-medium \".concat(getConsensusColor(product.consensusScore)),\n                                                                        children: [\n                                                                            product.consensusScore,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1887,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1874,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: [\n                                                                    \"Group consensus • \",\n                                                                    product.threadCount || 0,\n                                                                    \" \",\n                                                                    \"discussions\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1895,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1873,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm\",\n                                                                children: product.price > 0 ? \"$\".concat(product.price) : \"Price unavailable\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1903,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>{\n                                                                    setShowProductsOverview(false);\n                                                                // In a real app, this would scroll to the product in discussion\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Clock_CreditCard_MessageCircle_MessageSquare_Reply_Send_ShoppingBag_Smile_Trash2_TrendingUp_Upload_UserMinus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                        lineNumber: 1916,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Discuss\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                                lineNumber: 1908,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                        lineNumber: 1902,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                                lineNumber: 1791,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                        lineNumber: 1776,\n                                        columnNumber: 17\n                                    }, this)\n                                }, product.id, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1775,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1773,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 1766,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 1762,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                open: !!showEmojiPicker,\n                onOpenChange: ()=>setShowEmojiPicker(null),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                    className: \"sm:max-w-[300px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTitle, {\n                                    children: \"Add Reaction\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1935,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogDescription, {\n                                    children: \"Choose an emoji to react with\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1936,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1934,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-6 gap-2 mt-4\",\n                            children: [\n                                \"\\uD83D\\uDC4D\",\n                                \"\\uD83D\\uDC4E\",\n                                \"❤️\",\n                                \"\\uD83D\\uDE0D\",\n                                \"\\uD83E\\uDD14\",\n                                \"\\uD83D\\uDE15\",\n                                \"\\uD83D\\uDCB0\",\n                                \"\\uD83D\\uDCB8\",\n                                \"\\uD83C\\uDF89\",\n                                \"❌\",\n                                \"\\uD83D\\uDC40\",\n                                \"\\uD83D\\uDCF8\",\n                                \"\\uD83D\\uDC4B\",\n                                \"\\uD83D\\uDD25\",\n                                \"\\uD83D\\uDCA1\",\n                                \"✨\",\n                                \"⚠️\",\n                                \"\\uD83D\\uDEAB\"\n                            ].map((emoji)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    className: \"h-12 w-12 text-2xl hover:bg-accent\",\n                                    onClick: ()=>handleAddReaction(emoji, showEmojiPicker === null || showEmojiPicker === void 0 ? void 0 : showEmojiPicker.messageId, showEmojiPicker === null || showEmojiPicker === void 0 ? void 0 : showEmojiPicker.productId),\n                                    children: emoji\n                                }, emoji, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                                    lineNumber: 1959,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                            lineNumber: 1938,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                    lineNumber: 1933,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n                lineNumber: 1929,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Code/khenesis/app/groups/[id]/page.tsx\",\n        lineNumber: 878,\n        columnNumber: 5\n    }, this);\n}\n_s(GroupDetail, \"1xquhGfE4PnBTgHgiOrU38Naf2U=\");\n_c = GroupDetail;\nvar _c;\n$RefreshReg$(_c, \"GroupDetail\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/groups/[id]/page.tsx\n"));

/***/ })

});