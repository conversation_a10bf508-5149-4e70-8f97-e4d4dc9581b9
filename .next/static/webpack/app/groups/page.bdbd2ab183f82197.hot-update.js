"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/groups/page",{

/***/ "(app-pages-browser)/./app/groups/page.tsx":
/*!*****************************!*\
  !*** ./app/groups/page.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GroupsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layouts_mobile_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layouts/mobile-layout */ \"(app-pages-browser)/./components/layouts/mobile-layout.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Plus_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Plus,Settings,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Plus_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Plus,Settings,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Plus_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Plus,Settings,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Plus_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Plus,Settings,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Plus_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Plus,Settings,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Plus_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Plus,Settings,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_6__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Mock data for groups\nconst mockGroups = [\n    {\n        id: 1,\n        name: \"Living Room Remodel Group\",\n        description: \"A collective purchase for premium living room furniture at wholesale prices.\",\n        memberCount: 5,\n        createdDate: \"2023-03-05\",\n        isActive: true,\n        stage: \"manufacturing\",\n        currentUserRole: \"admin\"\n    },\n    {\n        id: 2,\n        name: \"Kitchen Appliances Bulk Buy\",\n        description: \"Group purchase for high-end kitchen appliances with significant discounts.\",\n        memberCount: 8,\n        createdDate: \"2023-03-12\",\n        isActive: true,\n        stage: \"payment\",\n        currentUserRole: \"member\"\n    },\n    {\n        id: 3,\n        name: \"Home Office Setup\",\n        description: \"Collaborative buying for ergonomic office furniture and equipment.\",\n        memberCount: 3,\n        createdDate: \"2023-02-28\",\n        isActive: false,\n        stage: \"discussion\",\n        currentUserRole: \"admin\"\n    },\n    {\n        id: 4,\n        name: \"Garden Furniture Collection\",\n        description: \"Seasonal purchase of outdoor furniture for spring and summer.\",\n        memberCount: 12,\n        createdDate: \"2023-03-18\",\n        isActive: true,\n        stage: \"suggestion\",\n        currentUserRole: \"member\"\n    }\n];\nfunction GroupsPage() {\n    _s();\n    const [localGroups, setLocalGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(mockGroups);\n    // Helper function to get stage styling\n    const getStageStyling = (stage)=>{\n        switch(stage){\n            case \"suggestion\":\n                return \"bg-blue-500/20 text-blue-700\";\n            case \"discussion\":\n                return \"bg-purple-500/20 text-purple-700\";\n            case \"payment\":\n                return \"bg-orange-500/20 text-orange-700\";\n            case \"manufacturing\":\n                return \"bg-green-500/20 text-green-700\";\n            case \"shipping\":\n                return \"bg-teal-500/20 text-teal-700\";\n            default:\n                return \"bg-gray-500/20 text-gray-700\";\n        }\n    };\n    // Admin function to toggle group status\n    const handleToggleGroupStatus = (groupId)=>{\n        setLocalGroups((prev)=>prev.map((group)=>group.id === groupId && group.currentUserRole === \"admin\" ? {\n                    ...group,\n                    isActive: !group.isActive\n                } : group));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layouts_mobile_layout__WEBPACK_IMPORTED_MODULE_2__.MobileLayout, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container py-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold\",\n                            children: \"Groups\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                            href: \"/groups/new\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                size: \"sm\",\n                                className: \"gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Plus_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"New Group\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: localGroups.map((group)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            className: \"overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"pb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                        className: \"text-lg\",\n                                                        children: group.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    group.currentUserRole === \"admin\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"text-xs\",\n                                                        children: \"Admin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground leading-relaxed\",\n                                                children: group.description\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap items-center gap-4 text-sm text-muted-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Plus_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n                                                                lineNumber: 154,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    group.memberCount,\n                                                                    \" members\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n                                                                lineNumber: 155,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Plus_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n                                                                lineNumber: 158,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"Created\",\n                                                                    \" \",\n                                                                    new Date(group.createdDate).toLocaleDateString(\"en-US\", {\n                                                                        year: \"numeric\",\n                                                                        month: \"short\",\n                                                                        day: \"numeric\"\n                                                                    })\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n                                                                lineNumber: 159,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        variant: \"outline\",\n                                                        className: getStageStyling(group.stage),\n                                                        children: group.stage.charAt(0).toUpperCase() + group.stage.slice(1)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1\",\n                                                        children: [\n                                                            group.isActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Plus_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-4 w-4 text-green-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n                                                                lineNumber: 185,\n                                                                columnNumber: 25\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Plus_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-4 w-4 text-gray-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n                                                                lineNumber: 187,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs \".concat(group.isActive ? \"text-green-600\" : \"text-gray-500\"),\n                                                                children: group.isActive ? \"Active\" : \"Inactive\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n                                                                lineNumber: 189,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardFooter, {\n                                    className: \"pt-0 flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                            href: \"/groups/\".concat(group.id),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                children: \"View Group\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 17\n                                        }, this),\n                                        group.currentUserRole === \"admin\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: ()=>handleToggleGroupStatus(group.id),\n                                            className: \"gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Plus_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 21\n                                                }, this),\n                                                group.isActive ? \"Deactivate\" : \"Activate\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, group.id, true, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 9\n                }, this),\n                localGroups.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Plus_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-12 w-12 text-muted-foreground mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium mb-2\",\n                            children: \"No groups yet\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground mb-4\",\n                            children: \"Create your first group to start collaborative purchasing.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                            href: \"/groups/new\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Plus_Settings_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Create Group\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n            lineNumber: 120,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Code/khenesis/app/groups/page.tsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, this);\n}\n_s(GroupsPage, \"7hRIb+L7Y7OYtRfSACBhCPXCRWI=\");\n_c = GroupsPage;\nvar _c;\n$RefreshReg$(_c, \"GroupsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/groups/page.tsx\n"));

/***/ })

});