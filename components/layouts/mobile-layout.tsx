"use client";

import { ArrowLeft, Home, Search, ShoppingBag, User } from "lucide-react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { ReactNode } from "react";

interface MobileLayoutProps {
  children: ReactNode;
  showBackButton?: boolean;
  showNav?: boolean;
}

export function MobileLayout({
  children,
  showBackButton,
  showNav = true,
}: MobileLayoutProps) {
  const pathname = usePathname();
  const router = useRouter();

  return (
    <div className="flex min-h-screen flex-col">
      <header className="sticky top-0 z-10 border-b bg-background p-4">
        <div className="flex items-center justify-between">
          {(pathname !== "/" || showBackButton) && (
            <button
              onClick={() => router.back()}
              className="rounded-full p-2 hover:bg-muted"
              aria-label="Go back"
            >
              <ArrowLeft className="h-5 w-5" />
            </button>
          )}
          <h1 className="text-lg font-medium">Khenesis</h1>
          <button
            className="rounded-full p-2 hover:bg-muted"
            aria-label="Search"
          >
            <Search className="h-5 w-5" />
          </button>
        </div>
      </header>

      <main className="flex-1 pb-16">{children}</main>

      {showNav && (
        <nav className="fixed bottom-0 z-10 w-full border-t bg-background">
          <div className="grid h-16 grid-cols-5">
            <Link
              href="/"
              className={`flex flex-col items-center justify-center ${
                pathname === "/" ? "text-primary" : "text-muted-foreground"
              }`}
            >
              <Home className="h-5 w-5" />
              <span className="text-xs">Home</span>
            </Link>
            <Link
              href="/products"
              className={`flex flex-col items-center justify-center ${
                pathname === "/products" || pathname.startsWith("/products/")
                  ? "text-primary"
                  : "text-muted-foreground"
              }`}
            >
              <ShoppingBag className="h-5 w-5" />
              <span className="text-xs">Products</span>
            </Link>
            <Link
              href="/orders"
              className={`flex flex-col items-center justify-center ${
                pathname === "/orders" || pathname.startsWith("/orders/")
                  ? "text-primary"
                  : "text-muted-foreground"
              }`}
            >
              <svg
                className="h-5 w-5"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                <line x1="8" y1="21" x2="16" y2="21"></line>
                <line x1="12" y1="17" x2="12" y2="21"></line>
              </svg>
              <span className="text-xs">Orders</span>
            </Link>
            <Link
              href="/groups"
              className={`flex flex-col items-center justify-center ${
                pathname === "/groups" || pathname.startsWith("/groups/")
                  ? "text-primary"
                  : "text-muted-foreground"
              }`}
            >
              <svg
                className="h-5 w-5"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <circle cx="9" cy="7" r="4" />
                <path d="M3 21v-2a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v2" />
                <circle cx="19" cy="11" r="2" />
                <path d="M19 22v-3a2 2 0 0 0-2-2h-1.5" />
              </svg>
              <span className="text-xs">Groups</span>
            </Link>
            <Link
              href="/account"
              className={`flex flex-col items-center justify-center ${
                pathname === "/account" || pathname.startsWith("/account/")
                  ? "text-primary"
                  : "text-muted-foreground"
              }`}
            >
              <User className="h-5 w-5" />
              <span className="text-xs">Account</span>
            </Link>
          </div>
        </nav>
      )}
    </div>
  );
}
