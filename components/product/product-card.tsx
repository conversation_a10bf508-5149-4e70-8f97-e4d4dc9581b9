"use client";

import { <PERSON><PERSON>ron<PERSON>ef<PERSON>, ChevronR<PERSON>, Heart, Star } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";

import { Button } from "@/components/ui/button";
import { Product } from "@/data/products";
import { cn, formatPrice } from "@/lib/utils";

interface ProductCardProps {
  product: Product;
  viewMode?: "grid" | "list";
}

export function ProductCard({ product, viewMode = "grid" }: ProductCardProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isHovered, setIsHovered] = useState(false);

  const nextImage = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setCurrentImageIndex((prev) =>
      prev === product.images.length - 1 ? 0 : prev + 1
    );
  };

  const prevImage = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setCurrentImageIndex((prev) =>
      prev === 0 ? product.images.length - 1 : prev - 1
    );
  };

  if (viewMode === "list") {
    return (
      <Link
        href={`/products/${product.id}`}
        className="group flex overflow-hidden rounded-lg border bg-card transition-colors hover:bg-accent/50"
      >
        <div className="relative aspect-square h-28 w-28 flex-shrink-0 overflow-hidden sm:h-36 sm:w-36">
          <Image
            src={product.images[currentImageIndex] || "/images/placeholder.png"}
            alt={product.name}
            fill
            sizes="(max-width: 768px) 100px, 150px"
            className="object-cover"
          />
          {product.images.length > 1 && isHovered && (
            <>
              <button
                onClick={prevImage}
                className="absolute left-1 top-1/2 flex h-6 w-6 -translate-y-1/2 items-center justify-center rounded-full bg-background/80 text-foreground backdrop-blur-sm"
                aria-label="Previous image"
              >
                <ChevronLeft className="h-4 w-4" />
              </button>
              <button
                onClick={nextImage}
                className="absolute right-1 top-1/2 flex h-6 w-6 -translate-y-1/2 items-center justify-center rounded-full bg-background/80 text-foreground backdrop-blur-sm"
                aria-label="Next image"
              >
                <ChevronRight className="h-4 w-4" />
              </button>
            </>
          )}
        </div>

        <div className="flex flex-1 flex-col justify-between p-3">
          <div>
            <div className="flex items-start justify-between">
              <h3 className="font-medium">{product.name}</h3>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 text-muted-foreground"
                aria-label="Add to wishlist"
              >
                <Heart className="h-4 w-4" />
              </Button>
            </div>
            <p className="mt-1 line-clamp-2 text-sm text-muted-foreground">
              {product.description}
            </p>
            <div className="mt-2 flex items-center text-sm">
              <Star className="mr-1 h-4 w-4 fill-primary text-primary" />
              <span>{product.rating}</span>
              <span className="mx-1 text-muted-foreground">•</span>
              <span className="text-muted-foreground">
                {product.reviews} reviews
              </span>
            </div>
          </div>

          <div className="mt-3 flex items-center justify-between">
            <div className="text-lg font-medium">
              {formatPrice(product.price)}
            </div>
            <div className="text-sm text-muted-foreground">
              {product.manufacturingTime} days to manufacture
            </div>
          </div>
        </div>
      </Link>
    );
  }

  return (
    <Link
      href={`/products/${product.id}`}
      className="group overflow-hidden rounded-lg border bg-card transition-colors hover:bg-accent/50"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="relative aspect-square overflow-hidden">
        <Image
          src={product.images[currentImageIndex] || "/images/placeholder.png"}
          alt={product.name}
          fill
          sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
          className="object-cover transition-transform group-hover:scale-105"
        />
        {product.images.length > 1 && isHovered && (
          <>
            <button
              onClick={prevImage}
              className="absolute left-2 top-1/2 flex h-7 w-7 -translate-y-1/2 items-center justify-center rounded-full bg-background/80 text-foreground backdrop-blur-sm"
              aria-label="Previous image"
            >
              <ChevronLeft className="h-4 w-4" />
            </button>
            <button
              onClick={nextImage}
              className="absolute right-2 top-1/2 flex h-7 w-7 -translate-y-1/2 items-center justify-center rounded-full bg-background/80 text-foreground backdrop-blur-sm"
              aria-label="Next image"
            >
              <ChevronRight className="h-4 w-4" />
            </button>
          </>
        )}
        <Button
          variant="ghost"
          size="icon"
          className="absolute right-2 top-2 h-8 w-8 rounded-full bg-background/80 text-muted-foreground backdrop-blur-sm"
          aria-label="Add to wishlist"
        >
          <Heart className="h-4 w-4" />
        </Button>
        <div
          className={cn(
            "absolute bottom-0 left-0 flex h-7 items-center gap-1 bg-background/80 px-2 text-xs backdrop-blur-sm",
            product.manufacturingTime > 0
              ? "bg-amber-500/80 text-amber-950"
              : "bg-green-500/80 text-green-950"
          )}
        >
          {product.manufacturingTime > 0
            ? `${product.manufacturingTime} days to make`
            : "In stock"}
        </div>
      </div>

      <div className="p-3">
        <div className="mb-1 flex items-center text-xs">
          <Star className="mr-1 h-3 w-3 fill-primary text-primary" />
          <span>{product.rating}</span>
          <span className="mx-1 text-muted-foreground">•</span>
          <span className="text-muted-foreground">
            {product.reviews} reviews
          </span>
        </div>
        <h3 className="line-clamp-1 font-medium">{product.name}</h3>
        <div className="mt-1 flex items-center justify-between">
          <div className="font-medium">{formatPrice(product.price)}</div>
          <div className="text-xs text-muted-foreground">
            {product.merchant.name}
          </div>
        </div>
      </div>
    </Link>
  );
}
