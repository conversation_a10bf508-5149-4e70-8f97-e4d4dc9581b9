{"version": 3, "sources": ["../../../src/app-beta/control-components.tsx"], "sourcesContent": ["import { deprecated } from '@clerk/shared/deprecated';\nimport React from 'react';\n\ndeprecated(\n  '@clerk/nextjs/app-beta',\n  'Use imports from `@clerk/nextjs` instead.\\nFor more details, consult the middleware documentation: https://clerk.com/docs/nextjs/middleware',\n);\nimport { auth } from './auth';\n\n/**\n * @deprecated Use imports from `@clerk/nextjs` instead.\n * For more details, consult the middleware documentation: https://clerk.com/docs/nextjs/middleware\n */\nexport function SignedIn(props: React.PropsWithChildren) {\n  const { children } = props;\n  const { userId } = auth();\n  return userId ? <>{children}</> : null;\n}\n\n/**\n * @deprecated Use imports from `@clerk/nextjs` instead.\n * For more details, consult the middleware documentation: https://clerk.com/docs/nextjs/middleware\n */\nexport function SignedOut(props: React.PropsWithChildren) {\n  const { children } = props;\n  const { userId } = auth();\n  return userId ? null : <>{children}</>;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBAA2B;AAC3B,mBAAkB;AAMlB,kBAAqB;AAAA,IAJrB;AAAA,EACE;AAAA,EACA;AACF;AAOO,SAAS,SAAS,OAAgC;AACvD,QAAM,EAAE,SAAS,IAAI;AACrB,QAAM,EAAE,OAAO,QAAI,kBAAK;AACxB,SAAO,SAAS,6BAAAA,QAAA,2BAAAA,QAAA,gBAAG,QAAS,IAAM;AACpC;AAMO,SAAS,UAAU,OAAgC;AACxD,QAAM,EAAE,SAAS,IAAI;AACrB,QAAM,EAAE,OAAO,QAAI,kBAAK;AACxB,SAAO,SAAS,OAAO,6BAAAA,QAAA,2BAAAA,QAAA,gBAAG,QAAS;AACrC;", "names": ["React"]}