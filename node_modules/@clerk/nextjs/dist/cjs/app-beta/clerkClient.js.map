{"version": 3, "sources": ["../../../src/app-beta/clerkClient.ts"], "sourcesContent": ["import { deprecated } from '@clerk/shared/deprecated';\n\ndeprecated(\n  '@clerk/nextjs/app-beta',\n  'Use imports from `@clerk/nextjs` instead.\\nFor more details, consult the middleware documentation: https://clerk.com/docs/nextjs/middleware',\n);\nimport { clerkClient as _clerkClient } from '../server/clerkClient';\n\n/**\n * @deprecated Use imports from `@clerk/nextjs` instead.\n * For more details, consult the middleware documentation: https://clerk.com/docs/nextjs/middleware\n */\nexport const clerkClient = _clerkClient;\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBAA2B;AAM3B,yBAA4C;AAAA,IAJ5C;AAAA,EACE;AAAA,EACA;AACF;AAOO,MAAM,cAAc,mBAAAA;", "names": ["_clerkClient"]}