{"version": 3, "sources": ["../../../src/app-beta/currentUser.ts"], "sourcesContent": ["import type { User } from '@clerk/backend';\nimport { deprecated } from '@clerk/shared/deprecated';\n\ndeprecated(\n  '@clerk/nextjs/app-beta',\n  'Use imports from `@clerk/nextjs` instead.\\nFor more details, consult the middleware documentation: https://clerk.com/docs/nextjs/middleware',\n);\nimport { auth } from './auth';\nimport { clerkClient } from './clerkClient';\n\n/**\n * @deprecated Use imports from `@clerk/nextjs` instead.\n * For more details, consult the middleware documentation: https://clerk.com/docs/nextjs/middleware\n */\nexport async function currentUser(): Promise<User | null> {\n  const { userId } = auth();\n  return userId ? clerkClient.users.getUser(userId) : null;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,wBAA2B;AAM3B,kBAAqB;AACrB,yBAA4B;AAAA,IAL5B;AAAA,EACE;AAAA,EACA;AACF;AAQA,eAAsB,cAAoC;AACxD,QAAM,EAAE,OAAO,QAAI,kBAAK;AACxB,SAAO,SAAS,+BAAY,MAAM,QAAQ,MAAM,IAAI;AACtD;", "names": []}