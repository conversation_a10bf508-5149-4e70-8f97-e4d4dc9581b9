{"version": 3, "sources": ["../../../src/app-beta/index.ts"], "sourcesContent": ["export { auth } from './auth';\nexport { currentUser } from './currentUser';\nexport { ClerkProvider } from './ClerkProvider';\nexport { SignedIn, SignedOut } from './control-components';\nexport {\n  UserButton,\n  SignUp,\n  SignIn,\n  OrganizationProfile,\n  CreateOrganization,\n  OrganizationSwitcher,\n  UserProfile,\n} from './client/ui-components';\nexport { clerkClient } from './clerkClient';\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAqB;AACrB,yBAA4B;AAC5B,2BAA8B;AAC9B,gCAAoC;AACpC,2BAQO;AACP,yBAA4B;", "names": []}