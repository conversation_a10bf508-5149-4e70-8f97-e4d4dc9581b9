{"version": 3, "sources": ["../../../../src/app-beta/client/ClerkProvider.tsx"], "sourcesContent": ["'use client';\nimport { deprecated } from '@clerk/shared/deprecated';\n\ndeprecated(\n  '@clerk/nextjs/app-beta',\n  'Use imports from `@clerk/nextjs` instead.\\nFor more details, consult the middleware documentation: https://clerk.com/docs/nextjs/middleware',\n);\n// !!! Note the import from react\nimport type { ClerkProviderProps } from '@clerk/clerk-react';\nimport { ClerkProvider as ReactClerkProvider } from '@clerk/clerk-react';\nimport { usePathname, useRouter } from 'next/navigation';\nimport React, { useCallback, useEffect } from 'react';\n\ndeclare global {\n  export interface Window {\n    __clerk_nav_await: Array<(value: void) => void>;\n    __clerk_nav: (to: string) => Promise<void>;\n  }\n}\n\n/**\n * @deprecated Use imports from `@clerk/nextjs` instead.\n * For more details, consult the middleware documentation: https://clerk.com/docs/nextjs/middleware\n */\nexport const useAwaitableNavigate = () => {\n  // eslint-disable-next-line @typescript-eslint/unbound-method\n  const { push, refresh } = useRouter();\n  const pathname = usePathname();\n\n  useEffect(() => {\n    window.__clerk_nav = (to: string) => {\n      return new Promise(res => {\n        window.__clerk_nav_await.push(res);\n        if (to === pathname) {\n          refresh();\n        } else {\n          push(to);\n        }\n      });\n    };\n  }, [pathname]);\n\n  useEffect(() => {\n    if (typeof window.__clerk_nav_await === 'undefined') {\n      window.__clerk_nav_await = [];\n    }\n    window.__clerk_nav_await.forEach(resolve => resolve());\n    window.__clerk_nav_await = [];\n  });\n\n  return useCallback((to: string) => {\n    return window.__clerk_nav(to);\n  }, []);\n};\n\n/**\n * @deprecated Use imports from `@clerk/nextjs` instead.\n * For more details, consult the middleware documentation: https://clerk.com/docs/nextjs/middleware\n */\nexport function ClerkProvider(props: ClerkProviderProps) {\n  const navigate = useAwaitableNavigate();\n  return (\n    <ReactClerkProvider\n      navigate={navigate}\n      {...props}\n    />\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,wBAA2B;AAQ3B,yBAAoD;AACpD,wBAAuC;AACvC,mBAA8C;AAAA,IAR9C;AAAA,EACE;AAAA,EACA;AACF;AAkBO,MAAM,uBAAuB,MAAM;AAExC,QAAM,EAAE,MAAM,QAAQ,QAAI,6BAAU;AACpC,QAAM,eAAW,+BAAY;AAE7B,8BAAU,MAAM;AACd,WAAO,cAAc,CAAC,OAAe;AACnC,aAAO,IAAI,QAAQ,SAAO;AACxB,eAAO,kBAAkB,KAAK,GAAG;AACjC,YAAI,OAAO,UAAU;AACnB,kBAAQ;AAAA,QACV,OAAO;AACL,eAAK,EAAE;AAAA,QACT;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,GAAG,CAAC,QAAQ,CAAC;AAEb,8BAAU,MAAM;AACd,QAAI,OAAO,OAAO,sBAAsB,aAAa;AACnD,aAAO,oBAAoB,CAAC;AAAA,IAC9B;AACA,WAAO,kBAAkB,QAAQ,aAAW,QAAQ,CAAC;AACrD,WAAO,oBAAoB,CAAC;AAAA,EAC9B,CAAC;AAED,aAAO,0BAAY,CAAC,OAAe;AACjC,WAAO,OAAO,YAAY,EAAE;AAAA,EAC9B,GAAG,CAAC,CAAC;AACP;AAMO,SAAS,cAAc,OAA2B;AACvD,QAAM,WAAW,qBAAqB;AACtC,SACE,6BAAAA,QAAA;AAAA,IAAC,mBAAAC;AAAA,IAAA;AAAA,MACC;AAAA,MACC,GAAG;AAAA;AAAA,EACN;AAEJ;", "names": ["React", "ReactClerkProvider"]}