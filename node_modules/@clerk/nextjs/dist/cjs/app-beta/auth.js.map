{"version": 3, "sources": ["../../../src/app-beta/auth.ts"], "sourcesContent": ["import { deprecated } from '@clerk/shared/deprecated';\n\ndeprecated(\n  '@clerk/nextjs/app-beta',\n  'Use imports from `@clerk/nextjs` instead.\\nFor more details, consult the middleware documentation: https://clerk.com/docs/nextjs/middleware',\n);\n\nimport { headers } from 'next/headers';\nimport { NextRequest } from 'next/server';\n\nimport { buildClerkProps, getAuth } from '../server';\n\nconst buildRequestLike = () => {\n  return new NextRequest('https://placeholder.com', { headers: headers() });\n};\n\n/**\n * @deprecated Use imports from `@clerk/nextjs` instead.\n * For more details, consult the middleware documentation: https://clerk.com/docs/nextjs/middleware\n */\nexport const auth = () => {\n  return getAuth(buildRequestLike());\n};\n\n/**\n * @deprecated Use imports from `@clerk/nextjs` instead.\n * For more details, consult the middleware documentation: https://clerk.com/docs/nextjs/middleware\n */\nexport const initialState = () => {\n  return buildClerkProps(buildRequestLike());\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBAA2B;AAO3B,qBAAwB;AACxB,oBAA4B;AAE5B,IAAAA,iBAAyC;AAAA,IARzC;AAAA,EACE;AAAA,EACA;AACF;AAOA,MAAM,mBAAmB,MAAM;AAC7B,SAAO,IAAI,0BAAY,2BAA2B,EAAE,aAAS,wBAAQ,EAAE,CAAC;AAC1E;AAMO,MAAM,OAAO,MAAM;AACxB,aAAO,wBAAQ,iBAAiB,CAAC;AACnC;AAMO,MAAM,eAAe,MAAM;AAChC,aAAO,gCAAgB,iBAAiB,CAAC;AAC3C;", "names": ["import_server"]}