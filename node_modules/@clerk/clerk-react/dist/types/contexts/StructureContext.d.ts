import React from 'react';
export interface StructureContextValue {
    guaranteedLoaded: boolean;
}
export declare const StructureContextStates: Readonly<{
    noGuarantees: Readonly<{
        guaranteedLoaded: false;
    }>;
    guaranteedLoaded: Readonly<{
        guaranteedLoaded: true;
    }>;
}>;
export declare const StructureContext: React.Context<StructureContextValue | undefined>;
export declare const LoadedGuarantee: React.FC<React.PropsWithChildren<unknown>>;
//# sourceMappingURL=StructureContext.d.ts.map