import type { InitialState } from '@clerk/types';
import React from 'react';
import type { IsomorphicClerkOptions } from '../types';
import { __internal__setErrorThrowerOptions } from '../utils';
export type ClerkProviderProps = IsomorphicClerkOptions & {
    children: React.ReactNode;
    initialState?: InitialState;
};
declare const ClerkProvider: React.ComponentType<ClerkProviderProps>;
export { ClerkProvider, __internal__setErrorThrowerOptions };
//# sourceMappingURL=ClerkProvider.d.ts.map