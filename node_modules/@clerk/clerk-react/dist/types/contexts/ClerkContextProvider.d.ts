import type { InitialState, Resources } from '@clerk/types';
import React from 'react';
import type { IsomorphicClerkOptions } from '../types';
type ClerkContextProvider = {
    isomorphicClerkOptions: IsomorphicClerkOptions;
    initialState: InitialState | undefined;
    children: React.ReactNode;
};
export type ClerkContextProviderState = Resources;
export declare function ClerkContextProvider(props: ClerkContextProvider): JSX.Element | null;
export {};
//# sourceMappingURL=ClerkContextProvider.d.ts.map