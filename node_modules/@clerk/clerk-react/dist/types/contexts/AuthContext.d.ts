/// <reference types="react" />
import type { ActJWTClaim, MembershipRole, OrganizationCustomPermissionKey } from '@clerk/types';
export declare const AuthContext: import("react").Context<{
    value: {
        userId: string | null | undefined;
        sessionId: string | null | undefined;
        actor: ActJWTClaim | null | undefined;
        orgId: string | null | undefined;
        orgRole: MembershipRole | null | undefined;
        orgSlug: string | null | undefined;
        orgPermissions: OrganizationCustomPermissionKey[] | null | undefined;
    };
} | undefined>, useAuthContext: () => {
    userId: string | null | undefined;
    sessionId: string | null | undefined;
    actor: ActJWTClaim | null | undefined;
    orgId: string | null | undefined;
    orgRole: MembershipRole | null | undefined;
    orgSlug: string | null | undefined;
    orgPermissions: OrganizationCustomPermissionKey[] | null | undefined;
};
//# sourceMappingURL=AuthContext.d.ts.map