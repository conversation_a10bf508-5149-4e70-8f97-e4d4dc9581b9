import type { CheckAuthorizationWithCustomPermissions, HandleOAuthCallbackParams, OrganizationCustomPermissionKey, OrganizationCustomRoleKey } from '@clerk/types';
import React from 'react';
import type { WithClerkProp } from '../types';
export declare const SignedIn: ({ children }: React.PropsWithChildren<unknown>) => JSX.Element | null;
export declare const SignedOut: ({ children }: React.PropsWithChildren<unknown>) => JSX.Element | null;
export declare const ClerkLoaded: ({ children }: React.PropsWithChildren<unknown>) => JSX.Element | null;
export declare const ClerkLoading: ({ children }: React.PropsWithChildren<unknown>) => JSX.Element | null;
type ProtectProps = React.PropsWithChildren<({
    condition?: never;
    role: OrganizationCustomRoleKey;
    permission?: never;
} | {
    condition?: never;
    role?: never;
    permission: OrganizationCustomPermissionKey;
} | {
    condition: (has: CheckAuthorizationWithCustomPermissions) => boolean;
    role?: never;
    permission?: never;
} | {
    condition?: never;
    role?: never;
    permission?: never;
}) & {
    fallback?: React.ReactNode;
}>;
/**
 * Use `<Protect/>` in order to prevent unauthenticated or unauthorized users from accessing the children passed to the component.
 *
 * Examples:
 * ```
 * <Protect permission="a_permission_key" />
 * <Protect role="a_role_key" />
 * <Protect condition={(has) => has({permission:"a_permission_key"})} />
 * <Protect condition={(has) => has({role:"a_role_key"})} />
 * <Protect fallback={<p>Unauthorized</p>} />
 * ```
 */
export declare const Protect: ({ children, fallback, ...restAuthorizedParams }: ProtectProps) => React.JSX.Element | null;
export declare const RedirectToSignIn: {
    (props: Omit<WithClerkProp<import("@clerk/types").SignInRedirectOptions>, "clerk">): React.JSX.Element | null;
    displayName: string;
};
export declare const RedirectToSignUp: {
    (props: Omit<WithClerkProp<import("@clerk/types").SignUpRedirectOptions>, "clerk">): React.JSX.Element | null;
    displayName: string;
};
export declare const RedirectToUserProfile: {
    (props: Omit<{
        clerk: import("@clerk/types").LoadedClerk;
    }, "clerk">): React.JSX.Element | null;
    displayName: string;
};
export declare const RedirectToOrganizationProfile: {
    (props: Omit<{
        clerk: import("@clerk/types").LoadedClerk;
    }, "clerk">): React.JSX.Element | null;
    displayName: string;
};
export declare const RedirectToCreateOrganization: {
    (props: Omit<{
        clerk: import("@clerk/types").LoadedClerk;
    }, "clerk">): React.JSX.Element | null;
    displayName: string;
};
export declare const AuthenticateWithRedirectCallback: {
    (props: Omit<WithClerkProp<HandleOAuthCallbackParams>, "clerk">): React.JSX.Element | null;
    displayName: string;
};
export declare const MultisessionAppSupport: ({ children }: React.PropsWithChildren<unknown>) => JSX.Element;
export {};
//# sourceMappingURL=controlComponents.d.ts.map