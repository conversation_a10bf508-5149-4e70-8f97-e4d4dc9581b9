import React from 'react';
import type { WithClerkProp } from '../types';
export declare const SignInButton: {
    (props: Omit<WithClerkProp<{
        afterSignInUrl?: string | undefined;
        afterSignUpUrl?: string | undefined;
        redirectUrl?: string | undefined;
        mode?: "redirect" | "modal" | undefined;
        children?: React.ReactNode;
    }>, "clerk">): React.JSX.Element | null;
    displayName: string;
};
//# sourceMappingURL=SignInButton.d.ts.map