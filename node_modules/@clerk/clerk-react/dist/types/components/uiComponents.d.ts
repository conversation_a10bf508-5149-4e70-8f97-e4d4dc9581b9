import type { CreateOrganizationProps, GoogleOneTapProps, OrganizationListProps, OrganizationProfileProps, OrganizationSwitcherProps, SignInProps, SignUpProps, UserButtonProps, UserProfileProps } from '@clerk/types';
import type { PropsWithChildren } from 'react';
import React from 'react';
import type { OrganizationProfileLinkProps, OrganizationProfilePageProps, UserProfileLinkProps, UserProfilePageProps, WithClerkProp } from '../types';
type UserProfileExportType = typeof _UserProfile & {
    Page: typeof UserProfilePage;
    Link: typeof UserProfileLink;
};
type UserButtonExportType = typeof _UserButton & {
    UserProfilePage: typeof UserProfilePage;
    UserProfileLink: typeof UserProfileLink;
};
type UserButtonPropsWithoutCustomPages = Omit<UserButtonProps, 'userProfileProps'> & {
    userProfileProps?: Pick<UserProfileProps, 'additionalOAuthScopes' | 'appearance'>;
};
type OrganizationProfileExportType = typeof _OrganizationProfile & {
    Page: typeof OrganizationProfilePage;
    Link: typeof OrganizationProfileLink;
};
type OrganizationSwitcherExportType = typeof _OrganizationSwitcher & {
    OrganizationProfilePage: typeof OrganizationProfilePage;
    OrganizationProfileLink: typeof OrganizationProfileLink;
};
type OrganizationSwitcherPropsWithoutCustomPages = Omit<OrganizationSwitcherProps, 'organizationProfileProps'> & {
    organizationProfileProps?: Pick<OrganizationProfileProps, 'appearance'>;
};
export declare const SignIn: {
    (props: Omit<WithClerkProp<SignInProps>, "clerk">): React.JSX.Element | null;
    displayName: string;
};
export declare const SignUp: {
    (props: Omit<WithClerkProp<SignUpProps>, "clerk">): React.JSX.Element | null;
    displayName: string;
};
export declare function UserProfilePage({ children }: PropsWithChildren<UserProfilePageProps>): React.JSX.Element;
export declare function UserProfileLink({ children }: PropsWithChildren<UserProfileLinkProps>): React.JSX.Element;
declare const _UserProfile: {
    (props: Omit<WithClerkProp<PropsWithChildren<Omit<UserProfileProps, "customPages">>>, "clerk">): React.JSX.Element | null;
    displayName: string;
};
export declare const UserProfile: UserProfileExportType;
declare const _UserButton: {
    (props: Omit<WithClerkProp<PropsWithChildren<UserButtonPropsWithoutCustomPages>>, "clerk">): React.JSX.Element | null;
    displayName: string;
};
export declare const UserButton: UserButtonExportType;
export declare function OrganizationProfilePage({ children }: PropsWithChildren<OrganizationProfilePageProps>): React.JSX.Element;
export declare function OrganizationProfileLink({ children }: PropsWithChildren<OrganizationProfileLinkProps>): React.JSX.Element;
declare const _OrganizationProfile: {
    (props: Omit<WithClerkProp<PropsWithChildren<Omit<OrganizationProfileProps, "customPages">>>, "clerk">): React.JSX.Element | null;
    displayName: string;
};
export declare const OrganizationProfile: OrganizationProfileExportType;
export declare const CreateOrganization: {
    (props: Omit<WithClerkProp<CreateOrganizationProps>, "clerk">): React.JSX.Element | null;
    displayName: string;
};
declare const _OrganizationSwitcher: {
    (props: Omit<WithClerkProp<PropsWithChildren<OrganizationSwitcherPropsWithoutCustomPages>>, "clerk">): React.JSX.Element | null;
    displayName: string;
};
export declare const OrganizationSwitcher: OrganizationSwitcherExportType;
export declare const OrganizationList: {
    (props: Omit<WithClerkProp<OrganizationListProps>, "clerk">): React.JSX.Element | null;
    displayName: string;
};
export declare const GoogleOneTap: {
    (props: Omit<WithClerkProp<GoogleOneTapProps>, "clerk">): React.JSX.Element | null;
    displayName: string;
};
export {};
//# sourceMappingURL=uiComponents.d.ts.map