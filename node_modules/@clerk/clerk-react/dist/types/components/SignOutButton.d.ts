import type { SignOutCallback, SignOutOptions } from '@clerk/types';
import React from 'react';
import type { WithClerkProp } from '../types';
export type SignOutButtonProps = {
    signOutCallback?: SignOutCallback;
    signOutOptions?: SignOutOptions;
    children?: React.ReactNode;
};
export declare const SignOutButton: {
    (props: Omit<React.PropsWithChildren<WithClerkProp<SignOutButtonProps>>, "clerk">): React.JSX.Element | null;
    displayName: string;
};
//# sourceMappingURL=SignOutButton.d.ts.map