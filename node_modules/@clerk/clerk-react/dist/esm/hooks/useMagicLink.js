import "../chunk-XTU7I5IS.js";
import { deprecated } from "@clerk/shared/deprecated";
import React from "react";
function useMagicLink(resource) {
  deprecated("useMagicLink", "Use `useEmailLink` instead.");
  const { startMagicLinkFlow, cancelMagicLinkFlow } = React.useMemo(() => resource.createMagicLinkFlow(), [resource]);
  React.useEffect(() => {
    return cancelMagicLinkFlow;
  }, []);
  return {
    startMagicLinkFlow,
    cancelMagicLinkFlow
  };
}
export {
  useMagicLink
};
//# sourceMappingURL=useMagicLink.js.map