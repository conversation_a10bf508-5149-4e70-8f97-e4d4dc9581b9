{"version": 3, "sources": ["../../../src/hooks/useAuth.ts"], "sourcesContent": ["import type {\n  ActJWTClaim,\n  CheckAuthorizationWithCustomPermissions,\n  GetToken,\n  MembershipRole,\n  SignOut,\n} from '@clerk/types';\nimport { useCallback } from 'react';\n\nimport { useAuthContext } from '../contexts/AuthContext';\nimport { useIsomorphicClerkContext } from '../contexts/IsomorphicClerkContext';\nimport { invalidStateError, useAuthHasRequiresRoleOrPermission } from '../errors';\nimport type IsomorphicClerk from '../isomorphicClerk';\nimport { createGetToken, createSignOut } from './utils';\n\ntype CheckAuthorizationSignedOut = undefined;\ntype CheckAuthorizationWithoutOrgOrUser = (params?: Parameters<CheckAuthorizationWithCustomPermissions>[0]) => false;\n\ntype UseAuthReturn =\n  | {\n      isLoaded: false;\n      isSignedIn: undefined;\n      userId: undefined;\n      sessionId: undefined;\n      actor: undefined;\n      orgId: undefined;\n      orgRole: undefined;\n      orgSlug: undefined;\n      has: CheckAuthorizationSignedOut;\n      signOut: SignOut;\n      getToken: GetToken;\n    }\n  | {\n      isLoaded: true;\n      isSignedIn: false;\n      userId: null;\n      sessionId: null;\n      actor: null;\n      orgId: null;\n      orgRole: null;\n      orgSlug: null;\n      has: CheckAuthorizationWithoutOrgOrUser;\n      signOut: SignOut;\n      getToken: GetToken;\n    }\n  | {\n      isLoaded: true;\n      isSignedIn: true;\n      userId: string;\n      sessionId: string;\n      actor: ActJWTClaim | null;\n      orgId: null;\n      orgRole: null;\n      orgSlug: null;\n      has: CheckAuthorizationWithoutOrgOrUser;\n      signOut: SignOut;\n      getToken: GetToken;\n    }\n  | {\n      isLoaded: true;\n      isSignedIn: true;\n      userId: string;\n      sessionId: string;\n      actor: ActJWTClaim | null;\n      orgId: string;\n      orgRole: MembershipRole;\n      orgSlug: string | null;\n      has: CheckAuthorizationWithCustomPermissions;\n      signOut: SignOut;\n      getToken: GetToken;\n    };\n\ntype UseAuth = () => UseAuthReturn;\n\n/**\n * Returns the current auth state, the user and session ids and the `getToken`\n * that can be used to retrieve the given template or the default Clerk token.\n *\n * Until Clerk loads, `isLoaded` will be set to `false`.\n * Once Clerk loads, `isLoaded` will be set to `true`, and you can\n * safely access the `userId` and `sessionId` variables.\n *\n * For projects using NextJs or Remix, you can have immediate access to this data  during SSR\n * simply by using the `withServerSideAuth` helper.\n *\n * @example\n * A simple example:\n *\n * import { useAuth } from '@clerk/clerk-react'\n *\n * function Hello() {\n *   const { isSignedIn, sessionId, userId } = useAuth();\n *   if(isSignedIn) {\n *     return null;\n *   }\n *   console.log(sessionId, userId)\n *   return <div>...</div>\n * }\n *\n * @example\n * Basic example in a NextJs app. This page will be fully rendered during SSR:\n *\n * import { useAuth } from '@clerk/nextjs'\n * import { withServerSideAuth } from '@clerk/nextjs/api'\n *\n * export getServerSideProps = withServerSideAuth();\n *\n * export HelloPage = () => {\n *   const { isSignedIn, sessionId, userId } = useAuth();\n *   console.log(isSignedIn, sessionId, userId)\n *   return <div>...</div>\n * }\n */\nexport const useAuth: UseAuth = () => {\n  const { sessionId, userId, actor, orgId, orgRole, orgSlug, orgPermissions } = useAuthContext();\n  const isomorphicClerk = useIsomorphicClerkContext() as unknown as IsomorphicClerk;\n\n  const getToken: GetToken = useCallback(createGetToken(isomorphicClerk), [isomorphicClerk]);\n  const signOut: SignOut = useCallback(createSignOut(isomorphicClerk), [isomorphicClerk]);\n\n  const has = useCallback(\n    (params: Parameters<CheckAuthorizationWithCustomPermissions>[0]) => {\n      if (!params?.permission && !params?.role) {\n        throw new Error(useAuthHasRequiresRoleOrPermission);\n      }\n\n      if (!orgId || !userId || !orgRole || !orgPermissions) {\n        return false;\n      }\n\n      if (params.permission) {\n        return orgPermissions.includes(params.permission);\n      }\n\n      if (params.role) {\n        return orgRole === params.role;\n      }\n\n      return false;\n    },\n    [orgId, orgRole, userId, orgPermissions],\n  );\n\n  if (sessionId === undefined && userId === undefined) {\n    return {\n      isLoaded: false,\n      isSignedIn: undefined,\n      sessionId,\n      userId,\n      actor: undefined,\n      orgId: undefined,\n      orgRole: undefined,\n      orgSlug: undefined,\n      has: undefined,\n      signOut,\n      getToken,\n    };\n  }\n\n  if (sessionId === null && userId === null) {\n    return {\n      isLoaded: true,\n      isSignedIn: false,\n      sessionId,\n      userId,\n      actor: null,\n      orgId: null,\n      orgRole: null,\n      orgSlug: null,\n      has: () => false,\n      signOut,\n      getToken,\n    };\n  }\n\n  if (!!sessionId && !!userId && !!orgId && !!orgRole) {\n    return {\n      isLoaded: true,\n      isSignedIn: true,\n      sessionId,\n      userId,\n      actor: actor || null,\n      orgId,\n      orgRole,\n      orgSlug: orgSlug || null,\n      has,\n      signOut,\n      getToken,\n    };\n  }\n\n  if (!!sessionId && !!userId && !orgId) {\n    return {\n      isLoaded: true,\n      isSignedIn: true,\n      sessionId,\n      userId,\n      actor: actor || null,\n      orgId: null,\n      orgRole: null,\n      orgSlug: null,\n      has: () => false,\n      signOut,\n      getToken,\n    };\n  }\n\n  throw new Error(invalidStateError);\n};\n"], "mappings": ";AAOA,SAAS,mBAAmB;AAE5B,SAAS,sBAAsB;AAC/B,SAAS,iCAAiC;AAC1C,SAAS,mBAAmB,0CAA0C;AAEtE,SAAS,gBAAgB,qBAAqB;AAoGvC,MAAM,UAAmB,MAAM;AACpC,QAAM,EAAE,WAAW,QAAQ,OAAO,OAAO,SAAS,SAAS,eAAe,IAAI,eAAe;AAC7F,QAAM,kBAAkB,0BAA0B;AAElD,QAAM,WAAqB,YAAY,eAAe,eAAe,GAAG,CAAC,eAAe,CAAC;AACzF,QAAM,UAAmB,YAAY,cAAc,eAAe,GAAG,CAAC,eAAe,CAAC;AAEtF,QAAM,MAAM;AAAA,IACV,CAAC,WAAmE;AAClE,UAAI,EAAC,iCAAQ,eAAc,EAAC,iCAAQ,OAAM;AACxC,cAAM,IAAI,MAAM,kCAAkC;AAAA,MACpD;AAEA,UAAI,CAAC,SAAS,CAAC,UAAU,CAAC,WAAW,CAAC,gBAAgB;AACpD,eAAO;AAAA,MACT;AAEA,UAAI,OAAO,YAAY;AACrB,eAAO,eAAe,SAAS,OAAO,UAAU;AAAA,MAClD;AAEA,UAAI,OAAO,MAAM;AACf,eAAO,YAAY,OAAO;AAAA,MAC5B;AAEA,aAAO;AAAA,IACT;AAAA,IACA,CAAC,OAAO,SAAS,QAAQ,cAAc;AAAA,EACzC;AAEA,MAAI,cAAc,UAAa,WAAW,QAAW;AACnD,WAAO;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ;AAAA,MACA;AAAA,MACA,OAAO;AAAA,MACP,OAAO;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,KAAK;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,MAAI,cAAc,QAAQ,WAAW,MAAM;AACzC,WAAO;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ;AAAA,MACA;AAAA,MACA,OAAO;AAAA,MACP,OAAO;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,KAAK,MAAM;AAAA,MACX;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,MAAI,CAAC,CAAC,aAAa,CAAC,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,CAAC,SAAS;AACnD,WAAO;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ;AAAA,MACA;AAAA,MACA,OAAO,SAAS;AAAA,MAChB;AAAA,MACA;AAAA,MACA,SAAS,WAAW;AAAA,MACpB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,MAAI,CAAC,CAAC,aAAa,CAAC,CAAC,UAAU,CAAC,OAAO;AACrC,WAAO;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ;AAAA,MACA;AAAA,MACA,OAAO,SAAS;AAAA,MAChB,OAAO;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,KAAK,MAAM;AAAA,MACX;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,QAAM,IAAI,MAAM,iBAAiB;AACnC;", "names": []}