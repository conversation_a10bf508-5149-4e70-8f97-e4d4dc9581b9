{"version": 3, "sources": ["../../../src/hooks/useSession.ts"], "sourcesContent": ["import type { ActiveSessionResource } from '@clerk/types';\n\nimport { useSessionContext } from '../contexts/SessionContext';\n\ntype UseSessionReturn =\n  | { isLoaded: false; isSignedIn: undefined; session: undefined }\n  | { isLoaded: true; isSignedIn: false; session: null }\n  | { isLoaded: true; isSignedIn: true; session: ActiveSessionResource };\n\ntype UseSession = () => UseSessionReturn;\n\n/**\n * Returns the current auth state and if a session exists, the session object.\n *\n * Until Clerk loads and initializes, `isLoaded` will be set to `false`.\n * Once Clerk loads, `isLoaded` will be set to `true`, and you can\n * safely access `isSignedIn` state and `session`.\n *\n * For projects using NextJs or Remix, you can make this state available during SSR\n * simply by using the `withServerSideAuth` helper and setting the `loadSession` flag to `true`.\n *\n * @example\n * A simple example:\n *\n * import { useSession } from '@clerk/clerk-react'\n *\n * function Hello() {\n *   const { isSignedIn, session } = useSession();\n *   if(!isSignedIn) {\n *     return null;\n *   }\n *   return <div>{session.updatedAt}</div>\n * }\n *\n * @example\n * Basic example in a NextJs app. This page will be fully rendered during SSR:\n *\n * import { useSession } from '@clerk/nextjs'\n * import { withServerSideAuth } from '@clerk/nextjs/api'\n *\n * export getServerSideProps = withServerSideAuth({ loadSession: true});\n *\n * export HelloPage = () => {\n *   const { isSignedIn, session } = useSession();\n *   if(!isSignedIn) {\n *     return null;\n *   }\n *  return <div>{session.updatedAt}</div>\n * }\n */\nexport const useSession: UseSession = () => {\n  const session = useSessionContext();\n\n  if (session === undefined) {\n    return { isLoaded: false, isSignedIn: undefined, session: undefined };\n  }\n\n  if (session === null) {\n    return { isLoaded: true, isSignedIn: false, session: null };\n  }\n\n  return { isLoaded: true, isSignedIn: true, session };\n};\n"], "mappings": ";AAEA,SAAS,yBAAyB;AAgD3B,MAAM,aAAyB,MAAM;AAC1C,QAAM,UAAU,kBAAkB;AAElC,MAAI,YAAY,QAAW;AACzB,WAAO,EAAE,UAAU,OAAO,YAAY,QAAW,SAAS,OAAU;AAAA,EACtE;AAEA,MAAI,YAAY,MAAM;AACpB,WAAO,EAAE,UAAU,MAAM,YAAY,OAAO,SAAS,KAAK;AAAA,EAC5D;AAEA,SAAO,EAAE,UAAU,MAAM,YAAY,MAAM,QAAQ;AACrD;", "names": []}