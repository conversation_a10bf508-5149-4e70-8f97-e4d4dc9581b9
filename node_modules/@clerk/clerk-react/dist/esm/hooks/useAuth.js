import "../chunk-XTU7I5IS.js";
import { useCallback } from "react";
import { useAuthContext } from "../contexts/AuthContext";
import { useIsomorphicClerkContext } from "../contexts/IsomorphicClerkContext";
import { invalidStateError, useAuthHasRequiresRoleOrPermission } from "../errors";
import { createGetToken, createSignOut } from "./utils";
const useAuth = () => {
  const { sessionId, userId, actor, orgId, orgRole, orgSlug, orgPermissions } = useAuthContext();
  const isomorphicClerk = useIsomorphicClerkContext();
  const getToken = useCallback(createGetToken(isomorphicClerk), [isomorphicClerk]);
  const signOut = useCallback(createSignOut(isomorphicClerk), [isomorphicClerk]);
  const has = useCallback(
    (params) => {
      if (!(params == null ? void 0 : params.permission) && !(params == null ? void 0 : params.role)) {
        throw new Error(useAuthHasRequiresRoleOrPermission);
      }
      if (!orgId || !userId || !orgRole || !orgPermissions) {
        return false;
      }
      if (params.permission) {
        return orgPermissions.includes(params.permission);
      }
      if (params.role) {
        return orgRole === params.role;
      }
      return false;
    },
    [orgId, orgRole, userId, orgPermissions]
  );
  if (sessionId === void 0 && userId === void 0) {
    return {
      isLoaded: false,
      isSignedIn: void 0,
      sessionId,
      userId,
      actor: void 0,
      orgId: void 0,
      orgRole: void 0,
      orgSlug: void 0,
      has: void 0,
      signOut,
      getToken
    };
  }
  if (sessionId === null && userId === null) {
    return {
      isLoaded: true,
      isSignedIn: false,
      sessionId,
      userId,
      actor: null,
      orgId: null,
      orgRole: null,
      orgSlug: null,
      has: () => false,
      signOut,
      getToken
    };
  }
  if (!!sessionId && !!userId && !!orgId && !!orgRole) {
    return {
      isLoaded: true,
      isSignedIn: true,
      sessionId,
      userId,
      actor: actor || null,
      orgId,
      orgRole,
      orgSlug: orgSlug || null,
      has,
      signOut,
      getToken
    };
  }
  if (!!sessionId && !!userId && !orgId) {
    return {
      isLoaded: true,
      isSignedIn: true,
      sessionId,
      userId,
      actor: actor || null,
      orgId: null,
      orgRole: null,
      orgSlug: null,
      has: () => false,
      signOut,
      getToken
    };
  }
  throw new Error(invalidStateError);
};
export {
  useAuth
};
//# sourceMappingURL=useAuth.js.map