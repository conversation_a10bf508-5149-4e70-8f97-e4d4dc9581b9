{"version": 3, "sources": ["../../../src/hooks/useSessionList.ts"], "sourcesContent": ["import type { SessionResource, SetActive, SetSession } from '@clerk/types';\n\nimport { useClientContext } from '../contexts/ClientContext';\nimport { useIsomorphicClerkContext } from '../contexts/IsomorphicClerkContext';\n\ntype UseSessionListReturn =\n  | {\n      isLoaded: false;\n      sessions: undefined;\n      /**\n       * @deprecated This method is deprecated and will be removed in the future. Use {@link Clerk.setActive} instead\n       * Set the current session explicitly. Setting the session to `null` unsets the active session and signs out the user.\n       * @param session Passed session resource object, session id (string version) or null\n       * @param beforeEmit Callback run just before the active session is set to the passed object. Can be used to hook up for pre-navigation actions.\n       */\n      setSession: undefined;\n      setActive: undefined;\n    }\n  | {\n      isLoaded: true;\n      sessions: SessionResource[];\n\n      /**\n       * @deprecated This method is deprecated and will be removed in the future. Use {@link Clerk.setActive} instead\n       * Set the current session explicitly. Setting the session to `null` unsets the active session and signs out the user.\n       * @param session Passed session resource object, session id (string version) or null\n       * @param beforeEmit Callback run just before the active session is set to the passed object. Can be used to hook up for pre-navigation actions.\n       */\n      setSession: SetSession;\n      setActive: SetActive;\n    };\n\ntype UseSessionList = () => UseSessionListReturn;\n\nexport const useSessionList: UseSessionList = () => {\n  const isomorphicClerk = useIsomorphicClerkContext();\n  const client = useClientContext();\n\n  if (!client) {\n    return { isLoaded: false, sessions: undefined, setSession: undefined, setActive: undefined };\n  }\n\n  return {\n    isLoaded: true,\n    sessions: client.sessions,\n    setSession: isomorphicClerk.setSession,\n    setActive: isomorphicClerk.setActive,\n  };\n};\n"], "mappings": ";AAEA,SAAS,wBAAwB;AACjC,SAAS,iCAAiC;AA+BnC,MAAM,iBAAiC,MAAM;AAClD,QAAM,kBAAkB,0BAA0B;AAClD,QAAM,SAAS,iBAAiB;AAEhC,MAAI,CAAC,QAAQ;AACX,WAAO,EAAE,UAAU,OAAO,UAAU,QAAW,YAAY,QAAW,WAAW,OAAU;AAAA,EAC7F;AAEA,SAAO;AAAA,IACL,UAAU;AAAA,IACV,UAAU,OAAO;AAAA,IACjB,YAAY,gBAAgB;AAAA,IAC5B,WAAW,gBAAgB;AAAA,EAC7B;AACF;", "names": []}