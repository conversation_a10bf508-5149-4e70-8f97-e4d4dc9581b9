import "../chunk-XTU7I5IS.js";
import React from "react";
function useEmailLink(resource) {
  const { startEmailLinkFlow, cancelEmailLinkFlow } = React.useMemo(() => resource.createEmailLinkFlow(), [resource]);
  React.useEffect(() => {
    return cancelEmailLinkFlow;
  }, []);
  return {
    startEmailLinkFlow,
    cancelEmailLinkFlow
  };
}
export {
  useEmailLink
};
//# sourceMappingURL=useEmailLink.js.map