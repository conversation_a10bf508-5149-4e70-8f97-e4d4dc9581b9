{"version": 3, "sources": ["../../../src/hooks/useMagicLink.ts"], "sourcesContent": ["import { deprecated } from '@clerk/shared/deprecated';\nimport type {\n  CreateMagicLinkFlowReturn,\n  EmailAddressResource,\n  SignInResource,\n  SignInStartMagicLinkFlowParams,\n  SignUpResource,\n  StartMagicLinkFlowParams,\n} from '@clerk/types';\nimport React from 'react';\n\ntype MagicLinkable = SignUpResource | EmailAddressResource | SignInResource;\ntype UseMagicLinkSignInReturn = CreateMagicLinkFlowReturn<SignInStartMagicLinkFlowParams, SignInResource>;\ntype UseMagicLinkSignUpReturn = CreateMagicLinkFlowReturn<StartMagicLinkFlowParams, SignUpResource>;\ntype UseMagicLinkEmailAddressReturn = CreateMagicLinkFlowReturn<StartMagicLinkFlowParams, EmailAddressResource>;\n\n/**\n * @deprecated Use `useEmailLink` instead.\n */\nfunction useMagicLink(resource: SignInResource): UseMagicLinkSignInReturn;\n/**\n * @deprecated Use `useEmailLink` instead.\n */\nfunction useMagicLink(resource: SignUpResource): UseMagicLinkSignUpReturn;\n/**\n * @deprecated Use `useEmailLink` instead.\n */\nfunction useMagicLink(resource: EmailAddressResource): UseMagicLinkEmailAddressReturn;\nfunction useMagicLink(\n  resource: MagicLinkable,\n): UseMagicLinkSignInReturn | UseMagicLinkSignUpReturn | UseMagicLinkEmailAddressReturn {\n  deprecated('useMagicLink', 'Use `useEmailLink` instead.');\n\n  const { startMagicLinkFlow, cancelMagicLinkFlow } = React.useMemo(() => resource.createMagicLinkFlow(), [resource]);\n\n  React.useEffect(() => {\n    return cancelMagicLinkFlow;\n  }, []);\n\n  return {\n    startMagicLinkFlow,\n    cancelMagicLinkFlow,\n  } as UseMagicLinkSignInReturn | UseMagicLinkSignUpReturn | UseMagicLinkEmailAddressReturn;\n}\n\nexport { useMagicLink };\n"], "mappings": ";AAAA,SAAS,kBAAkB;AAS3B,OAAO,WAAW;AAmBlB,SAAS,aACP,UACsF;AACtF,aAAW,gBAAgB,6BAA6B;AAExD,QAAM,EAAE,oBAAoB,oBAAoB,IAAI,MAAM,QAAQ,MAAM,SAAS,oBAAoB,GAAG,CAAC,QAAQ,CAAC;AAElH,QAAM,UAAU,MAAM;AACpB,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AAEL,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;", "names": []}