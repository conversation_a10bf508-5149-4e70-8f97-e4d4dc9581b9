{"version": 3, "sources": ["../../../src/hooks/index.ts"], "sourcesContent": ["export { useUser } from './useUser';\nexport { useAuth } from './useAuth';\nexport { useSession } from './useSession';\nexport { useClerk } from './useClerk';\nexport { useSignIn } from './useSignIn';\nexport { useSignUp } from './useSignUp';\nexport { useSessionList } from './useSessionList';\nexport { useOrganization } from './useOrganization';\nexport { useOrganizationList } from './useOrganizationList';\nexport { useOrganizations } from './useOrganizations';\nexport { useMagicLink } from './useMagicLink';\nexport { useEmailLink } from './useEmailLink';\n"], "mappings": ";AAAA,SAAS,eAAe;AACxB,SAAS,eAAe;AACxB,SAAS,kBAAkB;AAC3B,SAAS,gBAAgB;AACzB,SAAS,iBAAiB;AAC1B,SAAS,iBAAiB;AAC1B,SAAS,sBAAsB;AAC/B,SAAS,uBAAuB;AAChC,SAAS,2BAA2B;AACpC,SAAS,wBAAwB;AACjC,SAAS,oBAAoB;AAC7B,SAAS,oBAAoB;", "names": []}