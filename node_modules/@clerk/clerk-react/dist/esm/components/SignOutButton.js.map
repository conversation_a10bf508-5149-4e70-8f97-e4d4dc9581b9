{"version": 3, "sources": ["../../../src/components/SignOutButton.tsx"], "sourcesContent": ["import type { SignOutCallback, SignOutOptions } from '@clerk/types';\nimport React from 'react';\n\nimport type { WithClerkProp } from '../types';\nimport { assertSingleChild, normalizeWithDefaultValue, safeExecute } from '../utils';\nimport { withClerk } from './withClerk';\n\nexport type SignOutButtonProps = {\n  signOutCallback?: SignOutCallback;\n  signOutOptions?: SignOutOptions;\n  children?: React.ReactNode;\n};\n\nexport const SignOutButton = withClerk(\n  ({ clerk, children, ...props }: React.PropsWithChildren<WithClerkProp<SignOutButtonProps>>) => {\n    const { signOutCallback, signOutOptions, ...rest } = props;\n\n    children = normalizeWithDefaultValue(children, 'Sign out');\n    const child = assertSingleChild(children)('SignOutButton');\n\n    const clickHandler = () => {\n      return clerk.signOut(signOutCallback, signOutOptions);\n    };\n\n    const wrappedChildClickHandler: React.MouseEventHandler = async e => {\n      await safeExecute((child as any).props.onClick)(e);\n      return clickHandler();\n    };\n\n    const childProps = { ...rest, onClick: wrappedChildClickHandler };\n    return React.cloneElement(child as React.ReactElement<unknown>, childProps);\n  },\n  'SignOutButton',\n);\n"], "mappings": ";AACA,OAAO,WAAW;AAGlB,SAAS,mBAAmB,2BAA2B,mBAAmB;AAC1E,SAAS,iBAAiB;AAQnB,MAAM,gBAAgB;AAAA,EAC3B,CAAC,EAAE,OAAO,UAAU,GAAG,MAAM,MAAkE;AAC7F,UAAM,EAAE,iBAAiB,gBAAgB,GAAG,KAAK,IAAI;AAErD,eAAW,0BAA0B,UAAU,UAAU;AACzD,UAAM,QAAQ,kBAAkB,QAAQ,EAAE,eAAe;AAEzD,UAAM,eAAe,MAAM;AACzB,aAAO,MAAM,QAAQ,iBAAiB,cAAc;AAAA,IACtD;AAEA,UAAM,2BAAoD,OAAM,MAAK;AACnE,YAAM,YAAa,MAAc,MAAM,OAAO,EAAE,CAAC;AACjD,aAAO,aAAa;AAAA,IACtB;AAEA,UAAM,aAAa,EAAE,GAAG,MAAM,SAAS,yBAAyB;AAChE,WAAO,MAAM,aAAa,OAAsC,UAAU;AAAA,EAC5E;AAAA,EACA;AACF;", "names": []}