import "../chunk-XTU7I5IS.js";
import { logErrorInDevMode } from "@clerk/shared";
import React, { createElement } from "react";
import {
  organizationProfileLinkRenderedError,
  organizationProfilePageRenderedError,
  userProfileLinkRenderedError,
  userProfilePageRenderedError
} from "../errors";
import { useOrganizationProfileCustomPages, useUserProfileCustomPages } from "../utils";
import { withClerk } from "./withClerk";
const isMountProps = (props) => {
  return "mount" in props;
};
const isOpenProps = (props) => {
  return "open" in props;
};
class Portal extends React.PureComponent {
  constructor() {
    super(...arguments);
    this.portalRef = React.createRef();
  }
  componentDidUpdate(prevProps) {
    var _a, _b, _c, _d;
    if (!isMountProps(prevProps) || !isMountProps(this.props)) {
      return;
    }
    if (prevProps.props.appearance !== this.props.props.appearance || ((_b = (_a = prevProps.props) == null ? void 0 : _a.customPages) == null ? void 0 : _b.length) !== ((_d = (_c = this.props.props) == null ? void 0 : _c.customPages) == null ? void 0 : _d.length)) {
      this.props.updateProps({ node: this.portalRef.current, props: this.props.props });
    }
  }
  componentDidMount() {
    if (this.portalRef.current) {
      if (isMountProps(this.props)) {
        this.props.mount(this.portalRef.current, this.props.props);
      }
      if (isOpenProps(this.props)) {
        this.props.open(this.props.props);
      }
    }
  }
  componentWillUnmount() {
    if (this.portalRef.current) {
      if (isMountProps(this.props)) {
        this.props.unmount(this.portalRef.current);
      }
      if (isOpenProps(this.props)) {
        this.props.close();
      }
    }
  }
  render() {
    var _a, _b;
    return /* @__PURE__ */ React.createElement(React.Fragment, null, /* @__PURE__ */ React.createElement("div", { ref: this.portalRef }), isMountProps(this.props) && ((_b = (_a = this.props) == null ? void 0 : _a.customPagesPortals) == null ? void 0 : _b.map((portal, index) => createElement(portal, { key: index }))));
  }
}
const SignIn = withClerk(({ clerk, ...props }) => {
  return /* @__PURE__ */ React.createElement(
    Portal,
    {
      mount: clerk.mountSignIn,
      unmount: clerk.unmountSignIn,
      updateProps: clerk.__unstable__updateProps,
      props
    }
  );
}, "SignIn");
const SignUp = withClerk(({ clerk, ...props }) => {
  return /* @__PURE__ */ React.createElement(
    Portal,
    {
      mount: clerk.mountSignUp,
      unmount: clerk.unmountSignUp,
      updateProps: clerk.__unstable__updateProps,
      props
    }
  );
}, "SignUp");
function UserProfilePage({ children }) {
  logErrorInDevMode(userProfilePageRenderedError);
  return /* @__PURE__ */ React.createElement(React.Fragment, null, children);
}
function UserProfileLink({ children }) {
  logErrorInDevMode(userProfileLinkRenderedError);
  return /* @__PURE__ */ React.createElement(React.Fragment, null, children);
}
const _UserProfile = withClerk(
  ({ clerk, ...props }) => {
    const { customPages, customPagesPortals } = useUserProfileCustomPages(props.children);
    return /* @__PURE__ */ React.createElement(
      Portal,
      {
        mount: clerk.mountUserProfile,
        unmount: clerk.unmountUserProfile,
        updateProps: clerk.__unstable__updateProps,
        props: { ...props, customPages },
        customPagesPortals
      }
    );
  },
  "UserProfile"
);
const UserProfile = Object.assign(_UserProfile, {
  Page: UserProfilePage,
  Link: UserProfileLink
});
const _UserButton = withClerk(
  ({ clerk, ...props }) => {
    const { customPages, customPagesPortals } = useUserProfileCustomPages(props.children);
    const userProfileProps = Object.assign(props.userProfileProps || {}, { customPages });
    return /* @__PURE__ */ React.createElement(
      Portal,
      {
        mount: clerk.mountUserButton,
        unmount: clerk.unmountUserButton,
        updateProps: clerk.__unstable__updateProps,
        props: { ...props, userProfileProps },
        customPagesPortals
      }
    );
  },
  "UserButton"
);
const UserButton = Object.assign(_UserButton, {
  UserProfilePage,
  UserProfileLink
});
function OrganizationProfilePage({ children }) {
  logErrorInDevMode(organizationProfilePageRenderedError);
  return /* @__PURE__ */ React.createElement(React.Fragment, null, children);
}
function OrganizationProfileLink({ children }) {
  logErrorInDevMode(organizationProfileLinkRenderedError);
  return /* @__PURE__ */ React.createElement(React.Fragment, null, children);
}
const _OrganizationProfile = withClerk(
  ({ clerk, ...props }) => {
    const { customPages, customPagesPortals } = useOrganizationProfileCustomPages(props.children);
    return /* @__PURE__ */ React.createElement(
      Portal,
      {
        mount: clerk.mountOrganizationProfile,
        unmount: clerk.unmountOrganizationProfile,
        updateProps: clerk.__unstable__updateProps,
        props: { ...props, customPages },
        customPagesPortals
      }
    );
  },
  "OrganizationProfile"
);
const OrganizationProfile = Object.assign(_OrganizationProfile, {
  Page: OrganizationProfilePage,
  Link: OrganizationProfileLink
});
const CreateOrganization = withClerk(({ clerk, ...props }) => {
  return /* @__PURE__ */ React.createElement(
    Portal,
    {
      mount: clerk.mountCreateOrganization,
      unmount: clerk.unmountCreateOrganization,
      updateProps: clerk.__unstable__updateProps,
      props
    }
  );
}, "CreateOrganization");
const _OrganizationSwitcher = withClerk(
  ({ clerk, ...props }) => {
    const { customPages, customPagesPortals } = useOrganizationProfileCustomPages(props.children);
    const organizationProfileProps = Object.assign(props.organizationProfileProps || {}, { customPages });
    return /* @__PURE__ */ React.createElement(
      Portal,
      {
        mount: clerk.mountOrganizationSwitcher,
        unmount: clerk.unmountOrganizationSwitcher,
        updateProps: clerk.__unstable__updateProps,
        props: { ...props, organizationProfileProps },
        customPagesPortals
      }
    );
  },
  "OrganizationSwitcher"
);
const OrganizationSwitcher = Object.assign(_OrganizationSwitcher, {
  OrganizationProfilePage,
  OrganizationProfileLink
});
const OrganizationList = withClerk(({ clerk, ...props }) => {
  return /* @__PURE__ */ React.createElement(
    Portal,
    {
      mount: clerk.mountOrganizationList,
      unmount: clerk.unmountOrganizationList,
      updateProps: clerk.__unstable__updateProps,
      props
    }
  );
}, "OrganizationList");
const GoogleOneTap = withClerk(({ clerk, ...props }) => {
  return /* @__PURE__ */ React.createElement(
    Portal,
    {
      open: clerk.openGoogleOneTap,
      close: clerk.closeGoogleOneTap,
      props
    }
  );
}, "GoogleOneTap");
export {
  CreateOrganization,
  GoogleOneTap,
  OrganizationList,
  OrganizationProfile,
  OrganizationProfileLink,
  OrganizationProfilePage,
  OrganizationSwitcher,
  SignIn,
  SignUp,
  UserButton,
  UserProfile,
  UserProfileLink,
  UserProfilePage
};
//# sourceMappingURL=uiComponents.js.map