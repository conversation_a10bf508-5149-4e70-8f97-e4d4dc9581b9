{"version": 3, "sources": ["../../../../src/components/__tests__/SignOutButton.test.tsx"], "sourcesContent": ["import { render, screen, waitFor } from '@testing-library/react';\nimport userEvent from '@testing-library/user-event';\nimport React from 'react';\n\nimport { SignOutButton } from '../SignOutButton';\n\nconst mockSignOut = jest.fn();\nconst originalError = console.error;\n\nconst mockClerk = {\n  signOut: mockSignOut,\n} as any;\n\njest.mock('../withClerk', () => {\n  return {\n    withClerk: (Component: any) => (props: any) => {\n      return (\n        <Component\n          {...props}\n          clerk={mockClerk}\n        />\n      );\n    },\n  };\n});\n\ndescribe('<SignOutButton />', () => {\n  beforeAll(() => {\n    console.error = jest.fn();\n  });\n\n  afterAll(() => {\n    console.error = originalError;\n  });\n\n  beforeEach(() => {\n    mockSignOut.mockReset();\n  });\n\n  it('calls clerk.signOutOne when clicked', async () => {\n    render(<SignOutButton />);\n    const btn = screen.getByText('Sign out');\n    userEvent.click(btn);\n    await waitFor(() => {\n      expect(mockSignOut).toHaveBeenCalled();\n    });\n  });\n\n  it('uses text passed as children', async () => {\n    render(<SignOutButton>text</SignOutButton>);\n    screen.getByText('text');\n  });\n\n  it('throws if multiple children provided', async () => {\n    expect(() => {\n      render(\n        <SignOutButton>\n          <button>1</button>\n          <button>2</button>\n        </SignOutButton>,\n      );\n    }).toThrow();\n  });\n});\n"], "mappings": "AAAA,SAAS,QAAQ,QAAQ,eAAe;AACxC,OAAO,eAAe;AACtB,OAAO,WAAW;AAElB,SAAS,qBAAqB;AAE9B,MAAM,cAAc,KAAK,GAAG;AAC5B,MAAM,gBAAgB,QAAQ;AAE9B,MAAM,YAAY;AAAA,EAChB,SAAS;AACX;AAEA,KAAK,KAAK,gBAAgB,MAAM;AAC9B,SAAO;AAAA,IACL,WAAW,CAAC,cAAmB,CAAC,UAAe;AAC7C,aACE;AAAA,QAAC;AAAA;AAAA,UACE,GAAG;AAAA,UACJ,OAAO;AAAA;AAAA,MACT;AAAA,IAEJ;AAAA,EACF;AACF,CAAC;AAED,SAAS,qBAAqB,MAAM;AAClC,YAAU,MAAM;AACd,YAAQ,QAAQ,KAAK,GAAG;AAAA,EAC1B,CAAC;AAED,WAAS,MAAM;AACb,YAAQ,QAAQ;AAAA,EAClB,CAAC;AAED,aAAW,MAAM;AACf,gBAAY,UAAU;AAAA,EACxB,CAAC;AAED,KAAG,uCAAuC,YAAY;AACpD,WAAO,oCAAC,mBAAc,CAAE;AACxB,UAAM,MAAM,OAAO,UAAU,UAAU;AACvC,cAAU,MAAM,GAAG;AACnB,UAAM,QAAQ,MAAM;AAClB,aAAO,WAAW,EAAE,iBAAiB;AAAA,IACvC,CAAC;AAAA,EACH,CAAC;AAED,KAAG,gCAAgC,YAAY;AAC7C,WAAO,oCAAC,qBAAc,MAAI,CAAgB;AAC1C,WAAO,UAAU,MAAM;AAAA,EACzB,CAAC;AAED,KAAG,wCAAwC,YAAY;AACrD,WAAO,MAAM;AACX;AAAA,QACE,oCAAC,qBACC,oCAAC,gBAAO,GAAC,GACT,oCAAC,gBAAO,GAAC,CACX;AAAA,MACF;AAAA,IACF,CAAC,EAAE,QAAQ;AAAA,EACb,CAAC;AACH,CAAC;", "names": []}