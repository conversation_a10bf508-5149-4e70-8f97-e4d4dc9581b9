{"version": 3, "sources": ["../../../src/components/SignUpButton.tsx"], "sourcesContent": ["import React from 'react';\n\nimport type { SignUpButtonProps, WithClerkProp } from '../types';\nimport { assertSingleChild, normalizeWithDefaultValue, safeExecute } from '../utils';\nimport { withClerk } from './withClerk';\n\nexport const SignUpButton = withClerk(({ clerk, children, ...props }: WithClerkProp<SignUpButtonProps>) => {\n  const { afterSignInUrl, afterSignUpUrl, redirectUrl, mode, unsafeMetadata, ...rest } = props;\n\n  children = normalizeWithDefaultValue(children, 'Sign up');\n  const child = assertSingleChild(children)('SignUpButton');\n\n  const clickHandler = () => {\n    const opts = { afterSignInUrl, afterSignUpUrl, redirectUrl, unsafeMetadata };\n\n    if (mode === 'modal') {\n      return clerk.openSignUp(opts);\n    }\n\n    return clerk.redirectToSignUp(opts);\n  };\n\n  const wrappedChildClickHandler: React.MouseEventHandler = async e => {\n    await safeExecute((child as any).props.onClick)(e);\n    return clickHandler();\n  };\n\n  const childProps = { ...rest, onClick: wrappedChildClickHandler };\n  return React.cloneElement(child as React.ReactElement<unknown>, childProps);\n}, 'SignUpButton');\n"], "mappings": ";AAAA,OAAO,WAAW;AAGlB,SAAS,mBAAmB,2BAA2B,mBAAmB;AAC1E,SAAS,iBAAiB;AAEnB,MAAM,eAAe,UAAU,CAAC,EAAE,OAAO,UAAU,GAAG,MAAM,MAAwC;AACzG,QAAM,EAAE,gBAAgB,gBAAgB,aAAa,MAAM,gBAAgB,GAAG,KAAK,IAAI;AAEvF,aAAW,0BAA0B,UAAU,SAAS;AACxD,QAAM,QAAQ,kBAAkB,QAAQ,EAAE,cAAc;AAExD,QAAM,eAAe,MAAM;AACzB,UAAM,OAAO,EAAE,gBAAgB,gBAAgB,aAAa,eAAe;AAE3E,QAAI,SAAS,SAAS;AACpB,aAAO,MAAM,WAAW,IAAI;AAAA,IAC9B;AAEA,WAAO,MAAM,iBAAiB,IAAI;AAAA,EACpC;AAEA,QAAM,2BAAoD,OAAM,MAAK;AACnE,UAAM,YAAa,MAAc,MAAM,OAAO,EAAE,CAAC;AACjD,WAAO,aAAa;AAAA,EACtB;AAEA,QAAM,aAAa,EAAE,GAAG,MAAM,SAAS,yBAAyB;AAChE,SAAO,MAAM,aAAa,OAAsC,UAAU;AAC5E,GAAG,cAAc;", "names": []}