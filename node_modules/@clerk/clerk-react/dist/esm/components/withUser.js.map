{"version": 3, "sources": ["../../../src/components/withUser.tsx"], "sourcesContent": ["import type { UserResource } from '@clerk/types';\nimport React from 'react';\n\nimport { useUserContext } from '../contexts/UserContext';\nimport { hocChildrenNotAFunctionError } from '../errors';\n\nexport const withUser = <P extends { user: UserResource }>(Component: React.ComponentType<P>, displayName?: string) => {\n  displayName = displayName || Component.displayName || Component.name || 'Component';\n  Component.displayName = displayName;\n  const HOC: React.FC<Omit<P, 'user'>> = (props: Omit<P, 'user'>) => {\n    const user = useUserContext();\n\n    if (!user) {\n      return null;\n    }\n\n    return (\n      <Component\n        {...(props as P)}\n        user={user}\n      />\n    );\n  };\n\n  HOC.displayName = `withUser(${displayName})`;\n  return HOC;\n};\n\nexport const WithUser: React.FC<{\n  children: (user: UserResource) => React.ReactNode;\n}> = ({ children }) => {\n  const user = useUserContext();\n\n  if (typeof children !== 'function') {\n    throw new Error(hocChildrenNotAFunctionError);\n  }\n\n  if (!user) {\n    return null;\n  }\n\n  return <>{children(user)}</>;\n};\n"], "mappings": ";AACA,OAAO,WAAW;AAElB,SAAS,sBAAsB;AAC/B,SAAS,oCAAoC;AAEtC,MAAM,WAAW,CAAmC,WAAmC,gBAAyB;AACrH,gBAAc,eAAe,UAAU,eAAe,UAAU,QAAQ;AACxE,YAAU,cAAc;AACxB,QAAM,MAAiC,CAAC,UAA2B;AACjE,UAAM,OAAO,eAAe;AAE5B,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,IACT;AAEA,WACE;AAAA,MAAC;AAAA;AAAA,QACE,GAAI;AAAA,QACL;AAAA;AAAA,IACF;AAAA,EAEJ;AAEA,MAAI,cAAc,YAAY,WAAW;AACzC,SAAO;AACT;AAEO,MAAM,WAER,CAAC,EAAE,SAAS,MAAM;AACrB,QAAM,OAAO,eAAe;AAE5B,MAAI,OAAO,aAAa,YAAY;AAClC,UAAM,IAAI,MAAM,4BAA4B;AAAA,EAC9C;AAEA,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AAEA,SAAO,0DAAG,SAAS,IAAI,CAAE;AAC3B;", "names": []}