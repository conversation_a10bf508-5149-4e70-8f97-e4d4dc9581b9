{"version": 3, "sources": ["../../../src/components/withSession.tsx"], "sourcesContent": ["import type { SessionResource } from '@clerk/types';\nimport React from 'react';\n\nimport { useSessionContext } from '../contexts/SessionContext';\nimport { hocChildrenNotAFunctionError } from '../errors';\n\nexport const withSession = <P extends { session: SessionResource }>(\n  Component: React.ComponentType<P>,\n  displayName?: string,\n) => {\n  displayName = displayName || Component.displayName || Component.name || 'Component';\n  Component.displayName = displayName;\n  const HOC: React.FC<Omit<P, 'session'>> = (props: Omit<P, 'session'>) => {\n    const session = useSessionContext();\n\n    if (!session) {\n      return null;\n    }\n\n    return (\n      <Component\n        {...(props as P)}\n        session={session}\n      />\n    );\n  };\n\n  HOC.displayName = `withSession(${displayName})`;\n  return HOC;\n};\n\nexport const WithSession: React.FC<{\n  children: (session: SessionResource) => React.ReactNode;\n}> = ({ children }) => {\n  const session = useSessionContext();\n\n  if (typeof children !== 'function') {\n    throw new Error(hocChildrenNotAFunctionError);\n  }\n\n  if (!session) {\n    return null;\n  }\n\n  return <>{children(session)}</>;\n};\n"], "mappings": ";AACA,OAAO,WAAW;AAElB,SAAS,yBAAyB;AAClC,SAAS,oCAAoC;AAEtC,MAAM,cAAc,CACzB,WACA,gBACG;AACH,gBAAc,eAAe,UAAU,eAAe,UAAU,QAAQ;AACxE,YAAU,cAAc;AACxB,QAAM,MAAoC,CAAC,UAA8B;AACvE,UAAM,UAAU,kBAAkB;AAElC,QAAI,CAAC,SAAS;AACZ,aAAO;AAAA,IACT;AAEA,WACE;AAAA,MAAC;AAAA;AAAA,QACE,GAAI;AAAA,QACL;AAAA;AAAA,IACF;AAAA,EAEJ;AAEA,MAAI,cAAc,eAAe,WAAW;AAC5C,SAAO;AACT;AAEO,MAAM,cAER,CAAC,EAAE,SAAS,MAAM;AACrB,QAAM,UAAU,kBAAkB;AAElC,MAAI,OAAO,aAAa,YAAY;AAClC,UAAM,IAAI,MAAM,4BAA4B;AAAA,EAC9C;AAEA,MAAI,CAAC,SAAS;AACZ,WAAO;AAAA,EACT;AAEA,SAAO,0DAAG,SAAS,OAAO,CAAE;AAC9B;", "names": []}