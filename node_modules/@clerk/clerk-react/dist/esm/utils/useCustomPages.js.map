{"version": 3, "sources": ["../../../src/utils/useCustomPages.tsx"], "sourcesContent": ["import { logErrorInDevMode } from '@clerk/shared';\nimport type { CustomPage } from '@clerk/types';\nimport type { ReactElement } from 'react';\nimport React from 'react';\n\nimport {\n  OrganizationProfileLink,\n  OrganizationProfilePage,\n  UserProfileLink,\n  UserProfilePage,\n} from '../components/uiComponents';\nimport { customLinkWrongProps, customPagesIgnoredComponent, customPageWrongProps } from '../errors';\nimport type { UserProfilePageProps } from '../types';\nimport type { UseCustomElementPortalParams, UseCustomElementPortalReturn } from './useCustomElementPortal';\nimport { useCustomElementPortal } from './useCustomElementPortal';\n\nconst isThatComponent = (v: any, component: React.ReactNode): v is React.ReactNode => {\n  return !!v && React.isValidElement(v) && (v as React.ReactElement)?.type === component;\n};\n\nexport const useUserProfileCustomPages = (children: React.ReactNode | React.ReactNode[]) => {\n  const reorderItemsLabels = ['account', 'security'];\n  return useCustomPages({\n    children,\n    reorderItemsLabels,\n    LinkComponent: UserProfileLink,\n    PageComponent: UserProfilePage,\n    componentName: 'UserProfile',\n  });\n};\n\nexport const useOrganizationProfileCustomPages = (children: React.ReactNode | React.ReactNode[]) => {\n  const reorderItemsLabels = ['members', 'settings'];\n  return useCustomPages({\n    children,\n    reorderItemsLabels,\n    LinkComponent: OrganizationProfileLink,\n    PageComponent: OrganizationProfilePage,\n    componentName: 'OrganizationProfile',\n  });\n};\n\ntype UseCustomPagesParams = {\n  children: React.ReactNode | React.ReactNode[];\n  LinkComponent: any;\n  PageComponent: any;\n  reorderItemsLabels: string[];\n  componentName: string;\n};\n\ntype CustomPageWithIdType = UserProfilePageProps & { children?: React.ReactNode };\n\nconst useCustomPages = ({\n  children,\n  LinkComponent,\n  PageComponent,\n  reorderItemsLabels,\n  componentName,\n}: UseCustomPagesParams) => {\n  const validChildren: CustomPageWithIdType[] = [];\n\n  React.Children.forEach(children, child => {\n    if (!isThatComponent(child, PageComponent) && !isThatComponent(child, LinkComponent)) {\n      if (child) {\n        logErrorInDevMode(customPagesIgnoredComponent(componentName));\n      }\n      return;\n    }\n\n    const { props } = child as ReactElement;\n\n    const { children, label, url, labelIcon } = props;\n\n    if (isThatComponent(child, PageComponent)) {\n      if (isReorderItem(props, reorderItemsLabels)) {\n        // This is a reordering item\n        validChildren.push({ label });\n      } else if (isCustomPage(props)) {\n        // this is a custom page\n        validChildren.push({ label, labelIcon, children, url });\n      } else {\n        logErrorInDevMode(customPageWrongProps(componentName));\n        return;\n      }\n    }\n\n    if (isThatComponent(child, LinkComponent)) {\n      if (isExternalLink(props)) {\n        // This is an external link\n        validChildren.push({ label, labelIcon, url });\n      } else {\n        logErrorInDevMode(customLinkWrongProps(componentName));\n        return;\n      }\n    }\n  });\n\n  const customPageContents: UseCustomElementPortalParams[] = [];\n  const customPageLabelIcons: UseCustomElementPortalParams[] = [];\n  const customLinkLabelIcons: UseCustomElementPortalParams[] = [];\n\n  validChildren.forEach((cp, index) => {\n    if (isCustomPage(cp)) {\n      customPageContents.push({ component: cp.children, id: index });\n      customPageLabelIcons.push({ component: cp.labelIcon, id: index });\n      return;\n    }\n    if (isExternalLink(cp)) {\n      customLinkLabelIcons.push({ component: cp.labelIcon, id: index });\n    }\n  });\n\n  const customPageContentsPortals = useCustomElementPortal(customPageContents);\n  const customPageLabelIconsPortals = useCustomElementPortal(customPageLabelIcons);\n  const customLinkLabelIconsPortals = useCustomElementPortal(customLinkLabelIcons);\n\n  const customPages: CustomPage[] = [];\n  const customPagesPortals: React.ComponentType[] = [];\n\n  validChildren.forEach((cp, index) => {\n    if (isReorderItem(cp, reorderItemsLabels)) {\n      customPages.push({ label: cp.label });\n      return;\n    }\n    if (isCustomPage(cp)) {\n      const {\n        portal: contentPortal,\n        mount,\n        unmount,\n      } = customPageContentsPortals.find(p => p.id === index) as UseCustomElementPortalReturn;\n      const {\n        portal: labelPortal,\n        mount: mountIcon,\n        unmount: unmountIcon,\n      } = customPageLabelIconsPortals.find(p => p.id === index) as UseCustomElementPortalReturn;\n      customPages.push({ label: cp.label, url: cp.url, mount, unmount, mountIcon, unmountIcon });\n      customPagesPortals.push(contentPortal);\n      customPagesPortals.push(labelPortal);\n      return;\n    }\n    if (isExternalLink(cp)) {\n      const {\n        portal: labelPortal,\n        mount: mountIcon,\n        unmount: unmountIcon,\n      } = customLinkLabelIconsPortals.find(p => p.id === index) as UseCustomElementPortalReturn;\n      customPages.push({ label: cp.label, url: cp.url, mountIcon, unmountIcon });\n      customPagesPortals.push(labelPortal);\n      return;\n    }\n  });\n\n  return { customPages, customPagesPortals };\n};\n\nconst isReorderItem = (childProps: any, validItems: string[]): boolean => {\n  const { children, label, url, labelIcon } = childProps;\n  return !children && !url && !labelIcon && validItems.some(v => v === label);\n};\n\nconst isCustomPage = (childProps: any): boolean => {\n  const { children, label, url, labelIcon } = childProps;\n  return !!children && !!url && !!labelIcon && !!label;\n};\n\nconst isExternalLink = (childProps: any): boolean => {\n  const { children, label, url, labelIcon } = childProps;\n  return !children && !!url && !!labelIcon && !!label;\n};\n"], "mappings": ";AAAA,SAAS,yBAAyB;AAGlC,OAAO,WAAW;AAElB;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP,SAAS,sBAAsB,6BAA6B,4BAA4B;AAGxF,SAAS,8BAA8B;AAEvC,MAAM,kBAAkB,CAAC,GAAQ,cAAqD;AACpF,SAAO,CAAC,CAAC,KAAK,MAAM,eAAe,CAAC,MAAM,uBAA0B,UAAS;AAC/E;AAEO,MAAM,4BAA4B,CAAC,aAAkD;AAC1F,QAAM,qBAAqB,CAAC,WAAW,UAAU;AACjD,SAAO,eAAe;AAAA,IACpB;AAAA,IACA;AAAA,IACA,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,EACjB,CAAC;AACH;AAEO,MAAM,oCAAoC,CAAC,aAAkD;AAClG,QAAM,qBAAqB,CAAC,WAAW,UAAU;AACjD,SAAO,eAAe;AAAA,IACpB;AAAA,IACA;AAAA,IACA,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,EACjB,CAAC;AACH;AAYA,MAAM,iBAAiB,CAAC;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAA4B;AAC1B,QAAM,gBAAwC,CAAC;AAE/C,QAAM,SAAS,QAAQ,UAAU,WAAS;AACxC,QAAI,CAAC,gBAAgB,OAAO,aAAa,KAAK,CAAC,gBAAgB,OAAO,aAAa,GAAG;AACpF,UAAI,OAAO;AACT,0BAAkB,4BAA4B,aAAa,CAAC;AAAA,MAC9D;AACA;AAAA,IACF;AAEA,UAAM,EAAE,MAAM,IAAI;AAElB,UAAM,EAAE,UAAAA,WAAU,OAAO,KAAK,UAAU,IAAI;AAE5C,QAAI,gBAAgB,OAAO,aAAa,GAAG;AACzC,UAAI,cAAc,OAAO,kBAAkB,GAAG;AAE5C,sBAAc,KAAK,EAAE,MAAM,CAAC;AAAA,MAC9B,WAAW,aAAa,KAAK,GAAG;AAE9B,sBAAc,KAAK,EAAE,OAAO,WAAW,UAAAA,WAAU,IAAI,CAAC;AAAA,MACxD,OAAO;AACL,0BAAkB,qBAAqB,aAAa,CAAC;AACrD;AAAA,MACF;AAAA,IACF;AAEA,QAAI,gBAAgB,OAAO,aAAa,GAAG;AACzC,UAAI,eAAe,KAAK,GAAG;AAEzB,sBAAc,KAAK,EAAE,OAAO,WAAW,IAAI,CAAC;AAAA,MAC9C,OAAO;AACL,0BAAkB,qBAAqB,aAAa,CAAC;AACrD;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AAED,QAAM,qBAAqD,CAAC;AAC5D,QAAM,uBAAuD,CAAC;AAC9D,QAAM,uBAAuD,CAAC;AAE9D,gBAAc,QAAQ,CAAC,IAAI,UAAU;AACnC,QAAI,aAAa,EAAE,GAAG;AACpB,yBAAmB,KAAK,EAAE,WAAW,GAAG,UAAU,IAAI,MAAM,CAAC;AAC7D,2BAAqB,KAAK,EAAE,WAAW,GAAG,WAAW,IAAI,MAAM,CAAC;AAChE;AAAA,IACF;AACA,QAAI,eAAe,EAAE,GAAG;AACtB,2BAAqB,KAAK,EAAE,WAAW,GAAG,WAAW,IAAI,MAAM,CAAC;AAAA,IAClE;AAAA,EACF,CAAC;AAED,QAAM,4BAA4B,uBAAuB,kBAAkB;AAC3E,QAAM,8BAA8B,uBAAuB,oBAAoB;AAC/E,QAAM,8BAA8B,uBAAuB,oBAAoB;AAE/E,QAAM,cAA4B,CAAC;AACnC,QAAM,qBAA4C,CAAC;AAEnD,gBAAc,QAAQ,CAAC,IAAI,UAAU;AACnC,QAAI,cAAc,IAAI,kBAAkB,GAAG;AACzC,kBAAY,KAAK,EAAE,OAAO,GAAG,MAAM,CAAC;AACpC;AAAA,IACF;AACA,QAAI,aAAa,EAAE,GAAG;AACpB,YAAM;AAAA,QACJ,QAAQ;AAAA,QACR;AAAA,QACA;AAAA,MACF,IAAI,0BAA0B,KAAK,OAAK,EAAE,OAAO,KAAK;AACtD,YAAM;AAAA,QACJ,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,SAAS;AAAA,MACX,IAAI,4BAA4B,KAAK,OAAK,EAAE,OAAO,KAAK;AACxD,kBAAY,KAAK,EAAE,OAAO,GAAG,OAAO,KAAK,GAAG,KAAK,OAAO,SAAS,WAAW,YAAY,CAAC;AACzF,yBAAmB,KAAK,aAAa;AACrC,yBAAmB,KAAK,WAAW;AACnC;AAAA,IACF;AACA,QAAI,eAAe,EAAE,GAAG;AACtB,YAAM;AAAA,QACJ,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,SAAS;AAAA,MACX,IAAI,4BAA4B,KAAK,OAAK,EAAE,OAAO,KAAK;AACxD,kBAAY,KAAK,EAAE,OAAO,GAAG,OAAO,KAAK,GAAG,KAAK,WAAW,YAAY,CAAC;AACzE,yBAAmB,KAAK,WAAW;AACnC;AAAA,IACF;AAAA,EACF,CAAC;AAED,SAAO,EAAE,aAAa,mBAAmB;AAC3C;AAEA,MAAM,gBAAgB,CAAC,YAAiB,eAAkC;AACxE,QAAM,EAAE,UAAU,OAAO,KAAK,UAAU,IAAI;AAC5C,SAAO,CAAC,YAAY,CAAC,OAAO,CAAC,aAAa,WAAW,KAAK,OAAK,MAAM,KAAK;AAC5E;AAEA,MAAM,eAAe,CAAC,eAA6B;AACjD,QAAM,EAAE,UAAU,OAAO,KAAK,UAAU,IAAI;AAC5C,SAAO,CAAC,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,CAAC;AACjD;AAEA,MAAM,iBAAiB,CAAC,eAA6B;AACnD,QAAM,EAAE,UAAU,OAAO,KAAK,UAAU,IAAI;AAC5C,SAAO,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,CAAC;AAChD;", "names": ["children"]}