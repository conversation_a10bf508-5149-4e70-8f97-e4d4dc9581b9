{"version": 3, "sources": ["../../../../src/utils/__tests__/versionSelector.test.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/ban-ts-comment */\nimport { versionSelector } from '../versionSelector';\n\ndescribe('versionSelector', () => {\n  it('should return the clerkJSVersion if it is provided', () => {\n    expect(versionSelector('1.0.0')).toEqual('1.0.0');\n  });\n  it('should use the major version if there is no prerelease tag', () => {\n    // @ts-ignore\n    global.PACKAGE_VERSION = '1.0.0';\n    // @ts-ignore\n    global.JS_PACKAGE_VERSION = '2.0.0';\n\n    expect(versionSelector(undefined)).toEqual('1');\n  });\n  it('should use the prerelease tag when it is not snapshot', () => {\n    // @ts-ignore\n    global.PACKAGE_VERSION = '1.0.0-next.0';\n    // @ts-ignore\n    global.JS_PACKAGE_VERSION = '2.0.0-next.0';\n\n    expect(versionSelector(undefined)).toEqual('next');\n  });\n  it('should use the exact JS version if tag is snapshot', () => {\n    // @ts-ignore\n    global.PACKAGE_VERSION = '1.0.0-snapshot.0';\n    // @ts-ignore\n    global.JS_PACKAGE_VERSION = '2.0.0-snapshot.0';\n\n    expect(versionSelector(undefined)).toEqual('2.0.0-snapshot.0');\n  });\n});\n"], "mappings": "AACA,SAAS,uBAAuB;AAEhC,SAAS,mBAAmB,MAAM;AAChC,KAAG,sDAAsD,MAAM;AAC7D,WAAO,gBAAgB,OAAO,CAAC,EAAE,QAAQ,OAAO;AAAA,EAClD,CAAC;AACD,KAAG,8DAA8D,MAAM;AAErE,WAAO,kBAAkB;AAEzB,WAAO,qBAAqB;AAE5B,WAAO,gBAAgB,MAAS,CAAC,EAAE,QAAQ,GAAG;AAAA,EAChD,CAAC;AACD,KAAG,yDAAyD,MAAM;AAEhE,WAAO,kBAAkB;AAEzB,WAAO,qBAAqB;AAE5B,WAAO,gBAAgB,MAAS,CAAC,EAAE,QAAQ,MAAM;AAAA,EACnD,CAAC;AACD,KAAG,sDAAsD,MAAM;AAE7D,WAAO,kBAAkB;AAEzB,WAAO,qBAAqB;AAE5B,WAAO,gBAAgB,MAAS,CAAC,EAAE,QAAQ,kBAAkB;AAAA,EAC/D,CAAC;AACH,CAAC;", "names": []}