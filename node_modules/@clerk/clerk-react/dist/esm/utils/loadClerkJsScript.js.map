{"version": 3, "sources": ["../../../src/utils/loadClerkJsScript.ts"], "sourcesContent": ["import { parsePublishable<PERSON>ey } from '@clerk/shared/keys';\nimport { loadScript } from '@clerk/shared/loadScript';\nimport { isValidProxyUrl, proxyUrlToAbsoluteURL } from '@clerk/shared/proxy';\nimport { addClerkPrefix } from '@clerk/shared/url';\n\nimport type { IsomorphicClerkOptions } from '../types';\nimport { errorThrower } from './errorThrower';\nimport { isDevOrStagingUrl } from './isDevOrStageUrl';\nimport { versionSelector } from './versionSelector';\n\nconst FAILED_TO_LOAD_ERROR = 'Clerk: Failed to load Clerk';\n\ntype LoadClerkJsScriptOptions = Omit<IsomorphicClerkOptions, 'proxyUrl' | 'domain'> & {\n  proxyUrl: string;\n  domain: string;\n};\n\nexport const loadClerkJsScript = (opts: LoadClerkJsScriptOptions) => {\n  const { frontendApi, publishableKey } = opts;\n\n  if (!publishableKey && !frontendApi) {\n    errorThrower.throwMissingPublishableKeyError();\n  }\n\n  return loadScript(clerkJsScriptUrl(opts), {\n    async: true,\n    crossOrigin: 'anonymous',\n    beforeLoad: applyClerkJsScriptAttributes(opts),\n  }).catch(() => {\n    throw new Error(FAILED_TO_LOAD_ERROR);\n  });\n};\n\nconst clerkJsScriptUrl = (opts: LoadClerkJsScriptOptions) => {\n  const { clerkJSUrl, clerkJSVariant, clerkJSVersion, proxyUrl, domain, publishableKey, frontendApi } = opts;\n\n  if (clerkJSUrl) {\n    return clerkJSUrl;\n  }\n\n  let scriptHost = '';\n  if (!!proxyUrl && isValidProxyUrl(proxyUrl)) {\n    scriptHost = proxyUrlToAbsoluteURL(proxyUrl).replace(/http(s)?:\\/\\//, '');\n  } else if (domain && !isDevOrStagingUrl(parsePublishableKey(publishableKey)?.frontendApi || frontendApi || '')) {\n    scriptHost = addClerkPrefix(domain);\n  } else {\n    scriptHost = parsePublishableKey(publishableKey)?.frontendApi || frontendApi || '';\n  }\n\n  const variant = clerkJSVariant ? `${clerkJSVariant.replace(/\\.+$/, '')}.` : '';\n  const version = versionSelector(clerkJSVersion);\n  return `https://${scriptHost}/npm/@clerk/clerk-js@${version}/dist/clerk.${variant}browser.js`;\n};\n\nconst applyClerkJsScriptAttributes = (options: LoadClerkJsScriptOptions) => (script: HTMLScriptElement) => {\n  const { publishableKey, frontendApi, proxyUrl, domain } = options;\n  if (publishableKey) {\n    script.setAttribute('data-clerk-publishable-key', publishableKey);\n  } else if (frontendApi) {\n    script.setAttribute('data-clerk-frontend-api', frontendApi);\n  }\n\n  if (proxyUrl) {\n    script.setAttribute('data-clerk-proxy-url', proxyUrl);\n  }\n\n  if (domain) {\n    script.setAttribute('data-clerk-domain', domain);\n  }\n};\n"], "mappings": ";AAAA,SAAS,2BAA2B;AACpC,SAAS,kBAAkB;AAC3B,SAAS,iBAAiB,6BAA6B;AACvD,SAAS,sBAAsB;AAG/B,SAAS,oBAAoB;AAC7B,SAAS,yBAAyB;AAClC,SAAS,uBAAuB;AAEhC,MAAM,uBAAuB;AAOtB,MAAM,oBAAoB,CAAC,SAAmC;AACnE,QAAM,EAAE,aAAa,eAAe,IAAI;AAExC,MAAI,CAAC,kBAAkB,CAAC,aAAa;AACnC,iBAAa,gCAAgC;AAAA,EAC/C;AAEA,SAAO,WAAW,iBAAiB,IAAI,GAAG;AAAA,IACxC,OAAO;AAAA,IACP,aAAa;AAAA,IACb,YAAY,6BAA6B,IAAI;AAAA,EAC/C,CAAC,EAAE,MAAM,MAAM;AACb,UAAM,IAAI,MAAM,oBAAoB;AAAA,EACtC,CAAC;AACH;AAEA,MAAM,mBAAmB,CAAC,SAAmC;AAjC7D;AAkCE,QAAM,EAAE,YAAY,gBAAgB,gBAAgB,UAAU,QAAQ,gBAAgB,YAAY,IAAI;AAEtG,MAAI,YAAY;AACd,WAAO;AAAA,EACT;AAEA,MAAI,aAAa;AACjB,MAAI,CAAC,CAAC,YAAY,gBAAgB,QAAQ,GAAG;AAC3C,iBAAa,sBAAsB,QAAQ,EAAE,QAAQ,iBAAiB,EAAE;AAAA,EAC1E,WAAW,UAAU,CAAC,oBAAkB,yBAAoB,cAAc,MAAlC,mBAAqC,gBAAe,eAAe,EAAE,GAAG;AAC9G,iBAAa,eAAe,MAAM;AAAA,EACpC,OAAO;AACL,mBAAa,yBAAoB,cAAc,MAAlC,mBAAqC,gBAAe,eAAe;AAAA,EAClF;AAEA,QAAM,UAAU,iBAAiB,GAAG,eAAe,QAAQ,QAAQ,EAAE,CAAC,MAAM;AAC5E,QAAM,UAAU,gBAAgB,cAAc;AAC9C,SAAO,WAAW,UAAU,wBAAwB,OAAO,eAAe,OAAO;AACnF;AAEA,MAAM,+BAA+B,CAAC,YAAsC,CAAC,WAA8B;AACzG,QAAM,EAAE,gBAAgB,aAAa,UAAU,OAAO,IAAI;AAC1D,MAAI,gBAAgB;AAClB,WAAO,aAAa,8BAA8B,cAAc;AAAA,EAClE,WAAW,aAAa;AACtB,WAAO,aAAa,2BAA2B,WAAW;AAAA,EAC5D;AAEA,MAAI,UAAU;AACZ,WAAO,aAAa,wBAAwB,QAAQ;AAAA,EACtD;AAEA,MAAI,QAAQ;AACV,WAAO,aAAa,qBAAqB,MAAM;AAAA,EACjD;AACF;", "names": []}