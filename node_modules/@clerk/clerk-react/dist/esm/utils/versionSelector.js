import "../chunk-XTU7I5IS.js";
const versionSelector = (clerkJSVersion) => {
  if (clerkJSVersion) {
    return clerkJSVersion;
  }
  const prereleaseTag = getPrereleaseTag("4.32.5");
  if (prereleaseTag) {
    if (prereleaseTag === "snapshot") {
      return "4.73.14";
    }
    return prereleaseTag;
  }
  return getMajorVersion("4.32.5");
};
const getPrereleaseTag = (packageVersion) => {
  var _a;
  return (_a = packageVersion.match(/-(.*)\./)) == null ? void 0 : _a[1];
};
const getMajorVersion = (packageVersion) => packageVersion.split(".")[0];
export {
  versionSelector
};
//# sourceMappingURL=versionSelector.js.map