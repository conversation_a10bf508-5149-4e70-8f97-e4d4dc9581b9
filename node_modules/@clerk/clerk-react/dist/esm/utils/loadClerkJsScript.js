import "../chunk-XTU7I5IS.js";
import { parsePublishableKey } from "@clerk/shared/keys";
import { loadScript } from "@clerk/shared/loadScript";
import { isValidProxyUrl, proxyUrlToAbsoluteURL } from "@clerk/shared/proxy";
import { addClerkPrefix } from "@clerk/shared/url";
import { errorThrower } from "./errorThrower";
import { isDevOrStagingUrl } from "./isDevOrStageUrl";
import { versionSelector } from "./versionSelector";
const FAILED_TO_LOAD_ERROR = "Clerk: Failed to load Clerk";
const loadClerkJsScript = (opts) => {
  const { frontendApi, publishableKey } = opts;
  if (!publishableKey && !frontendApi) {
    errorThrower.throwMissingPublishableKeyError();
  }
  return loadScript(clerkJsScriptUrl(opts), {
    async: true,
    crossOrigin: "anonymous",
    beforeLoad: applyClerkJsScriptAttributes(opts)
  }).catch(() => {
    throw new Error(FAILED_TO_LOAD_ERROR);
  });
};
const clerkJsScriptUrl = (opts) => {
  var _a, _b;
  const { clerkJSUrl, clerkJSVariant, clerkJSVersion, proxyUrl, domain, publishableKey, frontendApi } = opts;
  if (clerkJSUrl) {
    return clerkJSUrl;
  }
  let scriptHost = "";
  if (!!proxyUrl && isValidProxyUrl(proxyUrl)) {
    scriptHost = proxyUrlToAbsoluteURL(proxyUrl).replace(/http(s)?:\/\//, "");
  } else if (domain && !isDevOrStagingUrl(((_a = parsePublishableKey(publishableKey)) == null ? void 0 : _a.frontendApi) || frontendApi || "")) {
    scriptHost = addClerkPrefix(domain);
  } else {
    scriptHost = ((_b = parsePublishableKey(publishableKey)) == null ? void 0 : _b.frontendApi) || frontendApi || "";
  }
  const variant = clerkJSVariant ? `${clerkJSVariant.replace(/\.+$/, "")}.` : "";
  const version = versionSelector(clerkJSVersion);
  return `https://${scriptHost}/npm/@clerk/clerk-js@${version}/dist/clerk.${variant}browser.js`;
};
const applyClerkJsScriptAttributes = (options) => (script) => {
  const { publishableKey, frontendApi, proxyUrl, domain } = options;
  if (publishableKey) {
    script.setAttribute("data-clerk-publishable-key", publishableKey);
  } else if (frontendApi) {
    script.setAttribute("data-clerk-frontend-api", frontendApi);
  }
  if (proxyUrl) {
    script.setAttribute("data-clerk-proxy-url", proxyUrl);
  }
  if (domain) {
    script.setAttribute("data-clerk-domain", domain);
  }
};
export {
  loadClerkJsScript
};
//# sourceMappingURL=loadClerkJsScript.js.map