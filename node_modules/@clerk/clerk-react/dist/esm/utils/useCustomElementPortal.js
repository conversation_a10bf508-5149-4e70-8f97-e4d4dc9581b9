import "../chunk-XTU7I5IS.js";
import React, { useState } from "react";
import { createPortal } from "react-dom";
const useCustomElementPortal = (elements) => {
  const initialState = Array(elements.length).fill(null);
  const [nodes, setNodes] = useState(initialState);
  return elements.map((el, index) => ({
    id: el.id,
    mount: (node) => setNodes((prevState) => prevState.map((n, i) => i === index ? node : n)),
    unmount: () => setNodes((prevState) => prevState.map((n, i) => i === index ? null : n)),
    portal: () => /* @__PURE__ */ React.createElement(React.Fragment, null, nodes[index] ? createPortal(el.component, nodes[index]) : null)
  }));
};
export {
  useCustomElementPortal
};
//# sourceMappingURL=useCustomElementPortal.js.map