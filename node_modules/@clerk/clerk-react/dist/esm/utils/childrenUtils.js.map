{"version": 3, "sources": ["../../../src/utils/childrenUtils.tsx"], "sourcesContent": ["import React from 'react';\n\nimport { multipleChildrenInButtonComponent } from '../errors';\n\nexport const assertSingleChild =\n  (children: React.ReactNode) =>\n  (name: 'SignInButton' | 'SignUpButton' | 'SignOutButton' | 'SignInWithMetamaskButton') => {\n    try {\n      return React.Children.only(children);\n    } catch (e) {\n      throw new Error(multipleChildrenInButtonComponent(name));\n    }\n  };\n\nexport const normalizeWithDefaultValue = (children: React.ReactNode | undefined, defaultText: string) => {\n  if (!children) {\n    children = defaultText;\n  }\n  if (typeof children === 'string') {\n    children = <button>{children}</button>;\n  }\n  return children;\n};\n\nexport const safeExecute =\n  (cb: unknown) =>\n  (...args: any) => {\n    if (cb && typeof cb === 'function') {\n      return cb(...args);\n    }\n  };\n"], "mappings": ";AAAA,OAAO,WAAW;AAElB,SAAS,yCAAyC;AAE3C,MAAM,oBACX,CAAC,aACD,CAAC,SAAyF;AACxF,MAAI;AACF,WAAO,MAAM,SAAS,KAAK,QAAQ;AAAA,EACrC,SAAS,GAAG;AACV,UAAM,IAAI,MAAM,kCAAkC,IAAI,CAAC;AAAA,EACzD;AACF;AAEK,MAAM,4BAA4B,CAAC,UAAuC,gBAAwB;AACvG,MAAI,CAAC,UAAU;AACb,eAAW;AAAA,EACb;AACA,MAAI,OAAO,aAAa,UAAU;AAChC,eAAW,oCAAC,gBAAQ,QAAS;AAAA,EAC/B;AACA,SAAO;AACT;AAEO,MAAM,cACX,CAAC,OACD,IAAI,SAAc;AAChB,MAAI,MAAM,OAAO,OAAO,YAAY;AAClC,WAAO,GAAG,GAAG,IAAI;AAAA,EACnB;AACF;", "names": []}