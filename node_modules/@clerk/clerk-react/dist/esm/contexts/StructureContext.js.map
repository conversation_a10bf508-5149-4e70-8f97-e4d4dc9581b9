{"version": 3, "sources": ["../../../src/contexts/StructureContext.tsx"], "sourcesContent": ["import React from 'react';\n\nimport { assertWrappedByClerkProvider } from './assertHelpers';\n\nexport interface StructureContextValue {\n  guaranteedLoaded: boolean;\n}\n\nexport const StructureContextStates = Object.freeze({\n  noGuarantees: Object.freeze({\n    guaranteedLoaded: false,\n  }),\n  guaranteedLoaded: Object.freeze({\n    guaranteedLoaded: true,\n  }),\n});\n\nexport const StructureContext = React.createContext<StructureContextValue | undefined>(undefined);\n\nStructureContext.displayName = 'StructureContext';\n\nconst useStructureContext = (): StructureContextValue => {\n  const structureCtx = React.useContext(StructureContext);\n  assertWrappedByClerkProvider(structureCtx);\n  return structureCtx;\n};\n\nexport const LoadedGuarantee: React.FC<React.PropsWithChildren<unknown>> = ({ children }) => {\n  const structure = useStructureContext();\n  if (structure.guaranteedLoaded) {\n    return <>{children}</>;\n  }\n  return (\n    <StructureContext.Provider value={StructureContextStates.guaranteedLoaded}>{children}</StructureContext.Provider>\n  );\n};\n"], "mappings": ";AAAA,OAAO,WAAW;AAElB,SAAS,oCAAoC;AAMtC,MAAM,yBAAyB,OAAO,OAAO;AAAA,EAClD,cAAc,OAAO,OAAO;AAAA,IAC1B,kBAAkB;AAAA,EACpB,CAAC;AAAA,EACD,kBAAkB,OAAO,OAAO;AAAA,IAC9B,kBAAkB;AAAA,EACpB,CAAC;AACH,CAAC;AAEM,MAAM,mBAAmB,MAAM,cAAiD,MAAS;AAEhG,iBAAiB,cAAc;AAE/B,MAAM,sBAAsB,MAA6B;AACvD,QAAM,eAAe,MAAM,WAAW,gBAAgB;AACtD,+BAA6B,YAAY;AACzC,SAAO;AACT;AAEO,MAAM,kBAA8D,CAAC,EAAE,SAAS,MAAM;AAC3F,QAAM,YAAY,oBAAoB;AACtC,MAAI,UAAU,kBAAkB;AAC9B,WAAO,0DAAG,QAAS;AAAA,EACrB;AACA,SACE,oCAAC,iBAAiB,UAAjB,EAA0B,OAAO,uBAAuB,oBAAmB,QAAS;AAEzF;", "names": []}