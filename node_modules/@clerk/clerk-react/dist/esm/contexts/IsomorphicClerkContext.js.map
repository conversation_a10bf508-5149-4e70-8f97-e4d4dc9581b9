{"version": 3, "sources": ["../../../src/contexts/IsomorphicClerkContext.tsx"], "sourcesContent": ["import { ClerkInstanceContext, useClerkInstanceContext } from '@clerk/shared/react';\n\nexport const [IsomorphicClerkContext, useIsomorphicClerkContext] = [ClerkInstanceContext, useClerkInstanceContext];\n"], "mappings": ";AAAA,SAAS,sBAAsB,+BAA+B;AAEvD,MAAM,CAAC,wBAAwB,yBAAyB,IAAI,CAAC,sBAAsB,uBAAuB;", "names": []}