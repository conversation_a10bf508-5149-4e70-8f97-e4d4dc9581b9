{"version": 3, "sources": ["../../../src/contexts/ClerkProvider.tsx"], "sourcesContent": ["import { isLegacyFrontendApiKey, isPublishableKey } from '@clerk/shared/keys';\nimport type { InitialState } from '@clerk/types';\nimport React from 'react';\n\nimport { multipleClerkProvidersError } from '../errors';\nimport type { IsomorphicClerkOptions } from '../types';\nimport { __internal__setErrorThrowerOptions, errorThrower, withMaxAllowedInstancesGuard } from '../utils';\nimport { ClerkContextProvider } from './ClerkContextProvider';\nimport { StructureContext, StructureContextStates } from './StructureContext';\n\n__internal__setErrorThrowerOptions({\n  packageName: '@clerk/clerk-react',\n});\n\nexport type ClerkProviderProps = IsomorphicClerkOptions & {\n  children: React.ReactNode;\n  initialState?: InitialState;\n};\n\nfunction ClerkProviderBase(props: ClerkProviderProps): JSX.Element {\n  const { initialState, children, ...restIsomorphicClerkOptions } = props;\n  const { frontendApi = '', publishableKey = '', Clerk: userInitialisedClerk } = restIsomorphicClerkOptions;\n\n  if (!userInitialisedClerk) {\n    if (!publishableKey && !frontendApi) {\n      errorThrower.throwMissingPublishableKeyError();\n    } else if (publishableKey && !isPublishableKey(publishableKey)) {\n      errorThrower.throwInvalidPublishableKeyError({ key: publishableKey });\n    } else if (!publishableKey && frontendApi && !isLegacyFrontendApiKey(frontendApi)) {\n      errorThrower.throwInvalidFrontendApiError({ key: frontendApi });\n    }\n  }\n\n  return (\n    <StructureContext.Provider value={StructureContextStates.noGuarantees}>\n      <ClerkContextProvider\n        initialState={initialState}\n        isomorphicClerkOptions={restIsomorphicClerkOptions}\n      >\n        {children}\n      </ClerkContextProvider>\n    </StructureContext.Provider>\n  );\n}\n\nconst ClerkProvider = withMaxAllowedInstancesGuard(ClerkProviderBase, 'ClerkProvider', multipleClerkProvidersError);\n\nClerkProvider.displayName = 'ClerkProvider';\n\nexport { ClerkProvider, __internal__setErrorThrowerOptions };\n"], "mappings": ";AAAA,SAAS,wBAAwB,wBAAwB;AAEzD,OAAO,WAAW;AAElB,SAAS,mCAAmC;AAE5C,SAAS,oCAAoC,cAAc,oCAAoC;AAC/F,SAAS,4BAA4B;AACrC,SAAS,kBAAkB,8BAA8B;AAEzD,mCAAmC;AAAA,EACjC,aAAa;AACf,CAAC;AAOD,SAAS,kBAAkB,OAAwC;AACjE,QAAM,EAAE,cAAc,UAAU,GAAG,2BAA2B,IAAI;AAClE,QAAM,EAAE,cAAc,IAAI,iBAAiB,IAAI,OAAO,qBAAqB,IAAI;AAE/E,MAAI,CAAC,sBAAsB;AACzB,QAAI,CAAC,kBAAkB,CAAC,aAAa;AACnC,mBAAa,gCAAgC;AAAA,IAC/C,WAAW,kBAAkB,CAAC,iBAAiB,cAAc,GAAG;AAC9D,mBAAa,gCAAgC,EAAE,KAAK,eAAe,CAAC;AAAA,IACtE,WAAW,CAAC,kBAAkB,eAAe,CAAC,uBAAuB,WAAW,GAAG;AACjF,mBAAa,6BAA6B,EAAE,KAAK,YAAY,CAAC;AAAA,IAChE;AAAA,EACF;AAEA,SACE,oCAAC,iBAAiB,UAAjB,EAA0B,OAAO,uBAAuB,gBACvD;AAAA,IAAC;AAAA;AAAA,MACC;AAAA,MACA,wBAAwB;AAAA;AAAA,IAEvB;AAAA,EACH,CACF;AAEJ;AAEA,MAAM,gBAAgB,6BAA6B,mBAAmB,iBAAiB,2BAA2B;AAElH,cAAc,cAAc;", "names": []}