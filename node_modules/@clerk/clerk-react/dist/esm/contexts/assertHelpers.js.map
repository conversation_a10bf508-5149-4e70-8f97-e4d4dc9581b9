{"version": 3, "sources": ["../../../src/contexts/assertHelpers.ts"], "sourcesContent": ["import { noClerkProviderError, noGuaranteedLoadedError } from '../errors';\n\nexport function assertWrappedByClerkProvider(contextVal: unknown): asserts contextVal {\n  if (!contextVal) {\n    throw new Error(noClerkProviderError);\n  }\n}\n\nexport function assertClerkLoadedGuarantee(guarantee: unknown, hookName: string): asserts guarantee {\n  if (!guarantee) {\n    throw new Error(noGuaranteedLoadedError(hookName));\n  }\n}\n"], "mappings": ";AAAA,SAAS,sBAAsB,+BAA+B;AAEvD,SAAS,6BAA6B,YAAyC;AACpF,MAAI,CAAC,YAAY;AACf,UAAM,IAAI,MAAM,oBAAoB;AAAA,EACtC;AACF;AAEO,SAAS,2BAA2B,WAAoB,UAAqC;AAClG,MAAI,CAAC,WAAW;AACd,UAAM,IAAI,MAAM,wBAAwB,QAAQ,CAAC;AAAA,EACnD;AACF;", "names": []}