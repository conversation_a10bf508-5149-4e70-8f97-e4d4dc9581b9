{"version": 3, "sources": ["../../src/isomorphicClerk.ts"], "sourcesContent": ["import { inBrowser } from '@clerk/shared/browser';\nimport { deprecated } from '@clerk/shared/deprecated';\nimport { handleValueOrFn } from '@clerk/shared/handleValueOrFn';\nimport type {\n  ActiveSessionResource,\n  AuthenticateWithGoogleOneTapParams,\n  AuthenticateWithMetamaskParams,\n  BeforeEmitCallback,\n  Clerk,\n  ClientResource,\n  CreateOrganizationParams,\n  CreateOrganizationProps,\n  DomainOrProxyUrl,\n  GoogleOneTapProps,\n  HandleEmailLinkVerificationParams,\n  HandleMagicLinkVerificationParams,\n  HandleOAuthCallbackParams,\n  InstanceType,\n  ListenerCallback,\n  LoadedClerk,\n  OrganizationListProps,\n  OrganizationMembershipResource,\n  OrganizationProfileProps,\n  OrganizationResource,\n  OrganizationSwitcherProps,\n  RedirectOptions,\n  SDKMetadata,\n  SetActiveParams,\n  SignInProps,\n  SignInRedirectOptions,\n  SignInResource,\n  SignOut,\n  SignOutCallback,\n  SignOutOptions,\n  SignUpProps,\n  SignUpRedirectOptions,\n  SignUpResource,\n  UnsubscribeCallback,\n  UserButtonProps,\n  UserProfileProps,\n  UserResource,\n} from '@clerk/types';\n\nimport { unsupportedNonBrowserDomainOrProxyUrlFunction } from './errors';\nimport type {\n  BrowserClerk,\n  BrowserClerkConstructor,\n  ClerkProp,\n  HeadlessBrowserClerk,\n  HeadlessBrowserClerkConstrutor,\n  IsomorphicClerkOptions,\n} from './types';\nimport { isConstructor, loadClerkJsScript } from './utils';\n\nexport interface Global {\n  Clerk?: HeadlessBrowserClerk | BrowserClerk;\n}\n\ndeclare const global: Global;\n\ntype GenericFunction<TArgs = never> = (...args: TArgs[]) => unknown;\n\ntype MethodName<T> = {\n  [P in keyof T]: T[P] extends GenericFunction ? P : never;\n}[keyof T];\n\ntype MethodCallback = () => Promise<unknown> | unknown;\n\ntype IsomorphicLoadedClerk = Omit<\n  LoadedClerk,\n  /**\n   * Override ClerkJS methods in order to support premountMethodCalls\n   */\n  | 'buildSignInUrl'\n  | 'buildSignUpUrl'\n  | 'buildUserProfileUrl'\n  | 'buildCreateOrganizationUrl'\n  | 'buildOrganizationProfileUrl'\n  | 'buildHomeUrl'\n  | 'buildUrlWithAuth'\n  | 'redirectWithAuth'\n  | 'redirectToSignIn'\n  | 'redirectToSignUp'\n  | 'handleRedirectCallback'\n  | 'handleGoogleOneTapCallback'\n  | 'handleUnauthenticated'\n  | 'authenticateWithMetamask'\n  | 'authenticateWithGoogleOneTap'\n  | 'createOrganization'\n  | 'getOrganization'\n  | 'mountUserButton'\n  | 'mountOrganizationList'\n  | 'mountOrganizationSwitcher'\n  | 'mountOrganizationProfile'\n  | 'mountCreateOrganization'\n  | 'mountSignUp'\n  | 'mountSignIn'\n  | 'mountUserProfile'\n  | 'client'\n  | 'getOrganizationMemberships'\n> & {\n  // TODO: Align return type\n  redirectWithAuth: (...args: Parameters<Clerk['redirectWithAuth']>) => void;\n  // TODO: Align return type\n  redirectToSignIn: (options: SignInRedirectOptions) => void;\n  // TODO: Align return type\n  redirectToSignUp: (options: SignUpRedirectOptions) => void;\n  // TODO: Align return type and parms\n  handleRedirectCallback: (params: HandleOAuthCallbackParams) => void;\n  handleGoogleOneTapCallback: (signInOrUp: SignInResource | SignUpResource, params: HandleOAuthCallbackParams) => void;\n  handleUnauthenticated: () => void;\n  // TODO: Align Promise unknown\n  authenticateWithMetamask: (params: AuthenticateWithMetamaskParams) => Promise<void>;\n  authenticateWithGoogleOneTap: (\n    params: AuthenticateWithGoogleOneTapParams,\n  ) => Promise<SignInResource | SignUpResource>;\n  // TODO: Align return type (maybe not possible or correct)\n  createOrganization: (params: CreateOrganizationParams) => Promise<OrganizationResource | void>;\n  // TODO: Align return type (maybe not possible or correct)\n  getOrganization: (organizationId: string) => Promise<OrganizationResource | void>;\n\n  // TODO: Align return type\n  buildSignInUrl: (opts?: RedirectOptions) => string | void;\n  // TODO: Align return type\n  buildSignUpUrl: (opts?: RedirectOptions) => string | void;\n  // TODO: Align return type\n  buildUserProfileUrl: () => string | void;\n  // TODO: Align return type\n  buildCreateOrganizationUrl: () => string | void;\n  // TODO: Align return type\n  buildOrganizationProfileUrl: () => string | void;\n  // TODO: Align return type\n  buildHomeUrl: () => string | void;\n  // TODO: Align return type\n  buildUrlWithAuth: (to: string) => string | void;\n\n  // TODO: Align optional props\n  mountUserButton: (node: HTMLDivElement, props: UserButtonProps) => void;\n  mountOrganizationList: (node: HTMLDivElement, props: OrganizationListProps) => void;\n  mountOrganizationSwitcher: (node: HTMLDivElement, props: OrganizationSwitcherProps) => void;\n  mountOrganizationProfile: (node: HTMLDivElement, props: OrganizationProfileProps) => void;\n  mountCreateOrganization: (node: HTMLDivElement, props: CreateOrganizationProps) => void;\n  mountSignUp: (node: HTMLDivElement, props: SignUpProps) => void;\n  mountSignIn: (node: HTMLDivElement, props: SignInProps) => void;\n  mountUserProfile: (node: HTMLDivElement, props: UserProfileProps) => void;\n  client: ClientResource | undefined;\n\n  getOrganizationMemberships: () => Promise<OrganizationMembershipResource[] | void>;\n};\n\nexport default class IsomorphicClerk implements IsomorphicLoadedClerk {\n  private readonly mode: 'browser' | 'server';\n  private readonly options: IsomorphicClerkOptions;\n  private readonly Clerk: ClerkProp;\n  private clerkjs: BrowserClerk | HeadlessBrowserClerk | null = null;\n  private preopenOneTap?: null | GoogleOneTapProps = null;\n  private preopenSignIn?: null | SignInProps = null;\n  private preopenSignUp?: null | SignUpProps = null;\n  private preopenUserProfile?: null | UserProfileProps = null;\n  private preopenOrganizationProfile?: null | OrganizationProfileProps = null;\n  private preopenCreateOrganization?: null | CreateOrganizationProps = null;\n  private premountSignInNodes = new Map<HTMLDivElement, SignInProps>();\n  private premountSignUpNodes = new Map<HTMLDivElement, SignUpProps>();\n  private premountUserProfileNodes = new Map<HTMLDivElement, UserProfileProps>();\n  private premountUserButtonNodes = new Map<HTMLDivElement, UserButtonProps>();\n  private premountOrganizationProfileNodes = new Map<HTMLDivElement, OrganizationProfileProps>();\n  private premountCreateOrganizationNodes = new Map<HTMLDivElement, CreateOrganizationProps>();\n  private premountOrganizationSwitcherNodes = new Map<HTMLDivElement, OrganizationSwitcherProps>();\n  private premountOrganizationListNodes = new Map<HTMLDivElement, OrganizationListProps>();\n  private premountMethodCalls = new Map<MethodName<BrowserClerk>, MethodCallback>();\n  private loadedListeners: Array<() => void> = [];\n\n  #loaded = false;\n  #domain: DomainOrProxyUrl['domain'];\n  #proxyUrl: DomainOrProxyUrl['proxyUrl'];\n  #frontendApi: string | undefined;\n  #publishableKey: string | undefined;\n\n  get publishableKey(): string | undefined {\n    return this.#publishableKey;\n  }\n\n  get loaded(): boolean {\n    return this.#loaded;\n  }\n\n  static #instance: IsomorphicClerk | null | undefined;\n\n  static getOrCreateInstance(options: IsomorphicClerkOptions) {\n    // During SSR: a new instance should be created for every request\n    // During CSR: use the cached instance for the whole lifetime of the app\n    // Also will recreate the instance if the provided Clerk instance changes\n    // This method should be idempotent in both scenarios\n    if (!inBrowser() || !this.#instance || (options.Clerk && this.#instance.Clerk !== options.Clerk)) {\n      this.#instance = new IsomorphicClerk(options);\n    }\n    return this.#instance;\n  }\n\n  static clearInstance() {\n    this.#instance = null;\n  }\n\n  get domain(): string {\n    // This getter can run in environments where window is not available.\n    // In those cases we should expect and use domain as a string\n    if (typeof window !== 'undefined' && window.location) {\n      return handleValueOrFn(this.#domain, new URL(window.location.href), '');\n    }\n    if (typeof this.#domain === 'function') {\n      throw new Error(unsupportedNonBrowserDomainOrProxyUrlFunction);\n    }\n    return this.#domain || '';\n  }\n\n  get proxyUrl(): string {\n    // This getter can run in environments where window is not available.\n    // In those cases we should expect and use proxy as a string\n    if (typeof window !== 'undefined' && window.location) {\n      return handleValueOrFn(this.#proxyUrl, new URL(window.location.href), '');\n    }\n    if (typeof this.#proxyUrl === 'function') {\n      throw new Error(unsupportedNonBrowserDomainOrProxyUrlFunction);\n    }\n    return this.#proxyUrl || '';\n  }\n\n  constructor(options: IsomorphicClerkOptions) {\n    const { Clerk = null, frontendApi, publishableKey } = options || {};\n    this.#frontendApi = frontendApi;\n    this.#publishableKey = publishableKey;\n    this.#proxyUrl = options?.proxyUrl;\n    this.#domain = options?.domain;\n    this.options = options;\n    this.Clerk = Clerk;\n    this.mode = inBrowser() ? 'browser' : 'server';\n    void this.loadClerkJS();\n  }\n\n  get sdkMetadata(): SDKMetadata | undefined {\n    return this.clerkjs?.sdkMetadata || this.options.sdkMetadata || undefined;\n  }\n\n  get instanceType(): InstanceType | undefined {\n    return this.clerkjs?.instanceType;\n  }\n\n  get frontendApi(): string {\n    return this.clerkjs?.frontendApi || this.#frontendApi || '';\n  }\n\n  get isStandardBrowser(): boolean {\n    return this.clerkjs?.isStandardBrowser || this.options.standardBrowser || false;\n  }\n\n  get isSatellite(): boolean {\n    // This getter can run in environments where window is not available.\n    // In those cases we should expect and use domain as a string\n    if (typeof window !== 'undefined' && window.location) {\n      return handleValueOrFn(this.options.isSatellite, new URL(window.location.href), false);\n    }\n    if (typeof this.options.isSatellite === 'function') {\n      throw new Error(unsupportedNonBrowserDomainOrProxyUrlFunction);\n    }\n    return false;\n  }\n\n  isReady = (): boolean => Boolean(this.clerkjs?.isReady());\n\n  buildSignInUrl = (opts?: RedirectOptions): string | void => {\n    const callback = () => this.clerkjs?.buildSignInUrl(opts) || '';\n    if (this.clerkjs && this.#loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildSignInUrl', callback);\n    }\n  };\n\n  buildSignUpUrl = (opts?: RedirectOptions): string | void => {\n    const callback = () => this.clerkjs?.buildSignUpUrl(opts) || '';\n    if (this.clerkjs && this.#loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildSignUpUrl', callback);\n    }\n  };\n\n  buildUserProfileUrl = (): string | void => {\n    const callback = () => this.clerkjs?.buildUserProfileUrl() || '';\n    if (this.clerkjs && this.#loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildUserProfileUrl', callback);\n    }\n  };\n\n  buildCreateOrganizationUrl = (): string | void => {\n    const callback = () => this.clerkjs?.buildCreateOrganizationUrl() || '';\n    if (this.clerkjs && this.#loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildCreateOrganizationUrl', callback);\n    }\n  };\n\n  buildOrganizationProfileUrl = (): string | void => {\n    const callback = () => this.clerkjs?.buildOrganizationProfileUrl() || '';\n    if (this.clerkjs && this.#loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildOrganizationProfileUrl', callback);\n    }\n  };\n\n  buildHomeUrl = (): string | void => {\n    const callback = () => this.clerkjs?.buildHomeUrl() || '';\n    if (this.clerkjs && this.#loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildHomeUrl', callback);\n    }\n  };\n\n  buildUrlWithAuth = (to: string): string | void => {\n    const callback = () => this.clerkjs?.buildUrlWithAuth(to) || '';\n    if (this.clerkjs && this.#loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildUrlWithAuth', callback);\n    }\n  };\n\n  handleUnauthenticated = (): void => {\n    const callback = () => this.clerkjs?.handleUnauthenticated();\n    if (this.clerkjs && this.#loaded) {\n      void callback();\n    } else {\n      this.premountMethodCalls.set('handleUnauthenticated', callback);\n    }\n  };\n\n  #waitForClerkJS(): Promise<HeadlessBrowserClerk | BrowserClerk> {\n    return new Promise<HeadlessBrowserClerk | BrowserClerk>(resolve => {\n      if (this.#loaded) {\n        resolve(this.clerkjs!);\n      }\n      this.addOnLoaded(() => resolve(this.clerkjs!));\n    });\n  }\n\n  async loadClerkJS(): Promise<HeadlessBrowserClerk | BrowserClerk | undefined> {\n    if (this.mode !== 'browser' || this.#loaded) {\n      return;\n    }\n\n    // Store frontendAPI value on window as a fallback. This value can be used as a\n    // fallback during ClerkJS hot loading in case ClerkJS fails to find the\n    // \"data-clerk-frontend-api\" attribute on its script tag.\n\n    // This can happen when the DOM is altered completely during client rehydration.\n    // For example, in Remix with React 18 the document changes completely via `hydrateRoot(document)`.\n\n    // For more information refer to:\n    // - https://github.com/remix-run/remix/issues/2947\n    // - https://github.com/facebook/react/issues/24430\n    if (typeof window !== 'undefined') {\n      window.__clerk_frontend_api = this.frontendApi;\n      window.__clerk_publishable_key = this.publishableKey;\n      window.__clerk_proxy_url = this.proxyUrl;\n      window.__clerk_domain = this.domain;\n    }\n\n    try {\n      if (this.Clerk) {\n        // Set a fixed Clerk version\n        let c: ClerkProp;\n\n        if (isConstructor<BrowserClerkConstructor | HeadlessBrowserClerkConstrutor>(this.Clerk)) {\n          // Construct a new Clerk object if a constructor is passed\n          c = new this.Clerk(this.publishableKey || this.frontendApi || '', {\n            proxyUrl: this.proxyUrl,\n            domain: this.domain,\n          } as any);\n          await c.load(this.options);\n        } else {\n          // Otherwise use the instantiated Clerk object\n          c = this.Clerk;\n\n          if (!c.isReady()) {\n            await c.load(this.options);\n          }\n        }\n\n        global.Clerk = c;\n      } else {\n        // Hot-load latest ClerkJS from Clerk CDN\n        if (!global.Clerk) {\n          await loadClerkJsScript({\n            ...this.options,\n            frontendApi: this.frontendApi,\n            publishableKey: this.publishableKey,\n            proxyUrl: this.proxyUrl,\n            domain: this.domain,\n          });\n        }\n\n        if (!global.Clerk) {\n          throw new Error('Failed to download latest ClerkJS. Contact <EMAIL>.');\n        }\n\n        await global.Clerk.load(this.options);\n      }\n\n      global.Clerk.sdkMetadata = this.options.sdkMetadata ?? { name: PACKAGE_NAME, version: PACKAGE_VERSION };\n\n      if (global.Clerk?.loaded || global.Clerk?.isReady()) {\n        return this.hydrateClerkJS(global.Clerk);\n      }\n      return;\n    } catch (err) {\n      const error = err as Error;\n      // In Next.js we can throw a full screen error in development mode.\n      // However, in production throwing an error results in an infinite loop.\n      // More info at: https://github.com/vercel/next.js/issues/6973\n      if (process.env.NODE_ENV === 'production') {\n        console.error(error.stack || error.message || error);\n      } else {\n        throw err;\n      }\n      return;\n    }\n  }\n\n  public addOnLoaded = (cb: () => void) => {\n    this.loadedListeners.push(cb);\n    /**\n     * When IsomorphicClerk is loaded execute the callback directly\n     */\n    if (this.loaded) {\n      this.emitLoaded();\n    }\n  };\n\n  public emitLoaded = () => {\n    this.loadedListeners.forEach(cb => cb());\n    this.loadedListeners = [];\n  };\n\n  private hydrateClerkJS = (clerkjs: BrowserClerk | HeadlessBrowserClerk | undefined) => {\n    if (!clerkjs) {\n      throw new Error('Failed to hydrate latest Clerk JS');\n    }\n\n    this.clerkjs = clerkjs;\n\n    this.premountMethodCalls.forEach(cb => cb());\n\n    if (this.preopenSignIn !== null) {\n      clerkjs.openSignIn(this.preopenSignIn);\n    }\n\n    if (this.preopenSignUp !== null) {\n      clerkjs.openSignUp(this.preopenSignUp);\n    }\n\n    if (this.preopenUserProfile !== null) {\n      clerkjs.openUserProfile(this.preopenUserProfile);\n    }\n\n    if (this.preopenOneTap !== null) {\n      clerkjs.openGoogleOneTap(this.preopenOneTap);\n    }\n\n    if (this.preopenOrganizationProfile !== null) {\n      clerkjs.openOrganizationProfile(this.preopenOrganizationProfile);\n    }\n\n    if (this.preopenCreateOrganization !== null) {\n      clerkjs.openCreateOrganization(this.preopenCreateOrganization);\n    }\n\n    this.premountSignInNodes.forEach((props: SignInProps, node: HTMLDivElement) => {\n      clerkjs.mountSignIn(node, props);\n    });\n\n    this.premountSignUpNodes.forEach((props: SignUpProps, node: HTMLDivElement) => {\n      clerkjs.mountSignUp(node, props);\n    });\n\n    this.premountUserProfileNodes.forEach((props: UserProfileProps, node: HTMLDivElement) => {\n      clerkjs.mountUserProfile(node, props);\n    });\n\n    this.premountUserButtonNodes.forEach((props: UserButtonProps, node: HTMLDivElement) => {\n      clerkjs.mountUserButton(node, props);\n    });\n\n    this.premountOrganizationListNodes.forEach((props: OrganizationListProps, node: HTMLDivElement) => {\n      clerkjs.mountOrganizationList(node, props);\n    });\n\n    this.#loaded = true;\n    this.emitLoaded();\n    return this.clerkjs;\n  };\n\n  get version(): string | undefined {\n    return this.clerkjs?.version;\n  }\n\n  get client(): ClientResource | undefined {\n    if (this.clerkjs) {\n      return this.clerkjs.client;\n      // TODO: add ssr condition\n    } else {\n      return undefined;\n    }\n  }\n\n  get session(): ActiveSessionResource | undefined | null {\n    if (this.clerkjs) {\n      return this.clerkjs.session;\n    } else {\n      return undefined;\n    }\n  }\n\n  get user(): UserResource | undefined | null {\n    if (this.clerkjs) {\n      return this.clerkjs.user;\n    } else {\n      return undefined;\n    }\n  }\n\n  get organization(): OrganizationResource | undefined | null {\n    if (this.clerkjs) {\n      return this.clerkjs.organization;\n    } else {\n      return undefined;\n    }\n  }\n\n  get __unstable__environment(): any {\n    if (this.clerkjs) {\n      return (this.clerkjs as any).__unstable__environment;\n      // TODO: add ssr condition\n    } else {\n      return undefined;\n    }\n  }\n\n  __unstable__setEnvironment(...args: any): void {\n    if (this.clerkjs && '__unstable__setEnvironment' in this.clerkjs) {\n      (this.clerkjs as any).__unstable__setEnvironment(args);\n    } else {\n      return undefined;\n    }\n  }\n\n  __unstable__updateProps = (props: any): any => {\n    // Handle case where accounts has clerk-react@4 installed, but clerk-js@3 is manually loaded\n    if (this.clerkjs && '__unstable__updateProps' in this.clerkjs) {\n      (this.clerkjs as any).__unstable__updateProps(props);\n    } else {\n      return undefined;\n    }\n  };\n\n  /**\n   * `setActive` can be used to set the active session and/or organization.\n   */\n  setActive = ({ session, organization, beforeEmit }: SetActiveParams): Promise<void> => {\n    if (this.clerkjs) {\n      return this.clerkjs.setActive({ session, organization, beforeEmit });\n    } else {\n      return Promise.reject();\n    }\n  };\n\n  setSession = (session: ActiveSessionResource | string | null, beforeEmit?: BeforeEmitCallback): Promise<void> => {\n    deprecated('setSession', 'Use `Clerk.setActive` instead');\n    return this.setActive({ session, beforeEmit });\n  };\n\n  openSignIn = (props?: SignInProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.openSignIn(props);\n    } else {\n      this.preopenSignIn = props;\n    }\n  };\n\n  closeSignIn = (): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.closeSignIn();\n    } else {\n      this.preopenSignIn = null;\n    }\n  };\n\n  openGoogleOneTap = (props?: GoogleOneTapProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.openGoogleOneTap(props);\n    } else {\n      this.preopenOneTap = props;\n    }\n  };\n\n  closeGoogleOneTap = (): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.closeGoogleOneTap();\n    } else {\n      this.preopenOneTap = null;\n    }\n  };\n\n  openUserProfile = (props?: UserProfileProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.openUserProfile(props);\n    } else {\n      this.preopenUserProfile = props;\n    }\n  };\n\n  closeUserProfile = (): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.closeUserProfile();\n    } else {\n      this.preopenUserProfile = null;\n    }\n  };\n\n  openOrganizationProfile = (props?: OrganizationProfileProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.openOrganizationProfile(props);\n    } else {\n      this.preopenOrganizationProfile = props;\n    }\n  };\n\n  closeOrganizationProfile = (): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.closeOrganizationProfile();\n    } else {\n      this.preopenOrganizationProfile = null;\n    }\n  };\n\n  openCreateOrganization = (props?: CreateOrganizationProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.openCreateOrganization(props);\n    } else {\n      this.preopenCreateOrganization = props;\n    }\n  };\n\n  closeCreateOrganization = (): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.closeCreateOrganization();\n    } else {\n      this.preopenCreateOrganization = null;\n    }\n  };\n\n  openSignUp = (props?: SignUpProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.openSignUp(props);\n    } else {\n      this.preopenSignUp = props;\n    }\n  };\n\n  closeSignUp = (): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.closeSignUp();\n    } else {\n      this.preopenSignUp = null;\n    }\n  };\n\n  mountSignIn = (node: HTMLDivElement, props: SignInProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.mountSignIn(node, props);\n    } else {\n      this.premountSignInNodes.set(node, props);\n    }\n  };\n\n  unmountSignIn = (node: HTMLDivElement): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.unmountSignIn(node);\n    } else {\n      this.premountSignInNodes.delete(node);\n    }\n  };\n\n  mountSignUp = (node: HTMLDivElement, props: SignUpProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.mountSignUp(node, props);\n    } else {\n      this.premountSignUpNodes.set(node, props);\n    }\n  };\n\n  unmountSignUp = (node: HTMLDivElement): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.unmountSignUp(node);\n    } else {\n      this.premountSignUpNodes.delete(node);\n    }\n  };\n\n  mountUserProfile = (node: HTMLDivElement, props: UserProfileProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.mountUserProfile(node, props);\n    } else {\n      this.premountUserProfileNodes.set(node, props);\n    }\n  };\n\n  unmountUserProfile = (node: HTMLDivElement): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.unmountUserProfile(node);\n    } else {\n      this.premountUserProfileNodes.delete(node);\n    }\n  };\n\n  mountOrganizationProfile = (node: HTMLDivElement, props: OrganizationProfileProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.mountOrganizationProfile(node, props);\n    } else {\n      this.premountOrganizationProfileNodes.set(node, props);\n    }\n  };\n\n  unmountOrganizationProfile = (node: HTMLDivElement): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.unmountOrganizationProfile(node);\n    } else {\n      this.premountOrganizationProfileNodes.delete(node);\n    }\n  };\n\n  mountCreateOrganization = (node: HTMLDivElement, props: CreateOrganizationProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.mountCreateOrganization(node, props);\n    } else {\n      this.premountCreateOrganizationNodes.set(node, props);\n    }\n  };\n\n  unmountCreateOrganization = (node: HTMLDivElement): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.unmountCreateOrganization(node);\n    } else {\n      this.premountCreateOrganizationNodes.delete(node);\n    }\n  };\n\n  mountOrganizationSwitcher = (node: HTMLDivElement, props: OrganizationSwitcherProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.mountOrganizationSwitcher(node, props);\n    } else {\n      this.premountOrganizationSwitcherNodes.set(node, props);\n    }\n  };\n\n  unmountOrganizationSwitcher = (node: HTMLDivElement): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.unmountOrganizationSwitcher(node);\n    } else {\n      this.premountOrganizationSwitcherNodes.delete(node);\n    }\n  };\n\n  mountOrganizationList = (node: HTMLDivElement, props: OrganizationListProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.mountOrganizationList(node, props);\n    } else {\n      this.premountOrganizationListNodes.set(node, props);\n    }\n  };\n\n  unmountOrganizationList = (node: HTMLDivElement): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.unmountOrganizationList(node);\n    } else {\n      this.premountOrganizationListNodes.delete(node);\n    }\n  };\n\n  mountUserButton = (node: HTMLDivElement, userButtonProps: UserButtonProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.mountUserButton(node, userButtonProps);\n    } else {\n      this.premountUserButtonNodes.set(node, userButtonProps);\n    }\n  };\n\n  unmountUserButton = (node: HTMLDivElement): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.unmountUserButton(node);\n    } else {\n      this.premountUserButtonNodes.delete(node);\n    }\n  };\n\n  addListener = (listener: ListenerCallback): UnsubscribeCallback => {\n    const callback = () => this.clerkjs?.addListener(listener);\n\n    if (this.clerkjs) {\n      return callback() as UnsubscribeCallback;\n    } else {\n      this.premountMethodCalls.set('addListener', callback);\n      return () => this.premountMethodCalls.delete('addListener');\n    }\n  };\n\n  navigate = (to: string): void => {\n    const callback = () => this.clerkjs?.navigate(to);\n    if (this.clerkjs && this.#loaded) {\n      void callback();\n    } else {\n      this.premountMethodCalls.set('navigate', callback);\n    }\n  };\n\n  redirectWithAuth = (...args: Parameters<Clerk['redirectWithAuth']>): void => {\n    const callback = () => this.clerkjs?.redirectWithAuth(...args);\n    if (this.clerkjs && this.#loaded) {\n      void callback();\n    } else {\n      this.premountMethodCalls.set('redirectWithAuth', callback);\n    }\n  };\n\n  redirectToSignIn = (opts: SignInRedirectOptions): void => {\n    const callback = () => this.clerkjs?.redirectToSignIn(opts as any);\n    if (this.clerkjs && this.#loaded) {\n      void callback();\n    } else {\n      this.premountMethodCalls.set('redirectToSignIn', callback);\n    }\n  };\n\n  redirectToSignUp = (opts: SignUpRedirectOptions): void => {\n    const callback = () => this.clerkjs?.redirectToSignUp(opts as any);\n    if (this.clerkjs && this.#loaded) {\n      void callback();\n    } else {\n      this.premountMethodCalls.set('redirectToSignUp', callback);\n    }\n  };\n\n  redirectToUserProfile = (): void => {\n    const callback = () => this.clerkjs?.redirectToUserProfile();\n    if (this.clerkjs && this.#loaded) {\n      callback();\n    } else {\n      this.premountMethodCalls.set('redirectToUserProfile', callback);\n    }\n  };\n\n  redirectToHome = (): void => {\n    const callback = () => this.clerkjs?.redirectToHome();\n    if (this.clerkjs && this.#loaded) {\n      callback();\n    } else {\n      this.premountMethodCalls.set('redirectToHome', callback);\n    }\n  };\n\n  redirectToOrganizationProfile = (): void => {\n    const callback = () => this.clerkjs?.redirectToOrganizationProfile();\n    if (this.clerkjs && this.#loaded) {\n      callback();\n    } else {\n      this.premountMethodCalls.set('redirectToOrganizationProfile', callback);\n    }\n  };\n\n  redirectToCreateOrganization = (): void => {\n    const callback = () => this.clerkjs?.redirectToCreateOrganization();\n    if (this.clerkjs && this.#loaded) {\n      callback();\n    } else {\n      this.premountMethodCalls.set('redirectToCreateOrganization', callback);\n    }\n  };\n\n  handleRedirectCallback = (params: HandleOAuthCallbackParams): void => {\n    const callback = () => this.clerkjs?.handleRedirectCallback(params);\n    if (this.clerkjs && this.#loaded) {\n      void callback()?.catch(() => {\n        // This error is caused when the host app is using React18\n        // and strictMode is enabled. This useEffects runs twice because\n        // the clerk-react ui components mounts, unmounts and mounts again\n        // so the clerk-js component loses its state because of the custom\n        // unmount callback we're using.\n        // This needs to be solved by tweaking the logic in uiComponents.tsx\n        // or by making handleRedirectCallback idempotent\n      });\n    } else {\n      this.premountMethodCalls.set('handleRedirectCallback', callback);\n    }\n  };\n  /**\n   * @deprecated Use `handleEmailLinkVerification` instead.\n   */\n  handleMagicLinkVerification = async (params: HandleMagicLinkVerificationParams): Promise<void> => {\n    deprecated('handleMagicLinkVerification', 'Use `handleEmailLinkVerification` instead.');\n    const callback = () => this.clerkjs?.handleMagicLinkVerification(params);\n    if (this.clerkjs && this.#loaded) {\n      return callback() as Promise<void>;\n    } else {\n      this.premountMethodCalls.set('handleMagicLinkVerification', callback);\n    }\n  };\n\n  handleGoogleOneTapCallback = (\n    signInOrUp: SignInResource | SignUpResource,\n    params: HandleOAuthCallbackParams,\n  ): void => {\n    const callback = () => this.clerkjs?.handleGoogleOneTapCallback(signInOrUp, params);\n    if (this.clerkjs && this.#loaded) {\n      void callback()?.catch(() => {\n        // This error is caused when the host app is using React18\n        // and strictMode is enabled. This useEffects runs twice because\n        // the clerk-react ui components mounts, unmounts and mounts again\n        // so the clerk-js component loses its state because of the custom\n        // unmount callback we're using.\n        // This needs to be solved by tweaking the logic in uiComponents.tsx\n        // or by making handleRedirectCallback idempotent\n      });\n    } else {\n      this.premountMethodCalls.set('handleGoogleOneTapCallback', callback);\n    }\n  };\n\n  handleEmailLinkVerification = async (params: HandleEmailLinkVerificationParams): Promise<void> => {\n    const callback = () => this.clerkjs?.handleEmailLinkVerification(params);\n    if (this.clerkjs && this.#loaded) {\n      return callback() as Promise<void>;\n    } else {\n      this.premountMethodCalls.set('handleEmailLinkVerification', callback);\n    }\n  };\n\n  authenticateWithMetamask = async (params: AuthenticateWithMetamaskParams): Promise<void> => {\n    const callback = () => this.clerkjs?.authenticateWithMetamask(params);\n    if (this.clerkjs && this.#loaded) {\n      return callback() as Promise<void>;\n    } else {\n      this.premountMethodCalls.set('authenticateWithMetamask', callback);\n    }\n  };\n\n  authenticateWithGoogleOneTap = async (\n    params: AuthenticateWithGoogleOneTapParams,\n  ): Promise<SignInResource | SignUpResource> => {\n    const clerkjs = await this.#waitForClerkJS();\n    return clerkjs.authenticateWithGoogleOneTap(params);\n  };\n\n  createOrganization = async (params: CreateOrganizationParams): Promise<OrganizationResource | void> => {\n    const callback = () => this.clerkjs?.createOrganization(params);\n    if (this.clerkjs && this.#loaded) {\n      return callback() as Promise<OrganizationResource>;\n    } else {\n      this.premountMethodCalls.set('createOrganization', callback);\n    }\n  };\n\n  getOrganizationMemberships = async (): Promise<OrganizationMembershipResource[] | void> => {\n    const callback = () => this.clerkjs?.getOrganizationMemberships();\n    if (this.clerkjs && this.#loaded) {\n      return callback() as Promise<OrganizationMembershipResource[]>;\n    } else {\n      this.premountMethodCalls.set('getOrganizationMemberships', callback);\n    }\n  };\n\n  getOrganization = async (organizationId: string): Promise<OrganizationResource | void> => {\n    const callback = () => this.clerkjs?.getOrganization(organizationId);\n    if (this.clerkjs && this.#loaded) {\n      return callback() as Promise<OrganizationResource>;\n    } else {\n      this.premountMethodCalls.set('getOrganization', callback);\n    }\n  };\n\n  signOut: SignOut = async (\n    signOutCallbackOrOptions?: SignOutCallback | SignOutOptions,\n    options?: SignOutOptions,\n  ): Promise<void> => {\n    const callback = () => this.clerkjs?.signOut(signOutCallbackOrOptions as any, options);\n    if (this.clerkjs && this.#loaded) {\n      return callback() as Promise<void>;\n    } else {\n      this.premountMethodCalls.set('signOut', callback);\n    }\n  };\n}\n"], "mappings": ";;;;;;AAAA;AAAA,SAAS,iBAAiB;AAC1B,SAAS,kBAAkB;AAC3B,SAAS,uBAAuB;AAyChC,SAAS,qDAAqD;AAS9D,SAAS,eAAe,yBAAyB;AAkGjD,MAAqB,mBAArB,MAAqB,iBAAiD;AAAA,EA6EpE,YAAY,SAAiC;AAkH7C;AA3LA,SAAQ,UAAsD;AAC9D,SAAQ,gBAA2C;AACnD,SAAQ,gBAAqC;AAC7C,SAAQ,gBAAqC;AAC7C,SAAQ,qBAA+C;AACvD,SAAQ,6BAA+D;AACvE,SAAQ,4BAA6D;AACrE,SAAQ,sBAAsB,oBAAI,IAAiC;AACnE,SAAQ,sBAAsB,oBAAI,IAAiC;AACnE,SAAQ,2BAA2B,oBAAI,IAAsC;AAC7E,SAAQ,0BAA0B,oBAAI,IAAqC;AAC3E,SAAQ,mCAAmC,oBAAI,IAA8C;AAC7F,SAAQ,kCAAkC,oBAAI,IAA6C;AAC3F,SAAQ,oCAAoC,oBAAI,IAA+C;AAC/F,SAAQ,gCAAgC,oBAAI,IAA2C;AACvF,SAAQ,sBAAsB,oBAAI,IAA8C;AAChF,SAAQ,kBAAqC,CAAC;AAE9C,gCAAU;AACV;AACA;AACA;AACA;AA2FA,mBAAU,MAAY;AA3QxB;AA2Q2B,sBAAQ,UAAK,YAAL,mBAAc,SAAS;AAAA;AAExD,0BAAiB,CAAC,SAA0C;AAC1D,YAAM,WAAW,MAAG;AA9QxB;AA8Q2B,2BAAK,YAAL,mBAAc,eAAe,UAAS;AAAA;AAC7D,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,kBAAkB,QAAQ;AAAA,MACzD;AAAA,IACF;AAEA,0BAAiB,CAAC,SAA0C;AAC1D,YAAM,WAAW,MAAG;AAvRxB;AAuR2B,2BAAK,YAAL,mBAAc,eAAe,UAAS;AAAA;AAC7D,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,kBAAkB,QAAQ;AAAA,MACzD;AAAA,IACF;AAEA,+BAAsB,MAAqB;AACzC,YAAM,WAAW,MAAG;AAhSxB;AAgS2B,2BAAK,YAAL,mBAAc,0BAAyB;AAAA;AAC9D,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,uBAAuB,QAAQ;AAAA,MAC9D;AAAA,IACF;AAEA,sCAA6B,MAAqB;AAChD,YAAM,WAAW,MAAG;AAzSxB;AAyS2B,2BAAK,YAAL,mBAAc,iCAAgC;AAAA;AACrE,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,8BAA8B,QAAQ;AAAA,MACrE;AAAA,IACF;AAEA,uCAA8B,MAAqB;AACjD,YAAM,WAAW,MAAG;AAlTxB;AAkT2B,2BAAK,YAAL,mBAAc,kCAAiC;AAAA;AACtE,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,+BAA+B,QAAQ;AAAA,MACtE;AAAA,IACF;AAEA,wBAAe,MAAqB;AAClC,YAAM,WAAW,MAAG;AA3TxB;AA2T2B,2BAAK,YAAL,mBAAc,mBAAkB;AAAA;AACvD,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,gBAAgB,QAAQ;AAAA,MACvD;AAAA,IACF;AAEA,4BAAmB,CAAC,OAA8B;AAChD,YAAM,WAAW,MAAG;AApUxB;AAoU2B,2BAAK,YAAL,mBAAc,iBAAiB,QAAO;AAAA;AAC7D,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,oBAAoB,QAAQ;AAAA,MAC3D;AAAA,IACF;AAEA,iCAAwB,MAAY;AAClC,YAAM,WAAW,MAAG;AA7UxB;AA6U2B,0BAAK,YAAL,mBAAc;AAAA;AACrC,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,aAAK,SAAS;AAAA,MAChB,OAAO;AACL,aAAK,oBAAoB,IAAI,yBAAyB,QAAQ;AAAA,MAChE;AAAA,IACF;AA8FA,SAAO,cAAc,CAAC,OAAmB;AACvC,WAAK,gBAAgB,KAAK,EAAE;AAI5B,UAAI,KAAK,QAAQ;AACf,aAAK,WAAW;AAAA,MAClB;AAAA,IACF;AAEA,SAAO,aAAa,MAAM;AACxB,WAAK,gBAAgB,QAAQ,QAAM,GAAG,CAAC;AACvC,WAAK,kBAAkB,CAAC;AAAA,IAC1B;AAEA,SAAQ,iBAAiB,CAAC,YAA6D;AACrF,UAAI,CAAC,SAAS;AACZ,cAAM,IAAI,MAAM,mCAAmC;AAAA,MACrD;AAEA,WAAK,UAAU;AAEf,WAAK,oBAAoB,QAAQ,QAAM,GAAG,CAAC;AAE3C,UAAI,KAAK,kBAAkB,MAAM;AAC/B,gBAAQ,WAAW,KAAK,aAAa;AAAA,MACvC;AAEA,UAAI,KAAK,kBAAkB,MAAM;AAC/B,gBAAQ,WAAW,KAAK,aAAa;AAAA,MACvC;AAEA,UAAI,KAAK,uBAAuB,MAAM;AACpC,gBAAQ,gBAAgB,KAAK,kBAAkB;AAAA,MACjD;AAEA,UAAI,KAAK,kBAAkB,MAAM;AAC/B,gBAAQ,iBAAiB,KAAK,aAAa;AAAA,MAC7C;AAEA,UAAI,KAAK,+BAA+B,MAAM;AAC5C,gBAAQ,wBAAwB,KAAK,0BAA0B;AAAA,MACjE;AAEA,UAAI,KAAK,8BAA8B,MAAM;AAC3C,gBAAQ,uBAAuB,KAAK,yBAAyB;AAAA,MAC/D;AAEA,WAAK,oBAAoB,QAAQ,CAAC,OAAoB,SAAyB;AAC7E,gBAAQ,YAAY,MAAM,KAAK;AAAA,MACjC,CAAC;AAED,WAAK,oBAAoB,QAAQ,CAAC,OAAoB,SAAyB;AAC7E,gBAAQ,YAAY,MAAM,KAAK;AAAA,MACjC,CAAC;AAED,WAAK,yBAAyB,QAAQ,CAAC,OAAyB,SAAyB;AACvF,gBAAQ,iBAAiB,MAAM,KAAK;AAAA,MACtC,CAAC;AAED,WAAK,wBAAwB,QAAQ,CAAC,OAAwB,SAAyB;AACrF,gBAAQ,gBAAgB,MAAM,KAAK;AAAA,MACrC,CAAC;AAED,WAAK,8BAA8B,QAAQ,CAAC,OAA8B,SAAyB;AACjG,gBAAQ,sBAAsB,MAAM,KAAK;AAAA,MAC3C,CAAC;AAED,yBAAK,SAAU;AACf,WAAK,WAAW;AAChB,aAAO,KAAK;AAAA,IACd;AAwDA,mCAA0B,CAAC,UAAoB;AAE7C,UAAI,KAAK,WAAW,6BAA6B,KAAK,SAAS;AAC7D,QAAC,KAAK,QAAgB,wBAAwB,KAAK;AAAA,MACrD,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAKA;AAAA;AAAA;AAAA,qBAAY,CAAC,EAAE,SAAS,cAAc,WAAW,MAAsC;AACrF,UAAI,KAAK,SAAS;AAChB,eAAO,KAAK,QAAQ,UAAU,EAAE,SAAS,cAAc,WAAW,CAAC;AAAA,MACrE,OAAO;AACL,eAAO,QAAQ,OAAO;AAAA,MACxB;AAAA,IACF;AAEA,sBAAa,CAAC,SAAgD,eAAmD;AAC/G,iBAAW,cAAc,+BAA+B;AACxD,aAAO,KAAK,UAAU,EAAE,SAAS,WAAW,CAAC;AAAA,IAC/C;AAEA,sBAAa,CAAC,UAA8B;AAC1C,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,aAAK,QAAQ,WAAW,KAAK;AAAA,MAC/B,OAAO;AACL,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACF;AAEA,uBAAc,MAAY;AACxB,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,aAAK,QAAQ,YAAY;AAAA,MAC3B,OAAO;AACL,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACF;AAEA,4BAAmB,CAAC,UAAoC;AACtD,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,aAAK,QAAQ,iBAAiB,KAAK;AAAA,MACrC,OAAO;AACL,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACF;AAEA,6BAAoB,MAAY;AAC9B,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,aAAK,QAAQ,kBAAkB;AAAA,MACjC,OAAO;AACL,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACF;AAEA,2BAAkB,CAAC,UAAmC;AACpD,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,aAAK,QAAQ,gBAAgB,KAAK;AAAA,MACpC,OAAO;AACL,aAAK,qBAAqB;AAAA,MAC5B;AAAA,IACF;AAEA,4BAAmB,MAAY;AAC7B,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,aAAK,QAAQ,iBAAiB;AAAA,MAChC,OAAO;AACL,aAAK,qBAAqB;AAAA,MAC5B;AAAA,IACF;AAEA,mCAA0B,CAAC,UAA2C;AACpE,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,aAAK,QAAQ,wBAAwB,KAAK;AAAA,MAC5C,OAAO;AACL,aAAK,6BAA6B;AAAA,MACpC;AAAA,IACF;AAEA,oCAA2B,MAAY;AACrC,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,aAAK,QAAQ,yBAAyB;AAAA,MACxC,OAAO;AACL,aAAK,6BAA6B;AAAA,MACpC;AAAA,IACF;AAEA,kCAAyB,CAAC,UAA0C;AAClE,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,aAAK,QAAQ,uBAAuB,KAAK;AAAA,MAC3C,OAAO;AACL,aAAK,4BAA4B;AAAA,MACnC;AAAA,IACF;AAEA,mCAA0B,MAAY;AACpC,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,aAAK,QAAQ,wBAAwB;AAAA,MACvC,OAAO;AACL,aAAK,4BAA4B;AAAA,MACnC;AAAA,IACF;AAEA,sBAAa,CAAC,UAA8B;AAC1C,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,aAAK,QAAQ,WAAW,KAAK;AAAA,MAC/B,OAAO;AACL,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACF;AAEA,uBAAc,MAAY;AACxB,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,aAAK,QAAQ,YAAY;AAAA,MAC3B,OAAO;AACL,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACF;AAEA,uBAAc,CAAC,MAAsB,UAA6B;AAChE,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,aAAK,QAAQ,YAAY,MAAM,KAAK;AAAA,MACtC,OAAO;AACL,aAAK,oBAAoB,IAAI,MAAM,KAAK;AAAA,MAC1C;AAAA,IACF;AAEA,yBAAgB,CAAC,SAA+B;AAC9C,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,aAAK,QAAQ,cAAc,IAAI;AAAA,MACjC,OAAO;AACL,aAAK,oBAAoB,OAAO,IAAI;AAAA,MACtC;AAAA,IACF;AAEA,uBAAc,CAAC,MAAsB,UAA6B;AAChE,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,aAAK,QAAQ,YAAY,MAAM,KAAK;AAAA,MACtC,OAAO;AACL,aAAK,oBAAoB,IAAI,MAAM,KAAK;AAAA,MAC1C;AAAA,IACF;AAEA,yBAAgB,CAAC,SAA+B;AAC9C,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,aAAK,QAAQ,cAAc,IAAI;AAAA,MACjC,OAAO;AACL,aAAK,oBAAoB,OAAO,IAAI;AAAA,MACtC;AAAA,IACF;AAEA,4BAAmB,CAAC,MAAsB,UAAkC;AAC1E,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,aAAK,QAAQ,iBAAiB,MAAM,KAAK;AAAA,MAC3C,OAAO;AACL,aAAK,yBAAyB,IAAI,MAAM,KAAK;AAAA,MAC/C;AAAA,IACF;AAEA,8BAAqB,CAAC,SAA+B;AACnD,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,aAAK,QAAQ,mBAAmB,IAAI;AAAA,MACtC,OAAO;AACL,aAAK,yBAAyB,OAAO,IAAI;AAAA,MAC3C;AAAA,IACF;AAEA,oCAA2B,CAAC,MAAsB,UAA0C;AAC1F,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,aAAK,QAAQ,yBAAyB,MAAM,KAAK;AAAA,MACnD,OAAO;AACL,aAAK,iCAAiC,IAAI,MAAM,KAAK;AAAA,MACvD;AAAA,IACF;AAEA,sCAA6B,CAAC,SAA+B;AAC3D,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,aAAK,QAAQ,2BAA2B,IAAI;AAAA,MAC9C,OAAO;AACL,aAAK,iCAAiC,OAAO,IAAI;AAAA,MACnD;AAAA,IACF;AAEA,mCAA0B,CAAC,MAAsB,UAAyC;AACxF,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,aAAK,QAAQ,wBAAwB,MAAM,KAAK;AAAA,MAClD,OAAO;AACL,aAAK,gCAAgC,IAAI,MAAM,KAAK;AAAA,MACtD;AAAA,IACF;AAEA,qCAA4B,CAAC,SAA+B;AAC1D,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,aAAK,QAAQ,0BAA0B,IAAI;AAAA,MAC7C,OAAO;AACL,aAAK,gCAAgC,OAAO,IAAI;AAAA,MAClD;AAAA,IACF;AAEA,qCAA4B,CAAC,MAAsB,UAA2C;AAC5F,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,aAAK,QAAQ,0BAA0B,MAAM,KAAK;AAAA,MACpD,OAAO;AACL,aAAK,kCAAkC,IAAI,MAAM,KAAK;AAAA,MACxD;AAAA,IACF;AAEA,uCAA8B,CAAC,SAA+B;AAC5D,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,aAAK,QAAQ,4BAA4B,IAAI;AAAA,MAC/C,OAAO;AACL,aAAK,kCAAkC,OAAO,IAAI;AAAA,MACpD;AAAA,IACF;AAEA,iCAAwB,CAAC,MAAsB,UAAuC;AACpF,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,aAAK,QAAQ,sBAAsB,MAAM,KAAK;AAAA,MAChD,OAAO;AACL,aAAK,8BAA8B,IAAI,MAAM,KAAK;AAAA,MACpD;AAAA,IACF;AAEA,mCAA0B,CAAC,SAA+B;AACxD,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,aAAK,QAAQ,wBAAwB,IAAI;AAAA,MAC3C,OAAO;AACL,aAAK,8BAA8B,OAAO,IAAI;AAAA,MAChD;AAAA,IACF;AAEA,2BAAkB,CAAC,MAAsB,oBAA2C;AAClF,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,aAAK,QAAQ,gBAAgB,MAAM,eAAe;AAAA,MACpD,OAAO;AACL,aAAK,wBAAwB,IAAI,MAAM,eAAe;AAAA,MACxD;AAAA,IACF;AAEA,6BAAoB,CAAC,SAA+B;AAClD,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,aAAK,QAAQ,kBAAkB,IAAI;AAAA,MACrC,OAAO;AACL,aAAK,wBAAwB,OAAO,IAAI;AAAA,MAC1C;AAAA,IACF;AAEA,uBAAc,CAAC,aAAoD;AACjE,YAAM,WAAW,MAAG;AA1yBxB;AA0yB2B,0BAAK,YAAL,mBAAc,YAAY;AAAA;AAEjD,UAAI,KAAK,SAAS;AAChB,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,eAAe,QAAQ;AACpD,eAAO,MAAM,KAAK,oBAAoB,OAAO,aAAa;AAAA,MAC5D;AAAA,IACF;AAEA,oBAAW,CAAC,OAAqB;AAC/B,YAAM,WAAW,MAAG;AArzBxB;AAqzB2B,0BAAK,YAAL,mBAAc,SAAS;AAAA;AAC9C,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,aAAK,SAAS;AAAA,MAChB,OAAO;AACL,aAAK,oBAAoB,IAAI,YAAY,QAAQ;AAAA,MACnD;AAAA,IACF;AAEA,4BAAmB,IAAI,SAAsD;AAC3E,YAAM,WAAW,MAAG;AA9zBxB;AA8zB2B,0BAAK,YAAL,mBAAc,iBAAiB,GAAG;AAAA;AACzD,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,aAAK,SAAS;AAAA,MAChB,OAAO;AACL,aAAK,oBAAoB,IAAI,oBAAoB,QAAQ;AAAA,MAC3D;AAAA,IACF;AAEA,4BAAmB,CAAC,SAAsC;AACxD,YAAM,WAAW,MAAG;AAv0BxB;AAu0B2B,0BAAK,YAAL,mBAAc,iBAAiB;AAAA;AACtD,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,aAAK,SAAS;AAAA,MAChB,OAAO;AACL,aAAK,oBAAoB,IAAI,oBAAoB,QAAQ;AAAA,MAC3D;AAAA,IACF;AAEA,4BAAmB,CAAC,SAAsC;AACxD,YAAM,WAAW,MAAG;AAh1BxB;AAg1B2B,0BAAK,YAAL,mBAAc,iBAAiB;AAAA;AACtD,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,aAAK,SAAS;AAAA,MAChB,OAAO;AACL,aAAK,oBAAoB,IAAI,oBAAoB,QAAQ;AAAA,MAC3D;AAAA,IACF;AAEA,iCAAwB,MAAY;AAClC,YAAM,WAAW,MAAG;AAz1BxB;AAy1B2B,0BAAK,YAAL,mBAAc;AAAA;AACrC,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,iBAAS;AAAA,MACX,OAAO;AACL,aAAK,oBAAoB,IAAI,yBAAyB,QAAQ;AAAA,MAChE;AAAA,IACF;AAEA,0BAAiB,MAAY;AAC3B,YAAM,WAAW,MAAG;AAl2BxB;AAk2B2B,0BAAK,YAAL,mBAAc;AAAA;AACrC,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,iBAAS;AAAA,MACX,OAAO;AACL,aAAK,oBAAoB,IAAI,kBAAkB,QAAQ;AAAA,MACzD;AAAA,IACF;AAEA,yCAAgC,MAAY;AAC1C,YAAM,WAAW,MAAG;AA32BxB;AA22B2B,0BAAK,YAAL,mBAAc;AAAA;AACrC,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,iBAAS;AAAA,MACX,OAAO;AACL,aAAK,oBAAoB,IAAI,iCAAiC,QAAQ;AAAA,MACxE;AAAA,IACF;AAEA,wCAA+B,MAAY;AACzC,YAAM,WAAW,MAAG;AAp3BxB;AAo3B2B,0BAAK,YAAL,mBAAc;AAAA;AACrC,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,iBAAS;AAAA,MACX,OAAO;AACL,aAAK,oBAAoB,IAAI,gCAAgC,QAAQ;AAAA,MACvE;AAAA,IACF;AAEA,kCAAyB,CAAC,WAA4C;AA53BxE;AA63BI,YAAM,WAAW,MAAG;AA73BxB,YAAAA;AA63B2B,gBAAAA,MAAA,KAAK,YAAL,gBAAAA,IAAc,uBAAuB;AAAA;AAC5D,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,eAAK,cAAS,MAAT,mBAAY,MAAM,MAAM;AAAA,QAQ7B;AAAA,MACF,OAAO;AACL,aAAK,oBAAoB,IAAI,0BAA0B,QAAQ;AAAA,MACjE;AAAA,IACF;AAIA;AAAA;AAAA;AAAA,uCAA8B,OAAO,WAA6D;AAChG,iBAAW,+BAA+B,4CAA4C;AACtF,YAAM,WAAW,MAAG;AAj5BxB;AAi5B2B,0BAAK,YAAL,mBAAc,4BAA4B;AAAA;AACjE,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,+BAA+B,QAAQ;AAAA,MACtE;AAAA,IACF;AAEA,sCAA6B,CAC3B,YACA,WACS;AA55Bb;AA65BI,YAAM,WAAW,MAAG;AA75BxB,YAAAA;AA65B2B,gBAAAA,MAAA,KAAK,YAAL,gBAAAA,IAAc,2BAA2B,YAAY;AAAA;AAC5E,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,eAAK,cAAS,MAAT,mBAAY,MAAM,MAAM;AAAA,QAQ7B;AAAA,MACF,OAAO;AACL,aAAK,oBAAoB,IAAI,8BAA8B,QAAQ;AAAA,MACrE;AAAA,IACF;AAEA,uCAA8B,OAAO,WAA6D;AAChG,YAAM,WAAW,MAAG;AA96BxB;AA86B2B,0BAAK,YAAL,mBAAc,4BAA4B;AAAA;AACjE,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,+BAA+B,QAAQ;AAAA,MACtE;AAAA,IACF;AAEA,oCAA2B,OAAO,WAA0D;AAC1F,YAAM,WAAW,MAAG;AAv7BxB;AAu7B2B,0BAAK,YAAL,mBAAc,yBAAyB;AAAA;AAC9D,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,4BAA4B,QAAQ;AAAA,MACnE;AAAA,IACF;AAEA,wCAA+B,OAC7B,WAC6C;AAC7C,YAAM,UAAU,MAAM,sBAAK,oCAAL;AACtB,aAAO,QAAQ,6BAA6B,MAAM;AAAA,IACpD;AAEA,8BAAqB,OAAO,WAA2E;AACrG,YAAM,WAAW,MAAG;AAv8BxB;AAu8B2B,0BAAK,YAAL,mBAAc,mBAAmB;AAAA;AACxD,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,sBAAsB,QAAQ;AAAA,MAC7D;AAAA,IACF;AAEA,sCAA6B,YAA8D;AACzF,YAAM,WAAW,MAAG;AAh9BxB;AAg9B2B,0BAAK,YAAL,mBAAc;AAAA;AACrC,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,8BAA8B,QAAQ;AAAA,MACrE;AAAA,IACF;AAEA,2BAAkB,OAAO,mBAAiE;AACxF,YAAM,WAAW,MAAG;AAz9BxB;AAy9B2B,0BAAK,YAAL,mBAAc,gBAAgB;AAAA;AACrD,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,mBAAmB,QAAQ;AAAA,MAC1D;AAAA,IACF;AAEA,mBAAmB,OACjB,0BACA,YACkB;AAClB,YAAM,WAAW,MAAG;AAr+BxB;AAq+B2B,0BAAK,YAAL,mBAAc,QAAQ,0BAAiC;AAAA;AAC9E,UAAI,KAAK,WAAW,mBAAK,UAAS;AAChC,eAAO,SAAS;AAAA,MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,WAAW,QAAQ;AAAA,MAClD;AAAA,IACF;AAvwBE,UAAM,EAAE,QAAQ,MAAM,aAAa,eAAe,IAAI,WAAW,CAAC;AAClE,uBAAK,cAAe;AACpB,uBAAK,iBAAkB;AACvB,uBAAK,WAAY,mCAAS;AAC1B,uBAAK,SAAU,mCAAS;AACxB,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,SAAK,OAAO,UAAU,IAAI,YAAY;AACtC,SAAK,KAAK,YAAY;AAAA,EACxB;AAAA,EA3DA,IAAI,iBAAqC;AACvC,WAAO,mBAAK;AAAA,EACd;AAAA,EAEA,IAAI,SAAkB;AACpB,WAAO,mBAAK;AAAA,EACd;AAAA,EAIA,OAAO,oBAAoB,SAAiC;AAK1D,QAAI,CAAC,UAAU,KAAK,CAAC,mBAAK,cAAc,QAAQ,SAAS,mBAAK,WAAU,UAAU,QAAQ,OAAQ;AAChG,yBAAK,WAAY,IAAI,iBAAgB,OAAO;AAAA,IAC9C;AACA,WAAO,mBAAK;AAAA,EACd;AAAA,EAEA,OAAO,gBAAgB;AACrB,uBAAK,WAAY;AAAA,EACnB;AAAA,EAEA,IAAI,SAAiB;AAGnB,QAAI,OAAO,WAAW,eAAe,OAAO,UAAU;AACpD,aAAO,gBAAgB,mBAAK,UAAS,IAAI,IAAI,OAAO,SAAS,IAAI,GAAG,EAAE;AAAA,IACxE;AACA,QAAI,OAAO,mBAAK,aAAY,YAAY;AACtC,YAAM,IAAI,MAAM,6CAA6C;AAAA,IAC/D;AACA,WAAO,mBAAK,YAAW;AAAA,EACzB;AAAA,EAEA,IAAI,WAAmB;AAGrB,QAAI,OAAO,WAAW,eAAe,OAAO,UAAU;AACpD,aAAO,gBAAgB,mBAAK,YAAW,IAAI,IAAI,OAAO,SAAS,IAAI,GAAG,EAAE;AAAA,IAC1E;AACA,QAAI,OAAO,mBAAK,eAAc,YAAY;AACxC,YAAM,IAAI,MAAM,6CAA6C;AAAA,IAC/D;AACA,WAAO,mBAAK,cAAa;AAAA,EAC3B;AAAA,EAcA,IAAI,cAAuC;AA/O7C;AAgPI,aAAO,UAAK,YAAL,mBAAc,gBAAe,KAAK,QAAQ,eAAe;AAAA,EAClE;AAAA,EAEA,IAAI,eAAyC;AAnP/C;AAoPI,YAAO,UAAK,YAAL,mBAAc;AAAA,EACvB;AAAA,EAEA,IAAI,cAAsB;AAvP5B;AAwPI,aAAO,UAAK,YAAL,mBAAc,gBAAe,mBAAK,iBAAgB;AAAA,EAC3D;AAAA,EAEA,IAAI,oBAA6B;AA3PnC;AA4PI,aAAO,UAAK,YAAL,mBAAc,sBAAqB,KAAK,QAAQ,mBAAmB;AAAA,EAC5E;AAAA,EAEA,IAAI,cAAuB;AAGzB,QAAI,OAAO,WAAW,eAAe,OAAO,UAAU;AACpD,aAAO,gBAAgB,KAAK,QAAQ,aAAa,IAAI,IAAI,OAAO,SAAS,IAAI,GAAG,KAAK;AAAA,IACvF;AACA,QAAI,OAAO,KAAK,QAAQ,gBAAgB,YAAY;AAClD,YAAM,IAAI,MAAM,6CAA6C;AAAA,IAC/D;AACA,WAAO;AAAA,EACT;AAAA,EAqFA,MAAM,cAAwE;AA9VhF;AA+VI,QAAI,KAAK,SAAS,aAAa,mBAAK,UAAS;AAC3C;AAAA,IACF;AAYA,QAAI,OAAO,WAAW,aAAa;AACjC,aAAO,uBAAuB,KAAK;AACnC,aAAO,0BAA0B,KAAK;AACtC,aAAO,oBAAoB,KAAK;AAChC,aAAO,iBAAiB,KAAK;AAAA,IAC/B;AAEA,QAAI;AACF,UAAI,KAAK,OAAO;AAEd,YAAI;AAEJ,YAAI,cAAwE,KAAK,KAAK,GAAG;AAEvF,cAAI,IAAI,KAAK,MAAM,KAAK,kBAAkB,KAAK,eAAe,IAAI;AAAA,YAChE,UAAU,KAAK;AAAA,YACf,QAAQ,KAAK;AAAA,UACf,CAAQ;AACR,gBAAM,EAAE,KAAK,KAAK,OAAO;AAAA,QAC3B,OAAO;AAEL,cAAI,KAAK;AAET,cAAI,CAAC,EAAE,QAAQ,GAAG;AAChB,kBAAM,EAAE,KAAK,KAAK,OAAO;AAAA,UAC3B;AAAA,QACF;AAEA,eAAO,QAAQ;AAAA,MACjB,OAAO;AAEL,YAAI,CAAC,OAAO,OAAO;AACjB,gBAAM,kBAAkB;AAAA,YACtB,GAAG,KAAK;AAAA,YACR,aAAa,KAAK;AAAA,YAClB,gBAAgB,KAAK;AAAA,YACrB,UAAU,KAAK;AAAA,YACf,QAAQ,KAAK;AAAA,UACf,CAAC;AAAA,QACH;AAEA,YAAI,CAAC,OAAO,OAAO;AACjB,gBAAM,IAAI,MAAM,+DAA+D;AAAA,QACjF;AAEA,cAAM,OAAO,MAAM,KAAK,KAAK,OAAO;AAAA,MACtC;AAEA,aAAO,MAAM,eAAc,UAAK,QAAQ,gBAAb,YAA4B,EAAE,MAAM,sBAAc,SAAS,SAAgB;AAEtG,YAAI,YAAO,UAAP,mBAAc,aAAU,YAAO,UAAP,mBAAc,YAAW;AACnD,eAAO,KAAK,eAAe,OAAO,KAAK;AAAA,MACzC;AACA;AAAA,IACF,SAAS,KAAK;AACZ,YAAM,QAAQ;AAId,UAAI,QAAQ,IAAI,aAAa,cAAc;AACzC,gBAAQ,MAAM,MAAM,SAAS,MAAM,WAAW,KAAK;AAAA,MACrD,OAAO;AACL,cAAM;AAAA,MACR;AACA;AAAA,IACF;AAAA,EACF;AAAA,EA2EA,IAAI,UAA8B;AA1fpC;AA2fI,YAAO,UAAK,YAAL,mBAAc;AAAA,EACvB;AAAA,EAEA,IAAI,SAAqC;AACvC,QAAI,KAAK,SAAS;AAChB,aAAO,KAAK,QAAQ;AAAA,IAEtB,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EAEA,IAAI,UAAoD;AACtD,QAAI,KAAK,SAAS;AAChB,aAAO,KAAK,QAAQ;AAAA,IACtB,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EAEA,IAAI,OAAwC;AAC1C,QAAI,KAAK,SAAS;AAChB,aAAO,KAAK,QAAQ;AAAA,IACtB,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EAEA,IAAI,eAAwD;AAC1D,QAAI,KAAK,SAAS;AAChB,aAAO,KAAK,QAAQ;AAAA,IACtB,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EAEA,IAAI,0BAA+B;AACjC,QAAI,KAAK,SAAS;AAChB,aAAQ,KAAK,QAAgB;AAAA,IAE/B,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EAEA,8BAA8B,MAAiB;AAC7C,QAAI,KAAK,WAAW,gCAAgC,KAAK,SAAS;AAChE,MAAC,KAAK,QAAgB,2BAA2B,IAAI;AAAA,IACvD,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AA8bF;AAh0BE;AACA;AACA;AACA;AACA;AAUO;AA2JP;AAAA,oBAAe,WAAiD;AAC9D,SAAO,IAAI,QAA6C,aAAW;AACjE,QAAI,mBAAK,UAAS;AAChB,cAAQ,KAAK,OAAQ;AAAA,IACvB;AACA,SAAK,YAAY,MAAM,QAAQ,KAAK,OAAQ,CAAC;AAAA,EAC/C,CAAC;AACH;AAlKA,aApCmB,kBAoCZ,WAAP;AApCF,IAAqB,kBAArB;", "names": ["_a"]}