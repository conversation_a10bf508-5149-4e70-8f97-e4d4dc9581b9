import "./chunk-XTU7I5IS.js";
import {
  MagicLinkErrorCode,
  EmailLinkErrorCode,
  isClerkAPIResponseError,
  isKnownError,
  isMetamaskError,
  isMagicLinkError,
  isEmailLinkError
} from "@clerk/shared/error";
const noFrontendApiError = "Clerk: You must add the frontendApi prop to your <ClerkProvider>";
const noClerkProviderError = "Clerk: You must wrap your application in a <ClerkProvider> component.";
const noGuaranteedLoadedError = (hookName) => `Clerk: You're calling ${hookName} before there's a guarantee the client has been loaded. Call ${hookName} from a child of <SignedIn>, <SignedOut>, or <ClerkLoaded>, or use the withClerk() HOC.`;
const noGuaranteedUserError = (hookName) => `Clerk: You're calling ${hookName} before there's a guarantee there's an active user. Call ${hookName} from a child of <SignedIn> or use the withUser() HOC.`;
const multipleClerkProvidersError = "Clerk: You've added multiple <ClerkProvider> components in your React component tree. Wrap your components in a single <ClerkProvider>.";
const hocChildrenNotAFunctionError = "Clerk: Child of WithClerk must be a function.";
const multipleChildrenInButtonComponent = (name) => `Clerk: You've passed multiple children components to <${name}/>. You can only pass a single child component or text.`;
const invalidStateError = "Clerk: Invalid state. Feel free to submit a bug or reach out to support here: https://clerk.com/support";
const unsupportedNonBrowserDomainOrProxyUrlFunction = "Clerk: Unsupported usage of isSatellite, domain or proxyUrl. The usage of isSatellite, domain or proxyUrl as function is not supported in non-browser environments.";
const userProfilePageRenderedError = "Clerk: <UserProfile.Page /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.";
const userProfileLinkRenderedError = "Clerk: <UserProfile.Link /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.";
const organizationProfilePageRenderedError = "Clerk: <OrganizationProfile.Page /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.";
const organizationProfileLinkRenderedError = "Clerk: <OrganizationProfile.Link /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.";
const customPagesIgnoredComponent = (componentName) => `Clerk: <${componentName} /> can only accept <${componentName}.Page /> and <${componentName}.Link /> as its children. Any other provided component will be ignored.`;
const customPageWrongProps = (componentName) => `Clerk: Missing props. <${componentName}.Page /> component requires the following props: url, label, labelIcon, alongside with children to be rendered inside the page.`;
const customLinkWrongProps = (componentName) => `Clerk: Missing props. <${componentName}.Link /> component requires the following props: url, label and labelIcon.`;
const useAuthHasRequiresRoleOrPermission = 'Clerk: Missing parameters. `has` from `useAuth` requires a permission or role key to be passed. Example usage: `has({permission: "org:posts:edit"`';
export {
  EmailLinkErrorCode,
  MagicLinkErrorCode,
  customLinkWrongProps,
  customPageWrongProps,
  customPagesIgnoredComponent,
  hocChildrenNotAFunctionError,
  invalidStateError,
  isClerkAPIResponseError,
  isEmailLinkError,
  isKnownError,
  isMagicLinkError,
  isMetamaskError,
  multipleChildrenInButtonComponent,
  multipleClerkProvidersError,
  noClerkProviderError,
  noFrontendApiError,
  noGuaranteedLoadedError,
  noGuaranteedUserError,
  organizationProfileLinkRenderedError,
  organizationProfilePageRenderedError,
  unsupportedNonBrowserDomainOrProxyUrlFunction,
  useAuthHasRequiresRoleOrPermission,
  userProfileLinkRenderedError,
  userProfilePageRenderedError
};
//# sourceMappingURL=errors.js.map