{"version": 3, "sources": ["../../src/index.ts"], "sourcesContent": ["import './polyfills';\n\nexport * from './contexts';\nexport * from './components';\nexport * from './hooks';\nexport type {\n  BrowserClerk,\n  ClerkProp,\n  HeadlessBrowserClerk,\n  WithUserProp,\n  WithClerkProp,\n  WithSessionProp,\n  IsomorphicClerkOptions,\n} from './types';\nexport {\n  MagicLinkErrorCode,\n  EmailLinkErrorCode,\n  isClerkAPIResponseError,\n  isKnownError,\n  isMetamaskError,\n  isMagicLinkError,\n  isEmailLinkError,\n} from './errors';\nexport { useMagicLink } from './hooks/useMagicLink';\nexport { useEmailLink } from './hooks/useEmailLink';\n"], "mappings": ";AAAA,OAAO;AAEP,cAAc;AACd,cAAc;AACd,cAAc;AAUd;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP,SAAS,oBAAoB;AAC7B,SAAS,oBAAoB;", "names": []}