"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, "default"), secondTarget && __copyProps(secondTarget, mod, "default"));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var src_exports = {};
__export(src_exports, {
  EmailLinkErrorCode: () => import_errors.EmailLinkErrorCode,
  MagicLinkErrorCode: () => import_errors.MagicLinkErrorCode,
  isClerkAPIResponseError: () => import_errors.isClerkAPIResponseError,
  isEmailLinkError: () => import_errors.isEmailLinkError,
  isKnownError: () => import_errors.isKnownError,
  isMagicLinkError: () => import_errors.isMagicLinkError,
  isMetamaskError: () => import_errors.isMetamaskError,
  useEmailLink: () => import_useEmailLink.useEmailLink,
  useMagicLink: () => import_useMagicLink.useMagicLink
});
module.exports = __toCommonJS(src_exports);
var import_polyfills = require("./polyfills");
__reExport(src_exports, require("./contexts"), module.exports);
__reExport(src_exports, require("./components"), module.exports);
__reExport(src_exports, require("./hooks"), module.exports);
var import_errors = require("./errors");
var import_useMagicLink = require("./hooks/useMagicLink");
var import_useEmailLink = require("./hooks/useEmailLink");
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  EmailLinkErrorCode,
  MagicLinkErrorCode,
  isClerkAPIResponseError,
  isEmailLinkError,
  isKnownError,
  isMagicLinkError,
  isMetamaskError,
  useEmailLink,
  useMagicLink,
  ...require("./contexts"),
  ...require("./components"),
  ...require("./hooks")
});
//# sourceMappingURL=index.js.map