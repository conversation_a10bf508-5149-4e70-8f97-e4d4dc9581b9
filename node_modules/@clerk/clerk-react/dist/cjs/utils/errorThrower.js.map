{"version": 3, "sources": ["../../../src/utils/errorThrower.ts"], "sourcesContent": ["import type { ErrorThrowerOptions } from '@clerk/shared/error';\nimport { buildErrorThrower } from '@clerk/shared/error';\n\nconst errorThrower = buildErrorThrower({ packageName: '@clerk/react' });\n\nfunction __internal__setErrorThrowerOptions(options: ErrorThrowerOptions) {\n  errorThrower.setMessages(options).setPackageName(options);\n}\n\nexport { errorThrower, __internal__setErrorThrowerOptions };\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,mBAAkC;AAElC,MAAM,mBAAe,gCAAkB,EAAE,aAAa,eAAe,CAAC;AAEtE,SAAS,mCAAmC,SAA8B;AACxE,eAAa,YAAY,OAAO,EAAE,eAAe,OAAO;AAC1D;", "names": []}