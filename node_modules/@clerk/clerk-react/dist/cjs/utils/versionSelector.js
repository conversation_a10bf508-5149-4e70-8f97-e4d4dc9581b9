"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var versionSelector_exports = {};
__export(versionSelector_exports, {
  versionSelector: () => versionSelector
});
module.exports = __toCommonJS(versionSelector_exports);
const versionSelector = (clerkJSVersion) => {
  if (clerkJSVersion) {
    return clerkJSVersion;
  }
  const prereleaseTag = getPrereleaseTag("4.32.5");
  if (prereleaseTag) {
    if (prereleaseTag === "snapshot") {
      return "4.73.14";
    }
    return prereleaseTag;
  }
  return getMajorVersion("4.32.5");
};
const getPrereleaseTag = (packageVersion) => {
  var _a;
  return (_a = packageVersion.match(/-(.*)\./)) == null ? void 0 : _a[1];
};
const getMajorVersion = (packageVersion) => packageVersion.split(".")[0];
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  versionSelector
});
//# sourceMappingURL=versionSelector.js.map