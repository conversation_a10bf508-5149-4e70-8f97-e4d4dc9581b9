"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var import_react = require("@testing-library/react");
var import_react2 = __toESM(require("react"));
var import_useMaxAllowedInstancesGuard = require("../useMaxAllowedInstancesGuard");
const originalError = console.error;
const ERR = "usedMoreThanOnceError";
describe("Max allowed instances Hook & Hoc", () => {
  beforeAll(() => {
    console.error = jest.fn();
  });
  afterAll(() => {
    console.error = originalError;
  });
  describe("useMaxAllowedInstancesGuard()", () => {
    const TestingComponent = () => {
      (0, import_useMaxAllowedInstancesGuard.useMaxAllowedInstancesGuard)("TestingComponent", ERR);
      return /* @__PURE__ */ import_react2.default.createElement("div", null, "hello");
    };
    it("renders normally if not used more than N times", () => {
      expect(() => {
        (0, import_react.render)(
          /* @__PURE__ */ import_react2.default.createElement("div", null, /* @__PURE__ */ import_react2.default.createElement(TestingComponent, null))
        );
      }).not.toThrowError(ERR);
    });
    it("throws an error if component is used more than N times", () => {
      expect(() => {
        (0, import_react.render)(
          /* @__PURE__ */ import_react2.default.createElement("div", null, /* @__PURE__ */ import_react2.default.createElement(TestingComponent, null), /* @__PURE__ */ import_react2.default.createElement(TestingComponent, null))
        );
      }).toThrowError(ERR);
    });
  });
  describe("withMaxAllowedInstancesGuard()", () => {
    const TestingComponentBase = () => {
      (0, import_useMaxAllowedInstancesGuard.useMaxAllowedInstancesGuard)("TestingComponent", ERR);
      return /* @__PURE__ */ import_react2.default.createElement("div", null, "hello");
    };
    const TestingComp = (0, import_useMaxAllowedInstancesGuard.withMaxAllowedInstancesGuard)(TestingComponentBase, "TestingComp", ERR);
    it("renders normally if not used more than N times", () => {
      expect(() => {
        (0, import_react.render)(
          /* @__PURE__ */ import_react2.default.createElement("div", null, /* @__PURE__ */ import_react2.default.createElement(TestingComp, null))
        );
      }).not.toThrowError(ERR);
    });
    it("throws an error if component is used more than N times", () => {
      expect(() => {
        (0, import_react.render)(
          /* @__PURE__ */ import_react2.default.createElement("div", null, /* @__PURE__ */ import_react2.default.createElement(TestingComp, null), /* @__PURE__ */ import_react2.default.createElement(TestingComp, null))
        );
      }).toThrowError(ERR);
    });
  });
});
//# sourceMappingURL=useMaxAllowedInstancesGuard.test.js.map