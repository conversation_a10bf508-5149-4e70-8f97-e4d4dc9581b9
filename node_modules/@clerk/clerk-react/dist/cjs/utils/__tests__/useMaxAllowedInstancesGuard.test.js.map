{"version": 3, "sources": ["../../../../src/utils/__tests__/useMaxAllowedInstancesGuard.test.tsx"], "sourcesContent": ["import { render } from '@testing-library/react';\nimport React from 'react';\n\nimport { useMaxAllowedInstancesGuard, withMaxAllowedInstancesGuard } from '../useMaxAllowedInstancesGuard';\n\nconst originalError = console.error;\nconst ERR = 'usedMoreThanOnceError';\n\ndescribe('Max allowed instances Hook & Hoc', () => {\n  beforeAll(() => {\n    console.error = jest.fn();\n  });\n\n  afterAll(() => {\n    console.error = originalError;\n  });\n\n  describe('useMaxAllowedInstancesGuard()', () => {\n    const TestingComponent = () => {\n      useMaxAllowedInstancesGuard('TestingComponent', ERR);\n      return <div>hello</div>;\n    };\n\n    it('renders normally if not used more than N times', () => {\n      expect(() => {\n        render(\n          <div>\n            <TestingComponent />\n          </div>,\n        );\n      }).not.toThrowError(ERR);\n    });\n\n    it('throws an error if component is used more than N times', () => {\n      expect(() => {\n        render(\n          <div>\n            <TestingComponent />\n            <TestingComponent />\n          </div>,\n        );\n      }).toThrowError(ERR);\n    });\n  });\n\n  describe('withMaxAllowedInstancesGuard()', () => {\n    const TestingComponentBase = () => {\n      useMaxAllowedInstancesGuard('TestingComponent', ERR);\n      return <div>hello</div>;\n    };\n\n    const TestingComp = withMaxAllowedInstancesGuard(TestingComponentBase, 'TestingComp', ERR);\n\n    it('renders normally if not used more than N times', () => {\n      expect(() => {\n        render(\n          <div>\n            <TestingComp />\n          </div>,\n        );\n      }).not.toThrowError(ERR);\n    });\n\n    it('throws an error if component is used more than N times', () => {\n      expect(() => {\n        render(\n          <div>\n            <TestingComp />\n            <TestingComp />\n          </div>,\n        );\n      }).toThrowError(ERR);\n    });\n  });\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,mBAAuB;AACvB,IAAAA,gBAAkB;AAElB,yCAA0E;AAE1E,MAAM,gBAAgB,QAAQ;AAC9B,MAAM,MAAM;AAEZ,SAAS,oCAAoC,MAAM;AACjD,YAAU,MAAM;AACd,YAAQ,QAAQ,KAAK,GAAG;AAAA,EAC1B,CAAC;AAED,WAAS,MAAM;AACb,YAAQ,QAAQ;AAAA,EAClB,CAAC;AAED,WAAS,iCAAiC,MAAM;AAC9C,UAAM,mBAAmB,MAAM;AAC7B,0EAA4B,oBAAoB,GAAG;AACnD,aAAO,8BAAAC,QAAA,cAAC,aAAI,OAAK;AAAA,IACnB;AAEA,OAAG,kDAAkD,MAAM;AACzD,aAAO,MAAM;AACX;AAAA,UACE,8BAAAA,QAAA,cAAC,aACC,8BAAAA,QAAA,cAAC,sBAAiB,CACpB;AAAA,QACF;AAAA,MACF,CAAC,EAAE,IAAI,aAAa,GAAG;AAAA,IACzB,CAAC;AAED,OAAG,0DAA0D,MAAM;AACjE,aAAO,MAAM;AACX;AAAA,UACE,8BAAAA,QAAA,cAAC,aACC,8BAAAA,QAAA,cAAC,sBAAiB,GAClB,8BAAAA,QAAA,cAAC,sBAAiB,CACpB;AAAA,QACF;AAAA,MACF,CAAC,EAAE,aAAa,GAAG;AAAA,IACrB,CAAC;AAAA,EACH,CAAC;AAED,WAAS,kCAAkC,MAAM;AAC/C,UAAM,uBAAuB,MAAM;AACjC,0EAA4B,oBAAoB,GAAG;AACnD,aAAO,8BAAAA,QAAA,cAAC,aAAI,OAAK;AAAA,IACnB;AAEA,UAAM,kBAAc,iEAA6B,sBAAsB,eAAe,GAAG;AAEzF,OAAG,kDAAkD,MAAM;AACzD,aAAO,MAAM;AACX;AAAA,UACE,8BAAAA,QAAA,cAAC,aACC,8BAAAA,QAAA,cAAC,iBAAY,CACf;AAAA,QACF;AAAA,MACF,CAAC,EAAE,IAAI,aAAa,GAAG;AAAA,IACzB,CAAC;AAED,OAAG,0DAA0D,MAAM;AACjE,aAAO,MAAM;AACX;AAAA,UACE,8BAAAA,QAAA,cAAC,aACC,8BAAAA,QAAA,cAAC,iBAAY,GACb,8BAAAA,QAAA,cAAC,iBAAY,CACf;AAAA,QACF;AAAA,MACF,CAAC,EAAE,aAAa,GAAG;AAAA,IACrB,CAAC;AAAA,EACH,CAAC;AACH,CAAC;", "names": ["import_react", "React"]}