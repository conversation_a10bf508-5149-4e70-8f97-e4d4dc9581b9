"use strict";
var import_versionSelector = require("../versionSelector");
describe("versionSelector", () => {
  it("should return the clerkJSVersion if it is provided", () => {
    expect((0, import_versionSelector.versionSelector)("1.0.0")).toEqual("1.0.0");
  });
  it("should use the major version if there is no prerelease tag", () => {
    global.PACKAGE_VERSION = "1.0.0";
    global.JS_PACKAGE_VERSION = "2.0.0";
    expect((0, import_versionSelector.versionSelector)(void 0)).toEqual("1");
  });
  it("should use the prerelease tag when it is not snapshot", () => {
    global.PACKAGE_VERSION = "1.0.0-next.0";
    global.JS_PACKAGE_VERSION = "2.0.0-next.0";
    expect((0, import_versionSelector.versionSelector)(void 0)).toEqual("next");
  });
  it("should use the exact JS version if tag is snapshot", () => {
    global.PACKAGE_VERSION = "1.0.0-snapshot.0";
    global.JS_PACKAGE_VERSION = "2.0.0-snapshot.0";
    expect((0, import_versionSelector.versionSelector)(void 0)).toEqual("2.0.0-snapshot.0");
  });
});
//# sourceMappingURL=versionSelector.test.js.map