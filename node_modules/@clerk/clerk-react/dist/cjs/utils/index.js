"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, "default"), secondTarget && __copyProps(secondTarget, mod, "default"));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var utils_exports = {};
__export(utils_exports, {
  loadClerkJsScript: () => import_loadClerkJsScript.loadClerkJsScript
});
module.exports = __toCommonJS(utils_exports);
__reExport(utils_exports, require("./childrenUtils"), module.exports);
__reExport(utils_exports, require("./errorThrower"), module.exports);
__reExport(utils_exports, require("./isConstructor"), module.exports);
var import_loadClerkJsScript = require("./loadClerkJsScript");
__reExport(utils_exports, require("./useMaxAllowedInstancesGuard"), module.exports);
__reExport(utils_exports, require("./useCustomElementPortal"), module.exports);
__reExport(utils_exports, require("./useCustomPages"), module.exports);
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  loadClerkJsScript,
  ...require("./childrenUtils"),
  ...require("./errorThrower"),
  ...require("./isConstructor"),
  ...require("./useMaxAllowedInstancesGuard"),
  ...require("./useCustomElementPortal"),
  ...require("./useCustomPages")
});
//# sourceMappingURL=index.js.map