"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var useCustomPages_exports = {};
__export(useCustomPages_exports, {
  useOrganizationProfileCustomPages: () => useOrganizationProfileCustomPages,
  useUserProfileCustomPages: () => useUserProfileCustomPages
});
module.exports = __toCommonJS(useCustomPages_exports);
var import_shared = require("@clerk/shared");
var import_react = __toESM(require("react"));
var import_uiComponents = require("../components/uiComponents");
var import_errors = require("../errors");
var import_useCustomElementPortal = require("./useCustomElementPortal");
const isThatComponent = (v, component) => {
  return !!v && import_react.default.isValidElement(v) && (v == null ? void 0 : v.type) === component;
};
const useUserProfileCustomPages = (children) => {
  const reorderItemsLabels = ["account", "security"];
  return useCustomPages({
    children,
    reorderItemsLabels,
    LinkComponent: import_uiComponents.UserProfileLink,
    PageComponent: import_uiComponents.UserProfilePage,
    componentName: "UserProfile"
  });
};
const useOrganizationProfileCustomPages = (children) => {
  const reorderItemsLabels = ["members", "settings"];
  return useCustomPages({
    children,
    reorderItemsLabels,
    LinkComponent: import_uiComponents.OrganizationProfileLink,
    PageComponent: import_uiComponents.OrganizationProfilePage,
    componentName: "OrganizationProfile"
  });
};
const useCustomPages = ({
  children,
  LinkComponent,
  PageComponent,
  reorderItemsLabels,
  componentName
}) => {
  const validChildren = [];
  import_react.default.Children.forEach(children, (child) => {
    if (!isThatComponent(child, PageComponent) && !isThatComponent(child, LinkComponent)) {
      if (child) {
        (0, import_shared.logErrorInDevMode)((0, import_errors.customPagesIgnoredComponent)(componentName));
      }
      return;
    }
    const { props } = child;
    const { children: children2, label, url, labelIcon } = props;
    if (isThatComponent(child, PageComponent)) {
      if (isReorderItem(props, reorderItemsLabels)) {
        validChildren.push({ label });
      } else if (isCustomPage(props)) {
        validChildren.push({ label, labelIcon, children: children2, url });
      } else {
        (0, import_shared.logErrorInDevMode)((0, import_errors.customPageWrongProps)(componentName));
        return;
      }
    }
    if (isThatComponent(child, LinkComponent)) {
      if (isExternalLink(props)) {
        validChildren.push({ label, labelIcon, url });
      } else {
        (0, import_shared.logErrorInDevMode)((0, import_errors.customLinkWrongProps)(componentName));
        return;
      }
    }
  });
  const customPageContents = [];
  const customPageLabelIcons = [];
  const customLinkLabelIcons = [];
  validChildren.forEach((cp, index) => {
    if (isCustomPage(cp)) {
      customPageContents.push({ component: cp.children, id: index });
      customPageLabelIcons.push({ component: cp.labelIcon, id: index });
      return;
    }
    if (isExternalLink(cp)) {
      customLinkLabelIcons.push({ component: cp.labelIcon, id: index });
    }
  });
  const customPageContentsPortals = (0, import_useCustomElementPortal.useCustomElementPortal)(customPageContents);
  const customPageLabelIconsPortals = (0, import_useCustomElementPortal.useCustomElementPortal)(customPageLabelIcons);
  const customLinkLabelIconsPortals = (0, import_useCustomElementPortal.useCustomElementPortal)(customLinkLabelIcons);
  const customPages = [];
  const customPagesPortals = [];
  validChildren.forEach((cp, index) => {
    if (isReorderItem(cp, reorderItemsLabels)) {
      customPages.push({ label: cp.label });
      return;
    }
    if (isCustomPage(cp)) {
      const {
        portal: contentPortal,
        mount,
        unmount
      } = customPageContentsPortals.find((p) => p.id === index);
      const {
        portal: labelPortal,
        mount: mountIcon,
        unmount: unmountIcon
      } = customPageLabelIconsPortals.find((p) => p.id === index);
      customPages.push({ label: cp.label, url: cp.url, mount, unmount, mountIcon, unmountIcon });
      customPagesPortals.push(contentPortal);
      customPagesPortals.push(labelPortal);
      return;
    }
    if (isExternalLink(cp)) {
      const {
        portal: labelPortal,
        mount: mountIcon,
        unmount: unmountIcon
      } = customLinkLabelIconsPortals.find((p) => p.id === index);
      customPages.push({ label: cp.label, url: cp.url, mountIcon, unmountIcon });
      customPagesPortals.push(labelPortal);
      return;
    }
  });
  return { customPages, customPagesPortals };
};
const isReorderItem = (childProps, validItems) => {
  const { children, label, url, labelIcon } = childProps;
  return !children && !url && !labelIcon && validItems.some((v) => v === label);
};
const isCustomPage = (childProps) => {
  const { children, label, url, labelIcon } = childProps;
  return !!children && !!url && !!labelIcon && !!label;
};
const isExternalLink = (childProps) => {
  const { children, label, url, labelIcon } = childProps;
  return !children && !!url && !!labelIcon && !!label;
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  useOrganizationProfileCustomPages,
  useUserProfileCustomPages
});
//# sourceMappingURL=useCustomPages.js.map