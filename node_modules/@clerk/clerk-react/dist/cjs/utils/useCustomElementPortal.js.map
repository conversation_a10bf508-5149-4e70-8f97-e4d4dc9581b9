{"version": 3, "sources": ["../../../src/utils/useCustomElementPortal.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { createPortal } from 'react-dom';\n\nexport type UseCustomElementPortalParams = {\n  component: React.ReactNode;\n  id: number;\n};\n\nexport type UseCustomElementPortalReturn = {\n  portal: () => JSX.Element;\n  mount: (node: Element) => void;\n  unmount: () => void;\n  id: number;\n};\n\n// This function takes a component as prop, and returns functions that mount and unmount\n// the given component into a given node\nexport const useCustomElementPortal = (elements: UseCustomElementPortalParams[]) => {\n  const initialState = Array(elements.length).fill(null);\n  const [nodes, setNodes] = useState<(Element | null)[]>(initialState);\n\n  return elements.map((el, index) => ({\n    id: el.id,\n    mount: (node: Element) => setNodes(prevState => prevState.map((n, i) => (i === index ? node : n))),\n    unmount: () => setNodes(prevState => prevState.map((n, i) => (i === index ? null : n))),\n    portal: () => <>{nodes[index] ? createPortal(el.component, nodes[index] as Element) : null}</>,\n  }));\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAgC;AAChC,uBAA6B;AAgBtB,MAAM,yBAAyB,CAAC,aAA6C;AAClF,QAAM,eAAe,MAAM,SAAS,MAAM,EAAE,KAAK,IAAI;AACrD,QAAM,CAAC,OAAO,QAAQ,QAAI,uBAA6B,YAAY;AAEnE,SAAO,SAAS,IAAI,CAAC,IAAI,WAAW;AAAA,IAClC,IAAI,GAAG;AAAA,IACP,OAAO,CAAC,SAAkB,SAAS,eAAa,UAAU,IAAI,CAAC,GAAG,MAAO,MAAM,QAAQ,OAAO,CAAE,CAAC;AAAA,IACjG,SAAS,MAAM,SAAS,eAAa,UAAU,IAAI,CAAC,GAAG,MAAO,MAAM,QAAQ,OAAO,CAAE,CAAC;AAAA,IACtF,QAAQ,MAAM,6BAAAA,QAAA,2BAAAA,QAAA,gBAAG,MAAM,KAAK,QAAI,+BAAa,GAAG,WAAW,MAAM,KAAK,CAAY,IAAI,IAAK;AAAA,EAC7F,EAAE;AACJ;", "names": ["React"]}