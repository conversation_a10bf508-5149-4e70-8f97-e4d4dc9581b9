{"version": 3, "sources": ["../../../src/utils/deriveState.ts"], "sourcesContent": ["import type {\n  ActiveSessionResource,\n  InitialState,\n  MembershipRole,\n  OrganizationCustomPermissionKey,\n  OrganizationResource,\n  Resources,\n  UserResource,\n} from '@clerk/types';\n\nexport const deriveState = (clerkLoaded: boolean, state: Resources, initialState: InitialState | undefined) => {\n  if (!clerkLoaded && initialState) {\n    return deriveFromSsrInitialState(initialState);\n  }\n  return deriveFromClientSideState(state);\n};\n\nconst deriveFromSsrInitialState = (initialState: InitialState) => {\n  const userId = initialState.userId;\n  const user = initialState.user as UserResource;\n  const sessionId = initialState.sessionId;\n  const session = initialState.session as ActiveSessionResource;\n  const organization = initialState.organization as OrganizationResource;\n  const orgId = initialState.orgId;\n  const orgRole = initialState.orgRole as MembershipRole;\n  const orgPermissions = initialState.orgPermissions as OrganizationCustomPermissionKey[];\n  const orgSlug = initialState.orgSlug;\n  const actor = initialState.actor;\n\n  return {\n    userId,\n    user,\n    sessionId,\n    session,\n    organization,\n    orgId,\n    orgRole,\n    orgPermissions,\n    orgSlug,\n    actor,\n    lastOrganizationInvitation: null,\n    lastOrganizationMember: null,\n  };\n};\n\nconst deriveFromClientSideState = (state: Resources) => {\n  const userId: string | null | undefined = state.user ? state.user.id : state.user;\n  const user = state.user;\n  const sessionId: string | null | undefined = state.session ? state.session.id : state.session;\n  const session = state.session;\n  const actor = session?.actor;\n  const organization = state.organization;\n  const orgId: string | null | undefined = state.organization ? state.organization.id : state.organization;\n  const orgSlug = organization?.slug;\n  const membership = organization\n    ? user?.organizationMemberships?.find(om => om.organization.id === orgId)\n    : organization;\n  const orgPermissions = membership ? membership.permissions : membership;\n  const orgRole = membership ? membership.role : membership;\n\n  const lastOrganizationInvitation = state.lastOrganizationInvitation;\n  const lastOrganizationMember = state.lastOrganizationMember;\n\n  return {\n    userId,\n    user,\n    sessionId,\n    session,\n    organization,\n    orgId,\n    orgRole,\n    orgSlug,\n    orgPermissions,\n    actor,\n    lastOrganizationInvitation,\n    lastOrganizationMember,\n  };\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAUO,MAAM,cAAc,CAAC,aAAsB,OAAkB,iBAA2C;AAC7G,MAAI,CAAC,eAAe,cAAc;AAChC,WAAO,0BAA0B,YAAY;AAAA,EAC/C;AACA,SAAO,0BAA0B,KAAK;AACxC;AAEA,MAAM,4BAA4B,CAAC,iBAA+B;AAChE,QAAM,SAAS,aAAa;AAC5B,QAAM,OAAO,aAAa;AAC1B,QAAM,YAAY,aAAa;AAC/B,QAAM,UAAU,aAAa;AAC7B,QAAM,eAAe,aAAa;AAClC,QAAM,QAAQ,aAAa;AAC3B,QAAM,UAAU,aAAa;AAC7B,QAAM,iBAAiB,aAAa;AACpC,QAAM,UAAU,aAAa;AAC7B,QAAM,QAAQ,aAAa;AAE3B,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,4BAA4B;AAAA,IAC5B,wBAAwB;AAAA,EAC1B;AACF;AAEA,MAAM,4BAA4B,CAAC,UAAqB;AA7CxD;AA8CE,QAAM,SAAoC,MAAM,OAAO,MAAM,KAAK,KAAK,MAAM;AAC7E,QAAM,OAAO,MAAM;AACnB,QAAM,YAAuC,MAAM,UAAU,MAAM,QAAQ,KAAK,MAAM;AACtF,QAAM,UAAU,MAAM;AACtB,QAAM,QAAQ,mCAAS;AACvB,QAAM,eAAe,MAAM;AAC3B,QAAM,QAAmC,MAAM,eAAe,MAAM,aAAa,KAAK,MAAM;AAC5F,QAAM,UAAU,6CAAc;AAC9B,QAAM,aAAa,gBACf,kCAAM,4BAAN,mBAA+B,KAAK,QAAM,GAAG,aAAa,OAAO,SACjE;AACJ,QAAM,iBAAiB,aAAa,WAAW,cAAc;AAC7D,QAAM,UAAU,aAAa,WAAW,OAAO;AAE/C,QAAM,6BAA6B,MAAM;AACzC,QAAM,yBAAyB,MAAM;AAErC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;", "names": []}