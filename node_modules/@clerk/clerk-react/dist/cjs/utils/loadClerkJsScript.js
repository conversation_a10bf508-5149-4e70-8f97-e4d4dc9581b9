"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var loadClerkJsScript_exports = {};
__export(loadClerkJsScript_exports, {
  loadClerkJsScript: () => loadClerkJsScript
});
module.exports = __toCommonJS(loadClerkJsScript_exports);
var import_keys = require("@clerk/shared/keys");
var import_loadScript = require("@clerk/shared/loadScript");
var import_proxy = require("@clerk/shared/proxy");
var import_url = require("@clerk/shared/url");
var import_errorThrower = require("./errorThrower");
var import_isDevOrStageUrl = require("./isDevOrStageUrl");
var import_versionSelector = require("./versionSelector");
const FAILED_TO_LOAD_ERROR = "Clerk: Failed to load Clerk";
const loadClerkJsScript = (opts) => {
  const { frontendApi, publishableKey } = opts;
  if (!publishableKey && !frontendApi) {
    import_errorThrower.errorThrower.throwMissingPublishableKeyError();
  }
  return (0, import_loadScript.loadScript)(clerkJsScriptUrl(opts), {
    async: true,
    crossOrigin: "anonymous",
    beforeLoad: applyClerkJsScriptAttributes(opts)
  }).catch(() => {
    throw new Error(FAILED_TO_LOAD_ERROR);
  });
};
const clerkJsScriptUrl = (opts) => {
  var _a, _b;
  const { clerkJSUrl, clerkJSVariant, clerkJSVersion, proxyUrl, domain, publishableKey, frontendApi } = opts;
  if (clerkJSUrl) {
    return clerkJSUrl;
  }
  let scriptHost = "";
  if (!!proxyUrl && (0, import_proxy.isValidProxyUrl)(proxyUrl)) {
    scriptHost = (0, import_proxy.proxyUrlToAbsoluteURL)(proxyUrl).replace(/http(s)?:\/\//, "");
  } else if (domain && !(0, import_isDevOrStageUrl.isDevOrStagingUrl)(((_a = (0, import_keys.parsePublishableKey)(publishableKey)) == null ? void 0 : _a.frontendApi) || frontendApi || "")) {
    scriptHost = (0, import_url.addClerkPrefix)(domain);
  } else {
    scriptHost = ((_b = (0, import_keys.parsePublishableKey)(publishableKey)) == null ? void 0 : _b.frontendApi) || frontendApi || "";
  }
  const variant = clerkJSVariant ? `${clerkJSVariant.replace(/\.+$/, "")}.` : "";
  const version = (0, import_versionSelector.versionSelector)(clerkJSVersion);
  return `https://${scriptHost}/npm/@clerk/clerk-js@${version}/dist/clerk.${variant}browser.js`;
};
const applyClerkJsScriptAttributes = (options) => (script) => {
  const { publishableKey, frontendApi, proxyUrl, domain } = options;
  if (publishableKey) {
    script.setAttribute("data-clerk-publishable-key", publishableKey);
  } else if (frontendApi) {
    script.setAttribute("data-clerk-frontend-api", frontendApi);
  }
  if (proxyUrl) {
    script.setAttribute("data-clerk-proxy-url", proxyUrl);
  }
  if (domain) {
    script.setAttribute("data-clerk-domain", domain);
  }
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  loadClerkJsScript
});
//# sourceMappingURL=loadClerkJsScript.js.map