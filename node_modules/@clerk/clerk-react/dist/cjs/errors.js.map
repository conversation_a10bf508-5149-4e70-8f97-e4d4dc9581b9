{"version": 3, "sources": ["../../src/errors.ts"], "sourcesContent": ["export {\n  MagicLinkErrorCode,\n  EmailLinkErrorCode,\n  isClerkAPIResponseError,\n  isKnownError,\n  isMetamaskError,\n  isMagicLinkError,\n  isEmailLinkError,\n} from '@clerk/shared/error';\n\nexport const noFrontendApiError = 'Clerk: You must add the frontendApi prop to your <ClerkProvider>';\n\nexport const noClerkProviderError = 'Clerk: You must wrap your application in a <ClerkProvider> component.';\n\nexport const noGuaranteedLoadedError = (hookName: string) =>\n  `Clerk: You're calling ${hookName} before there's a guarantee the client has been loaded. Call ${hookName} from a child of <SignedIn>, <SignedOut>, or <ClerkLoaded>, or use the withClerk() HOC.`;\n\nexport const noGuaranteedUserError = (hookName: string) =>\n  `Clerk: You're calling ${hookName} before there's a guarantee there's an active user. Call ${hookName} from a child of <SignedIn> or use the withUser() HOC.`;\n\nexport const multipleClerkProvidersError =\n  \"Clerk: You've added multiple <ClerkProvider> components in your React component tree. Wrap your components in a single <ClerkProvider>.\";\n\nexport const hocChildrenNotAFunctionError = 'Clerk: Child of WithClerk must be a function.';\n\nexport const multipleChildrenInButtonComponent = (name: string) =>\n  `Clerk: You've passed multiple children components to <${name}/>. You can only pass a single child component or text.`;\n\nexport const invalidStateError =\n  'Clerk: Invalid state. Feel free to submit a bug or reach out to support here: https://clerk.com/support';\n\nexport const unsupportedNonBrowserDomainOrProxyUrlFunction =\n  'Clerk: Unsupported usage of isSatellite, domain or proxyUrl. The usage of isSatellite, domain or proxyUrl as function is not supported in non-browser environments.';\n\nexport const userProfilePageRenderedError =\n  'Clerk: <UserProfile.Page /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.';\nexport const userProfileLinkRenderedError =\n  'Clerk: <UserProfile.Link /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.';\n\nexport const organizationProfilePageRenderedError =\n  'Clerk: <OrganizationProfile.Page /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.';\nexport const organizationProfileLinkRenderedError =\n  'Clerk: <OrganizationProfile.Link /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.';\n\nexport const customPagesIgnoredComponent = (componentName: string) =>\n  `Clerk: <${componentName} /> can only accept <${componentName}.Page /> and <${componentName}.Link /> as its children. Any other provided component will be ignored.`;\n\nexport const customPageWrongProps = (componentName: string) =>\n  `Clerk: Missing props. <${componentName}.Page /> component requires the following props: url, label, labelIcon, alongside with children to be rendered inside the page.`;\n\nexport const customLinkWrongProps = (componentName: string) =>\n  `Clerk: Missing props. <${componentName}.Link /> component requires the following props: url, label and labelIcon.`;\n\nexport const useAuthHasRequiresRoleOrPermission =\n  'Clerk: Missing parameters. `has` from `useAuth` requires a permission or role key to be passed. Example usage: `has({permission: \"org:posts:edit\"`';\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAQO;AAEA,MAAM,qBAAqB;AAE3B,MAAM,uBAAuB;AAE7B,MAAM,0BAA0B,CAAC,aACtC,yBAAyB,QAAQ,gEAAgE,QAAQ;AAEpG,MAAM,wBAAwB,CAAC,aACpC,yBAAyB,QAAQ,4DAA4D,QAAQ;AAEhG,MAAM,8BACX;AAEK,MAAM,+BAA+B;AAErC,MAAM,oCAAoC,CAAC,SAChD,yDAAyD,IAAI;AAExD,MAAM,oBACX;AAEK,MAAM,gDACX;AAEK,MAAM,+BACX;AACK,MAAM,+BACX;AAEK,MAAM,uCACX;AACK,MAAM,uCACX;AAEK,MAAM,8BAA8B,CAAC,kBAC1C,WAAW,aAAa,wBAAwB,aAAa,iBAAiB,aAAa;AAEtF,MAAM,uBAAuB,CAAC,kBACnC,0BAA0B,aAAa;AAElC,MAAM,uBAAuB,CAAC,kBACnC,0BAA0B,aAAa;AAElC,MAAM,qCACX;", "names": []}