"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var useAuth_exports = {};
__export(useAuth_exports, {
  useAuth: () => useAuth
});
module.exports = __toCommonJS(useAuth_exports);
var import_react = require("react");
var import_AuthContext = require("../contexts/AuthContext");
var import_IsomorphicClerkContext = require("../contexts/IsomorphicClerkContext");
var import_errors = require("../errors");
var import_utils = require("./utils");
const useAuth = () => {
  const { sessionId, userId, actor, orgId, orgRole, orgSlug, orgPermissions } = (0, import_AuthContext.useAuthContext)();
  const isomorphicClerk = (0, import_IsomorphicClerkContext.useIsomorphicClerkContext)();
  const getToken = (0, import_react.useCallback)((0, import_utils.createGetToken)(isomorphicClerk), [isomorphicClerk]);
  const signOut = (0, import_react.useCallback)((0, import_utils.createSignOut)(isomorphicClerk), [isomorphicClerk]);
  const has = (0, import_react.useCallback)(
    (params) => {
      if (!(params == null ? void 0 : params.permission) && !(params == null ? void 0 : params.role)) {
        throw new Error(import_errors.useAuthHasRequiresRoleOrPermission);
      }
      if (!orgId || !userId || !orgRole || !orgPermissions) {
        return false;
      }
      if (params.permission) {
        return orgPermissions.includes(params.permission);
      }
      if (params.role) {
        return orgRole === params.role;
      }
      return false;
    },
    [orgId, orgRole, userId, orgPermissions]
  );
  if (sessionId === void 0 && userId === void 0) {
    return {
      isLoaded: false,
      isSignedIn: void 0,
      sessionId,
      userId,
      actor: void 0,
      orgId: void 0,
      orgRole: void 0,
      orgSlug: void 0,
      has: void 0,
      signOut,
      getToken
    };
  }
  if (sessionId === null && userId === null) {
    return {
      isLoaded: true,
      isSignedIn: false,
      sessionId,
      userId,
      actor: null,
      orgId: null,
      orgRole: null,
      orgSlug: null,
      has: () => false,
      signOut,
      getToken
    };
  }
  if (!!sessionId && !!userId && !!orgId && !!orgRole) {
    return {
      isLoaded: true,
      isSignedIn: true,
      sessionId,
      userId,
      actor: actor || null,
      orgId,
      orgRole,
      orgSlug: orgSlug || null,
      has,
      signOut,
      getToken
    };
  }
  if (!!sessionId && !!userId && !orgId) {
    return {
      isLoaded: true,
      isSignedIn: true,
      sessionId,
      userId,
      actor: actor || null,
      orgId: null,
      orgRole: null,
      orgSlug: null,
      has: () => false,
      signOut,
      getToken
    };
  }
  throw new Error(import_errors.invalidStateError);
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  useAuth
});
//# sourceMappingURL=useAuth.js.map