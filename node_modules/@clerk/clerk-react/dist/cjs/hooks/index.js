"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var hooks_exports = {};
__export(hooks_exports, {
  useAuth: () => import_useAuth.useAuth,
  useClerk: () => import_useClerk.useClerk,
  useEmailLink: () => import_useEmailLink.useEmailLink,
  useMagicLink: () => import_useMagicLink.useMagicLink,
  useOrganization: () => import_useOrganization.useOrganization,
  useOrganizationList: () => import_useOrganizationList.useOrganizationList,
  useOrganizations: () => import_useOrganizations.useOrganizations,
  useSession: () => import_useSession.useSession,
  useSessionList: () => import_useSessionList.useSessionList,
  useSignIn: () => import_useSignIn.useSignIn,
  useSignUp: () => import_useSignUp.useSignUp,
  useUser: () => import_useUser.useUser
});
module.exports = __toCommonJS(hooks_exports);
var import_useUser = require("./useUser");
var import_useAuth = require("./useAuth");
var import_useSession = require("./useSession");
var import_useClerk = require("./useClerk");
var import_useSignIn = require("./useSignIn");
var import_useSignUp = require("./useSignUp");
var import_useSessionList = require("./useSessionList");
var import_useOrganization = require("./useOrganization");
var import_useOrganizationList = require("./useOrganizationList");
var import_useOrganizations = require("./useOrganizations");
var import_useMagicLink = require("./useMagicLink");
var import_useEmailLink = require("./useEmailLink");
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  useAuth,
  useClerk,
  useEmailLink,
  useMagicLink,
  useOrganization,
  useOrganizationList,
  useOrganizations,
  useSession,
  useSessionList,
  useSignIn,
  useSignUp,
  useUser
});
//# sourceMappingURL=index.js.map