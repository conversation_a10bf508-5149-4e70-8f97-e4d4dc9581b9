{"version": 3, "sources": ["../../../src/hooks/useSignUp.ts"], "sourcesContent": ["import type { SetActive, SetSession, SignUpResource } from '@clerk/types';\n\nimport { useClientContext } from '../contexts/ClientContext';\nimport { useIsomorphicClerkContext } from '../contexts/IsomorphicClerkContext';\n\ntype UseSignUpReturn =\n  | {\n      isLoaded: false;\n      signUp: undefined;\n      /**\n       * @deprecated This method is deprecated and will be removed in the future. Use {@link Clerk.setActive} instead\n       * Set the current session explicitly. Setting the session to `null` unsets the active session and signs out the user.\n       * @param session Passed session resource object, session id (string version) or null\n       * @param beforeEmit Callback run just before the active session is set to the passed object. Can be used to hook up for pre-navigation actions.\n       */\n      setSession: undefined;\n      setActive: undefined;\n    }\n  | {\n      isLoaded: true;\n      signUp: SignUpResource;\n      /**\n       * @deprecated This method is deprecated and will be removed in the future. Use {@link Clerk.setActive} instead\n       * Set the current session explicitly. Setting the session to `null` unsets the active session and signs out the user.\n       * @param session Passed session resource object, session id (string version) or null\n       * @param beforeEmit Callback run just before the active session is set to the passed object. Can be used to hook up for pre-navigation actions.\n       */\n      setSession: SetSession;\n      setActive: SetActive;\n    };\n\ntype UseSignUp = () => UseSignUpReturn;\n\nexport const useSignUp: UseSignUp = () => {\n  const isomorphicClerk = useIsomorphicClerkContext();\n  const client = useClientContext();\n\n  if (!client) {\n    return { isLoaded: false, signUp: undefined, setSession: undefined, setActive: undefined };\n  }\n\n  return {\n    isLoaded: true,\n    signUp: client.signUp,\n    setSession: isomorphicClerk.setSession,\n    setActive: isomorphicClerk.setActive,\n  };\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,2BAAiC;AACjC,oCAA0C;AA8BnC,MAAM,YAAuB,MAAM;AACxC,QAAM,sBAAkB,yDAA0B;AAClD,QAAM,aAAS,uCAAiB;AAEhC,MAAI,CAAC,QAAQ;AACX,WAAO,EAAE,UAAU,OAAO,QAAQ,QAAW,YAAY,QAAW,WAAW,OAAU;AAAA,EAC3F;AAEA,SAAO;AAAA,IACL,UAAU;AAAA,IACV,QAAQ,OAAO;AAAA,IACf,YAAY,gBAAgB;AAAA,IAC5B,WAAW,gBAAgB;AAAA,EAC7B;AACF;", "names": []}