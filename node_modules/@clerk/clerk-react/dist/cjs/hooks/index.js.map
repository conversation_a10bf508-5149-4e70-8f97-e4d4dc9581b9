{"version": 3, "sources": ["../../../src/hooks/index.ts"], "sourcesContent": ["export { useUser } from './useUser';\nexport { useAuth } from './useAuth';\nexport { useSession } from './useSession';\nexport { useClerk } from './useClerk';\nexport { useSignIn } from './useSignIn';\nexport { useSignUp } from './useSignUp';\nexport { useSessionList } from './useSessionList';\nexport { useOrganization } from './useOrganization';\nexport { useOrganizationList } from './useOrganizationList';\nexport { useOrganizations } from './useOrganizations';\nexport { useMagicLink } from './useMagicLink';\nexport { useEmailLink } from './useEmailLink';\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAAwB;AACxB,qBAAwB;AACxB,wBAA2B;AAC3B,sBAAyB;AACzB,uBAA0B;AAC1B,uBAA0B;AAC1B,4BAA+B;AAC/B,6BAAgC;AAChC,iCAAoC;AACpC,8BAAiC;AACjC,0BAA6B;AAC7B,0BAA6B;", "names": []}