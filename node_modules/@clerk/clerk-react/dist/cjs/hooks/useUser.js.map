{"version": 3, "sources": ["../../../src/hooks/useUser.ts"], "sourcesContent": ["import type { UserResource } from '@clerk/types';\n\nimport { useUserContext } from '../contexts/UserContext';\n\ntype UseUserReturn =\n  | { isLoaded: false; isSignedIn: undefined; user: undefined }\n  | { isLoaded: true; isSignedIn: false; user: null }\n  | { isLoaded: true; isSignedIn: true; user: UserResource };\n\n/**\n * Returns the current auth state and if a user is signed in, the user object.\n *\n * Until Clerk loads and initializes, `isLoaded` will be set to `false`.\n * Once Clerk loads, `isLoaded` will be set to `true`, and you can\n * safely access `isSignedIn` state and `user`.\n *\n * For projects using NextJs or Remix, you can make this state available during SSR\n * simply by using the `withServerSideAuth` helper and setting the `loadUser` flag to `true`.\n *\n *\n * @example\n * A simple example:\n *\n * import { useUser } from '@clerk/clerk-react'\n *\n * function Hello() {\n *   const { isSignedIn, user } = useUser();\n *   if(!isSignedIn) {\n *     return null;\n *   }\n *   return <div>Hello, {user.firstName}</div>\n * }\n *\n * @example\n * Basic example in a NextJs app. This page will be fully rendered during SSR:\n *\n * import { useUser } from '@clerk/nextjs'\n * import { withServerSideAuth } from '@clerk/nextjs/api'\n *\n * export getServerSideProps = withServerSideAuth({ loadUser: true});\n *\n * export HelloPage = () => {\n *   const { isSignedIn, user } = useUser();\n *   if(!isSignedIn) {\n *     return null;\n *   }\n *   return <div>Hello, {user.firstName}</div>\n * }\n *\n */\nexport function useUser(): UseUserReturn {\n  const user = useUserContext();\n\n  if (user === undefined) {\n    return { isLoaded: false, isSignedIn: undefined, user: undefined };\n  }\n\n  if (user === null) {\n    return { isLoaded: true, isSignedIn: false, user: null };\n  }\n\n  return { isLoaded: true, isSignedIn: true, user };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,yBAA+B;AAgDxB,SAAS,UAAyB;AACvC,QAAM,WAAO,mCAAe;AAE5B,MAAI,SAAS,QAAW;AACtB,WAAO,EAAE,UAAU,OAAO,YAAY,QAAW,MAAM,OAAU;AAAA,EACnE;AAEA,MAAI,SAAS,MAAM;AACjB,WAAO,EAAE,UAAU,MAAM,YAAY,OAAO,MAAM,KAAK;AAAA,EACzD;AAEA,SAAO,EAAE,UAAU,MAAM,YAAY,MAAM,KAAK;AAClD;", "names": []}