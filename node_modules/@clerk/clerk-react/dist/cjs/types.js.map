{"version": 3, "sources": ["../../src/types.ts"], "sourcesContent": ["import type {\n  Clerk,\n  ClerkOptions,\n  ClientResource,\n  DomainOrProxyUrl,\n  LoadedClerk,\n  MultiDomainAndOrProxy,\n  PublishableKeyOrFrontendApi,\n  SDKMetadata,\n  SessionResource,\n  SignInRedirectOptions,\n  SignUpRedirectOptions,\n  UserResource,\n} from '@clerk/types';\nimport type React from 'react';\n\ndeclare global {\n  interface Window {\n    __clerk_frontend_api?: string;\n    __clerk_publishable_key?: string;\n    __clerk_proxy_url?: Clerk['proxyUrl'];\n    __clerk_domain?: Clerk['domain'];\n  }\n}\n\nexport type IsomorphicClerkOptions = Omit<ClerkOptions, 'isSatellite'> & {\n  Clerk?: ClerkProp;\n  clerkJSUrl?: string;\n  clerkJSVariant?: 'headless' | '';\n  clerkJSVersion?: string;\n  sdkMetadata?: SDKMetadata;\n} & PublishableKeyOrFrontendApi &\n  MultiDomainAndOrProxy;\n\nexport interface BrowserClerkConstructor {\n  new (publishableKey: string, options?: DomainOrProxyUrl): BrowserClerk;\n}\n\nexport interface HeadlessBrowserClerkConstrutor {\n  new (publishableKey: string, options?: DomainOrProxyUrl): HeadlessBrowserClerk;\n}\n\nexport type WithClerkProp<T = unknown> = T & { clerk: LoadedClerk };\n\nexport type WithUserProp<T = unknown> = T & { user: UserResource };\n\nexport type WithSessionProp<T = unknown> = T & { session: SessionResource };\n\n// Clerk object\nexport interface MountProps {\n  mount: (node: HTMLDivElement, props: any) => void;\n  unmount: (node: HTMLDivElement) => void;\n  updateProps: (props: any) => void;\n  props?: any;\n  customPagesPortals?: any[];\n}\n\nexport interface OpenProps {\n  open: (props: any) => void;\n  close: () => void;\n  props?: any;\n}\n\nexport interface HeadlessBrowserClerk extends Clerk {\n  load: (opts?: Omit<ClerkOptions, 'isSatellite'>) => Promise<void>;\n  updateClient: (client: ClientResource) => void;\n}\n\nexport interface BrowserClerk extends HeadlessBrowserClerk {\n  onComponentsReady: Promise<void>;\n  components: any;\n}\n\nexport type ClerkProp =\n  | BrowserClerkConstructor\n  | BrowserClerk\n  | HeadlessBrowserClerk\n  | HeadlessBrowserClerkConstrutor\n  | undefined\n  | null;\n\ntype ButtonProps = {\n  afterSignInUrl?: string;\n  afterSignUpUrl?: string;\n  redirectUrl?: string;\n  mode?: 'redirect' | 'modal';\n  children?: React.ReactNode;\n};\n\nexport type SignInButtonProps = ButtonProps;\nexport interface SignUpButtonProps extends ButtonProps {\n  unsafeMetadata?: SignUpUnsafeMetadata;\n}\n\nexport type SignInWithMetamaskButtonProps = Pick<ButtonProps, 'redirectUrl' | 'children'>;\n\nexport type RedirectToSignInProps = SignInRedirectOptions;\nexport type RedirectToSignUpProps = SignUpRedirectOptions;\n\nexport type UserProfilePageProps = {\n  url?: string;\n  label: string;\n  labelIcon?: React.ReactNode;\n};\n\nexport type UserProfileLinkProps = {\n  url: string;\n  label: string;\n  labelIcon: React.ReactNode;\n};\n\nexport type OrganizationProfilePageProps = UserProfilePageProps;\nexport type OrganizationProfileLinkProps = UserProfileLinkProps;\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}