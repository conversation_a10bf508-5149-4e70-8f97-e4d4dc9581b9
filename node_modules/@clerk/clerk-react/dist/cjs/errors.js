"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var errors_exports = {};
__export(errors_exports, {
  EmailLinkErrorCode: () => import_error.EmailLinkErrorCode,
  MagicLinkErrorCode: () => import_error.MagicLinkErrorCode,
  customLinkWrongProps: () => customLinkWrongProps,
  customPageWrongProps: () => customPageWrongProps,
  customPagesIgnoredComponent: () => customPagesIgnoredComponent,
  hocChildrenNotAFunctionError: () => hocChildrenNotAFunctionError,
  invalidStateError: () => invalidStateError,
  isClerkAPIResponseError: () => import_error.isClerkAPIResponseError,
  isEmailLinkError: () => import_error.isEmailLinkError,
  isKnownError: () => import_error.isKnownError,
  isMagicLinkError: () => import_error.isMagicLinkError,
  isMetamaskError: () => import_error.isMetamaskError,
  multipleChildrenInButtonComponent: () => multipleChildrenInButtonComponent,
  multipleClerkProvidersError: () => multipleClerkProvidersError,
  noClerkProviderError: () => noClerkProviderError,
  noFrontendApiError: () => noFrontendApiError,
  noGuaranteedLoadedError: () => noGuaranteedLoadedError,
  noGuaranteedUserError: () => noGuaranteedUserError,
  organizationProfileLinkRenderedError: () => organizationProfileLinkRenderedError,
  organizationProfilePageRenderedError: () => organizationProfilePageRenderedError,
  unsupportedNonBrowserDomainOrProxyUrlFunction: () => unsupportedNonBrowserDomainOrProxyUrlFunction,
  useAuthHasRequiresRoleOrPermission: () => useAuthHasRequiresRoleOrPermission,
  userProfileLinkRenderedError: () => userProfileLinkRenderedError,
  userProfilePageRenderedError: () => userProfilePageRenderedError
});
module.exports = __toCommonJS(errors_exports);
var import_error = require("@clerk/shared/error");
const noFrontendApiError = "Clerk: You must add the frontendApi prop to your <ClerkProvider>";
const noClerkProviderError = "Clerk: You must wrap your application in a <ClerkProvider> component.";
const noGuaranteedLoadedError = (hookName) => `Clerk: You're calling ${hookName} before there's a guarantee the client has been loaded. Call ${hookName} from a child of <SignedIn>, <SignedOut>, or <ClerkLoaded>, or use the withClerk() HOC.`;
const noGuaranteedUserError = (hookName) => `Clerk: You're calling ${hookName} before there's a guarantee there's an active user. Call ${hookName} from a child of <SignedIn> or use the withUser() HOC.`;
const multipleClerkProvidersError = "Clerk: You've added multiple <ClerkProvider> components in your React component tree. Wrap your components in a single <ClerkProvider>.";
const hocChildrenNotAFunctionError = "Clerk: Child of WithClerk must be a function.";
const multipleChildrenInButtonComponent = (name) => `Clerk: You've passed multiple children components to <${name}/>. You can only pass a single child component or text.`;
const invalidStateError = "Clerk: Invalid state. Feel free to submit a bug or reach out to support here: https://clerk.com/support";
const unsupportedNonBrowserDomainOrProxyUrlFunction = "Clerk: Unsupported usage of isSatellite, domain or proxyUrl. The usage of isSatellite, domain or proxyUrl as function is not supported in non-browser environments.";
const userProfilePageRenderedError = "Clerk: <UserProfile.Page /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.";
const userProfileLinkRenderedError = "Clerk: <UserProfile.Link /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.";
const organizationProfilePageRenderedError = "Clerk: <OrganizationProfile.Page /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.";
const organizationProfileLinkRenderedError = "Clerk: <OrganizationProfile.Link /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.";
const customPagesIgnoredComponent = (componentName) => `Clerk: <${componentName} /> can only accept <${componentName}.Page /> and <${componentName}.Link /> as its children. Any other provided component will be ignored.`;
const customPageWrongProps = (componentName) => `Clerk: Missing props. <${componentName}.Page /> component requires the following props: url, label, labelIcon, alongside with children to be rendered inside the page.`;
const customLinkWrongProps = (componentName) => `Clerk: Missing props. <${componentName}.Link /> component requires the following props: url, label and labelIcon.`;
const useAuthHasRequiresRoleOrPermission = 'Clerk: Missing parameters. `has` from `useAuth` requires a permission or role key to be passed. Example usage: `has({permission: "org:posts:edit"`';
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  EmailLinkErrorCode,
  MagicLinkErrorCode,
  customLinkWrongProps,
  customPageWrongProps,
  customPagesIgnoredComponent,
  hocChildrenNotAFunctionError,
  invalidStateError,
  isClerkAPIResponseError,
  isEmailLinkError,
  isKnownError,
  isMagicLinkError,
  isMetamaskError,
  multipleChildrenInButtonComponent,
  multipleClerkProvidersError,
  noClerkProviderError,
  noFrontendApiError,
  noGuaranteedLoadedError,
  noGuaranteedUserError,
  organizationProfileLinkRenderedError,
  organizationProfilePageRenderedError,
  unsupportedNonBrowserDomainOrProxyUrlFunction,
  useAuthHasRequiresRoleOrPermission,
  userProfileLinkRenderedError,
  userProfilePageRenderedError
});
//# sourceMappingURL=errors.js.map