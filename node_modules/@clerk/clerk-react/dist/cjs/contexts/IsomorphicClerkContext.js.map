{"version": 3, "sources": ["../../../src/contexts/IsomorphicClerkContext.tsx"], "sourcesContent": ["import { ClerkInstanceContext, useClerkInstanceContext } from '@clerk/shared/react';\n\nexport const [IsomorphicClerkContext, useIsomorphicClerkContext] = [ClerkInstanceContext, useClerkInstanceContext];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAA8D;AAEvD,MAAM,CAAC,wBAAwB,yBAAyB,IAAI,CAAC,mCAAsB,oCAAuB;", "names": []}