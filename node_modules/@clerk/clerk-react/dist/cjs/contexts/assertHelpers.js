"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var assertHelpers_exports = {};
__export(assertHelpers_exports, {
  assertClerkLoadedGuarantee: () => assertClerkLoadedGuarantee,
  assertWrappedByClerkProvider: () => assertWrappedByClerkProvider
});
module.exports = __toCommonJS(assertHelpers_exports);
var import_errors = require("../errors");
function assertWrappedByClerkProvider(contextVal) {
  if (!contextVal) {
    throw new Error(import_errors.noClerkProviderError);
  }
}
function assertClerkLoadedGuarantee(guarantee, hookName) {
  if (!guarantee) {
    throw new Error((0, import_errors.noGuaranteedLoadedError)(hookName));
  }
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  assertClerkLoadedGuarantee,
  assertWrappedByClerkProvider
});
//# sourceMappingURL=assertHelpers.js.map