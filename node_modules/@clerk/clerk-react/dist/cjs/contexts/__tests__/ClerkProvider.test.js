"use strict";
var import_localizations = require("@clerk/localizations");
var import_themes = require("@clerk/themes");
var import_expect_type = require("expect-type");
describe("ClerkProvider", () => {
  describe("Type tests", () => {
    describe("publishableKey and frontendApi", () => {
      it("expects a publishableKey and children as the minimum accepted case", () => {
        (0, import_expect_type.expectTypeOf)({ publishableKey: "test", children: "" }).toMatchTypeOf();
      });
      it("publishable key is replaceable with frontendApi", () => {
        (0, import_expect_type.expectTypeOf)({ frontendApi: "test", children: "" }).toMatchTypeOf();
      });
      it("errors if no publishableKey or frontendApi", () => {
        (0, import_expect_type.expectTypeOf)({ children: "" }).not.toMatchTypeOf();
      });
      it("errors if both publishable<PERSON>ey and frontendApi are provided", () => {
        (0, import_expect_type.expectTypeOf)({ publishableKey: "test", frontendApi: "test" }).not.toMatchTypeOf();
      });
    });
  });
  describe("Multi domain", () => {
    const defaultProps = { publishableKey: "test", children: "" };
    it("proxyUrl (primary app)", () => {
      (0, import_expect_type.expectTypeOf)({ ...defaultProps, proxyUrl: "test" }).toMatchTypeOf();
    });
    it("proxyUrl + isSatellite (satellite app)", () => {
      (0, import_expect_type.expectTypeOf)({ ...defaultProps, proxyUrl: "test", isSatellite: true }).toMatchTypeOf();
    });
    it("domain + isSatellite (satellite app)", () => {
      (0, import_expect_type.expectTypeOf)({ ...defaultProps, domain: "test", isSatellite: true }).toMatchTypeOf();
    });
    it("only domain is not allowed", () => {
      (0, import_expect_type.expectTypeOf)({ ...defaultProps, domain: "test" }).not.toMatchTypeOf();
    });
    it("only isSatellite is not allowed", () => {
      (0, import_expect_type.expectTypeOf)({ ...defaultProps, isSatellite: true }).not.toMatchTypeOf();
    });
    it("proxyUrl + domain is not allowed", () => {
      (0, import_expect_type.expectTypeOf)({ ...defaultProps, proxyUrl: "test", domain: "test" }).not.toMatchTypeOf();
    });
    it("proxyUrl + domain + isSatellite is not allowed", () => {
      (0, import_expect_type.expectTypeOf)({
        ...defaultProps,
        proxyUrl: "test",
        domain: "test",
        isSatellite: true
      }).not.toMatchTypeOf();
    });
  });
  describe("clerkJSVariant", () => {
    const defaultProps = { publishableKey: "test", children: "" };
    it("is either headless or empty", () => {
      (0, import_expect_type.expectTypeOf)({ ...defaultProps, clerkJSVariant: "headless" }).toMatchTypeOf();
      (0, import_expect_type.expectTypeOf)({ ...defaultProps, clerkJSVariant: "" }).toMatchTypeOf();
      (0, import_expect_type.expectTypeOf)({ ...defaultProps, clerkJSVariant: void 0 }).toMatchTypeOf();
      (0, import_expect_type.expectTypeOf)({ ...defaultProps, clerkJSVariant: "test" }).not.toMatchTypeOf();
    });
  });
  describe("appearance", () => {
    const defaultProps = { publishableKey: "test", children: "" };
    it("exists as a prop", () => {
      (0, import_expect_type.expectTypeOf)({ ...defaultProps, appearance: {} }).toMatchTypeOf();
    });
    it("includes variables, elements, layout baseTheme", () => {
      (0, import_expect_type.expectTypeOf)({
        ...defaultProps,
        appearance: { elements: {}, variables: {}, layout: {}, baseTheme: import_themes.dark }
      }).toMatchTypeOf();
    });
    it("errors if a non existent key is provided", () => {
      (0, import_expect_type.expectTypeOf)({
        ...defaultProps,
        appearance: { variables: { nonExistentKey: "" } }
      }).not.toMatchTypeOf();
      (0, import_expect_type.expectTypeOf)({
        ...defaultProps,
        appearance: { layout: { nonExistentKey: "" } }
      }).not.toMatchTypeOf();
    });
  });
  describe("localization", () => {
    const defaultProps = { publishableKey: "test", children: "" };
    it("exists as a prop", () => {
      (0, import_expect_type.expectTypeOf)({ ...defaultProps, localization: {} }).toMatchTypeOf();
    });
    it("errors if a non existent key is provided", () => {
      (0, import_expect_type.expectTypeOf)({
        ...defaultProps,
        localization: { a: "test" }
      }).not.toMatchTypeOf();
      (0, import_expect_type.expectTypeOf)({
        ...defaultProps,
        localization: { signUp: { start: "test" } }
      }).not.toMatchTypeOf();
    });
    it("works with all our prebuilt localizations", () => {
      (0, import_expect_type.expectTypeOf)({
        ...defaultProps,
        localization: import_localizations.deDe
      }).toMatchTypeOf();
      (0, import_expect_type.expectTypeOf)({
        ...defaultProps,
        localization: import_localizations.frFR
      }).toMatchTypeOf();
      (0, import_expect_type.expectTypeOf)({
        ...defaultProps,
        localization: import_localizations.enUS
      }).toMatchTypeOf();
      (0, import_expect_type.expectTypeOf)({
        ...defaultProps,
        localization: import_localizations.esES
      }).toMatchTypeOf();
      (0, import_expect_type.expectTypeOf)({
        ...defaultProps,
        localization: import_localizations.itIT
      }).toMatchTypeOf();
      (0, import_expect_type.expectTypeOf)({
        ...defaultProps,
        localization: import_localizations.ptBR
      }).toMatchTypeOf();
      (0, import_expect_type.expectTypeOf)({
        ...defaultProps,
        localization: import_localizations.ruRU
      }).toMatchTypeOf();
      (0, import_expect_type.expectTypeOf)({
        ...defaultProps,
        localization: import_localizations.svSE
      }).toMatchTypeOf();
      (0, import_expect_type.expectTypeOf)({
        ...defaultProps,
        localization: import_localizations.trTR
      }).toMatchTypeOf();
      (0, import_expect_type.expectTypeOf)({
        ...defaultProps,
        localization: import_localizations.jaJP
      }).toMatchTypeOf();
      (0, import_expect_type.expectTypeOf)({
        ...defaultProps,
        localization: import_localizations.jaJP
      }).toMatchTypeOf();
      (0, import_expect_type.expectTypeOf)({
        ...defaultProps,
        localization: import_localizations.csCZ
      }).toMatchTypeOf();
      (0, import_expect_type.expectTypeOf)({
        ...defaultProps,
        localization: import_localizations.koKR
      }).toMatchTypeOf();
      (0, import_expect_type.expectTypeOf)({
        ...defaultProps,
        localization: import_localizations.skSK
      }).toMatchTypeOf();
      (0, import_expect_type.expectTypeOf)({
        ...defaultProps,
        localization: import_localizations.ukUA
      }).toMatchTypeOf();
    });
    it("is able to receive multiple localizations", () => {
      (0, import_expect_type.expectTypeOf)({
        ...defaultProps,
        localization: { ...import_localizations.frFR, ...import_localizations.deDe }
      }).toMatchTypeOf();
    });
  });
  describe("children", () => {
    it("errors if no children", () => {
      (0, import_expect_type.expectTypeOf)({ publishableKey: "test" }).not.toMatchTypeOf();
    });
  });
});
//# sourceMappingURL=ClerkProvider.test.js.map