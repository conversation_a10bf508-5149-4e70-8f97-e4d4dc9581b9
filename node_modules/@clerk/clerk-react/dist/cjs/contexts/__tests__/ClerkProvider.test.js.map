{"version": 3, "sources": ["../../../../src/contexts/__tests__/ClerkProvider.test.tsx"], "sourcesContent": ["import {\n  csCZ,\n  deDe,\n  enUS,\n  esES,\n  frFR,\n  itIT,\n  jaJP,\n  koKR,\n  ptBR,\n  ruRU,\n  skSK,\n  svSE,\n  trTR,\n  ukUA,\n} from '@clerk/localizations';\nimport { dark } from '@clerk/themes';\nimport { expectTypeOf } from 'expect-type';\n\nimport type { ClerkProvider } from '../ClerkProvider';\n\n//@ts-ignore\ntype ClerkProviderProps = Parameters<typeof ClerkProvider>[0];\n\ndescribe('ClerkProvider', () => {\n  describe('Type tests', () => {\n    describe('publishableKey and frontendApi', () => {\n      it('expects a publishableKey and children as the minimum accepted case', () => {\n        expectTypeOf({ publishableKey: 'test', children: '' }).toMatchTypeOf<ClerkProviderProps>();\n      });\n\n      it('publishable key is replaceable with frontendApi', () => {\n        expectTypeOf({ frontendApi: 'test', children: '' }).toMatchTypeOf<ClerkProviderProps>();\n      });\n\n      it('errors if no publishableKey or frontendApi', () => {\n        expectTypeOf({ children: '' }).not.toMatchTypeOf<ClerkProviderProps>();\n      });\n\n      it('errors if both publishableKey and frontendApi are provided', () => {\n        expectTypeOf({ publishableKey: 'test', frontendApi: 'test' }).not.toMatchTypeOf<ClerkProviderProps>();\n      });\n    });\n  });\n\n  describe('Multi domain', () => {\n    const defaultProps = { publishableKey: 'test', children: '' };\n\n    it('proxyUrl (primary app)', () => {\n      expectTypeOf({ ...defaultProps, proxyUrl: 'test' }).toMatchTypeOf<ClerkProviderProps>();\n    });\n\n    it('proxyUrl + isSatellite (satellite app)', () => {\n      expectTypeOf({ ...defaultProps, proxyUrl: 'test', isSatellite: true }).toMatchTypeOf<ClerkProviderProps>();\n    });\n\n    it('domain + isSatellite (satellite app)', () => {\n      expectTypeOf({ ...defaultProps, domain: 'test', isSatellite: true }).toMatchTypeOf<ClerkProviderProps>();\n    });\n\n    it('only domain is not allowed', () => {\n      expectTypeOf({ ...defaultProps, domain: 'test' }).not.toMatchTypeOf<ClerkProviderProps>();\n    });\n\n    it('only isSatellite is not allowed', () => {\n      expectTypeOf({ ...defaultProps, isSatellite: true }).not.toMatchTypeOf<ClerkProviderProps>();\n    });\n\n    it('proxyUrl + domain is not allowed', () => {\n      expectTypeOf({ ...defaultProps, proxyUrl: 'test', domain: 'test' }).not.toMatchTypeOf<ClerkProviderProps>();\n    });\n\n    it('proxyUrl + domain + isSatellite is not allowed', () => {\n      expectTypeOf({\n        ...defaultProps,\n        proxyUrl: 'test',\n        domain: 'test',\n        isSatellite: true,\n      }).not.toMatchTypeOf<ClerkProviderProps>();\n    });\n  });\n\n  describe('clerkJSVariant', () => {\n    const defaultProps = { publishableKey: 'test', children: '' };\n\n    it('is either headless or empty', () => {\n      expectTypeOf({ ...defaultProps, clerkJSVariant: 'headless' as const }).toMatchTypeOf<ClerkProviderProps>();\n      expectTypeOf({ ...defaultProps, clerkJSVariant: '' as const }).toMatchTypeOf<ClerkProviderProps>();\n      expectTypeOf({ ...defaultProps, clerkJSVariant: undefined }).toMatchTypeOf<ClerkProviderProps>();\n      expectTypeOf({ ...defaultProps, clerkJSVariant: 'test' }).not.toMatchTypeOf<ClerkProviderProps>();\n    });\n  });\n\n  describe('appearance', () => {\n    const defaultProps = { publishableKey: 'test', children: '' };\n\n    it('exists as a prop', () => {\n      expectTypeOf({ ...defaultProps, appearance: {} }).toMatchTypeOf<ClerkProviderProps>();\n    });\n\n    it('includes variables, elements, layout baseTheme', () => {\n      expectTypeOf({\n        ...defaultProps,\n        appearance: { elements: {}, variables: {}, layout: {}, baseTheme: dark },\n      }).toMatchTypeOf<ClerkProviderProps>();\n    });\n\n    it('errors if a non existent key is provided', () => {\n      expectTypeOf({\n        ...defaultProps,\n        appearance: { variables: { nonExistentKey: '' } },\n      }).not.toMatchTypeOf<ClerkProviderProps>();\n\n      expectTypeOf({\n        ...defaultProps,\n        appearance: { layout: { nonExistentKey: '' } },\n      }).not.toMatchTypeOf<ClerkProviderProps>();\n\n      // expectTypeOf({\n      //   ...defaultProps,\n      //   appearance: { elements: { nonExistentKey: '' } },\n      // }).not.toMatchTypeOf<ClerkProviderProps>();\n    });\n  });\n\n  describe('localization', () => {\n    const defaultProps = { publishableKey: 'test', children: '' };\n\n    it('exists as a prop', () => {\n      expectTypeOf({ ...defaultProps, localization: {} }).toMatchTypeOf<ClerkProviderProps>();\n    });\n\n    it('errors if a non existent key is provided', () => {\n      expectTypeOf({\n        ...defaultProps,\n        localization: { a: 'test' },\n      }).not.toMatchTypeOf<ClerkProviderProps>();\n\n      expectTypeOf({\n        ...defaultProps,\n        localization: { signUp: { start: 'test' } },\n      }).not.toMatchTypeOf<ClerkProviderProps>();\n    });\n\n    it('works with all our prebuilt localizations', () => {\n      expectTypeOf({\n        ...defaultProps,\n        localization: deDe,\n      }).toMatchTypeOf<ClerkProviderProps>();\n\n      expectTypeOf({\n        ...defaultProps,\n        localization: frFR,\n      }).toMatchTypeOf<ClerkProviderProps>();\n\n      expectTypeOf({\n        ...defaultProps,\n        localization: enUS,\n      }).toMatchTypeOf<ClerkProviderProps>();\n\n      expectTypeOf({\n        ...defaultProps,\n        localization: esES,\n      }).toMatchTypeOf<ClerkProviderProps>();\n\n      expectTypeOf({\n        ...defaultProps,\n        localization: itIT,\n      }).toMatchTypeOf<ClerkProviderProps>();\n\n      expectTypeOf({\n        ...defaultProps,\n        localization: ptBR,\n      }).toMatchTypeOf<ClerkProviderProps>();\n\n      expectTypeOf({\n        ...defaultProps,\n        localization: ruRU,\n      }).toMatchTypeOf<ClerkProviderProps>();\n\n      expectTypeOf({\n        ...defaultProps,\n        localization: svSE,\n      }).toMatchTypeOf<ClerkProviderProps>();\n\n      expectTypeOf({\n        ...defaultProps,\n        localization: trTR,\n      }).toMatchTypeOf<ClerkProviderProps>();\n\n      expectTypeOf({\n        ...defaultProps,\n        localization: jaJP,\n      }).toMatchTypeOf<ClerkProviderProps>();\n\n      expectTypeOf({\n        ...defaultProps,\n        localization: jaJP,\n      }).toMatchTypeOf<ClerkProviderProps>();\n\n      expectTypeOf({\n        ...defaultProps,\n        localization: csCZ,\n      }).toMatchTypeOf<ClerkProviderProps>();\n\n      expectTypeOf({\n        ...defaultProps,\n        localization: koKR,\n      }).toMatchTypeOf<ClerkProviderProps>();\n\n      expectTypeOf({\n        ...defaultProps,\n        localization: skSK,\n      }).toMatchTypeOf<ClerkProviderProps>();\n\n      expectTypeOf({\n        ...defaultProps,\n        localization: ukUA,\n      }).toMatchTypeOf<ClerkProviderProps>();\n    });\n\n    it('is able to receive multiple localizations', () => {\n      expectTypeOf({\n        ...defaultProps,\n        localization: { ...frFR, ...deDe },\n      }).toMatchTypeOf<ClerkProviderProps>();\n    });\n  });\n\n  describe('children', () => {\n    it('errors if no children', () => {\n      expectTypeOf({ publishableKey: 'test' }).not.toMatchTypeOf<ClerkProviderProps>();\n    });\n  });\n});\n"], "mappings": ";AAAA,2BAeO;AACP,oBAAqB;AACrB,yBAA6B;AAO7B,SAAS,iBAAiB,MAAM;AAC9B,WAAS,cAAc,MAAM;AAC3B,aAAS,kCAAkC,MAAM;AAC/C,SAAG,sEAAsE,MAAM;AAC7E,6CAAa,EAAE,gBAAgB,QAAQ,UAAU,GAAG,CAAC,EAAE,cAAkC;AAAA,MAC3F,CAAC;AAED,SAAG,mDAAmD,MAAM;AAC1D,6CAAa,EAAE,aAAa,QAAQ,UAAU,GAAG,CAAC,EAAE,cAAkC;AAAA,MACxF,CAAC;AAED,SAAG,8CAA8C,MAAM;AACrD,6CAAa,EAAE,UAAU,GAAG,CAAC,EAAE,IAAI,cAAkC;AAAA,MACvE,CAAC;AAED,SAAG,8DAA8D,MAAM;AACrE,6CAAa,EAAE,gBAAgB,QAAQ,aAAa,OAAO,CAAC,EAAE,IAAI,cAAkC;AAAA,MACtG,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AAED,WAAS,gBAAgB,MAAM;AAC7B,UAAM,eAAe,EAAE,gBAAgB,QAAQ,UAAU,GAAG;AAE5D,OAAG,0BAA0B,MAAM;AACjC,2CAAa,EAAE,GAAG,cAAc,UAAU,OAAO,CAAC,EAAE,cAAkC;AAAA,IACxF,CAAC;AAED,OAAG,0CAA0C,MAAM;AACjD,2CAAa,EAAE,GAAG,cAAc,UAAU,QAAQ,aAAa,KAAK,CAAC,EAAE,cAAkC;AAAA,IAC3G,CAAC;AAED,OAAG,wCAAwC,MAAM;AAC/C,2CAAa,EAAE,GAAG,cAAc,QAAQ,QAAQ,aAAa,KAAK,CAAC,EAAE,cAAkC;AAAA,IACzG,CAAC;AAED,OAAG,8BAA8B,MAAM;AACrC,2CAAa,EAAE,GAAG,cAAc,QAAQ,OAAO,CAAC,EAAE,IAAI,cAAkC;AAAA,IAC1F,CAAC;AAED,OAAG,mCAAmC,MAAM;AAC1C,2CAAa,EAAE,GAAG,cAAc,aAAa,KAAK,CAAC,EAAE,IAAI,cAAkC;AAAA,IAC7F,CAAC;AAED,OAAG,oCAAoC,MAAM;AAC3C,2CAAa,EAAE,GAAG,cAAc,UAAU,QAAQ,QAAQ,OAAO,CAAC,EAAE,IAAI,cAAkC;AAAA,IAC5G,CAAC;AAED,OAAG,kDAAkD,MAAM;AACzD,2CAAa;AAAA,QACX,GAAG;AAAA,QACH,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,aAAa;AAAA,MACf,CAAC,EAAE,IAAI,cAAkC;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC;AAED,WAAS,kBAAkB,MAAM;AAC/B,UAAM,eAAe,EAAE,gBAAgB,QAAQ,UAAU,GAAG;AAE5D,OAAG,+BAA+B,MAAM;AACtC,2CAAa,EAAE,GAAG,cAAc,gBAAgB,WAAoB,CAAC,EAAE,cAAkC;AACzG,2CAAa,EAAE,GAAG,cAAc,gBAAgB,GAAY,CAAC,EAAE,cAAkC;AACjG,2CAAa,EAAE,GAAG,cAAc,gBAAgB,OAAU,CAAC,EAAE,cAAkC;AAC/F,2CAAa,EAAE,GAAG,cAAc,gBAAgB,OAAO,CAAC,EAAE,IAAI,cAAkC;AAAA,IAClG,CAAC;AAAA,EACH,CAAC;AAED,WAAS,cAAc,MAAM;AAC3B,UAAM,eAAe,EAAE,gBAAgB,QAAQ,UAAU,GAAG;AAE5D,OAAG,oBAAoB,MAAM;AAC3B,2CAAa,EAAE,GAAG,cAAc,YAAY,CAAC,EAAE,CAAC,EAAE,cAAkC;AAAA,IACtF,CAAC;AAED,OAAG,kDAAkD,MAAM;AACzD,2CAAa;AAAA,QACX,GAAG;AAAA,QACH,YAAY,EAAE,UAAU,CAAC,GAAG,WAAW,CAAC,GAAG,QAAQ,CAAC,GAAG,WAAW,mBAAK;AAAA,MACzE,CAAC,EAAE,cAAkC;AAAA,IACvC,CAAC;AAED,OAAG,4CAA4C,MAAM;AACnD,2CAAa;AAAA,QACX,GAAG;AAAA,QACH,YAAY,EAAE,WAAW,EAAE,gBAAgB,GAAG,EAAE;AAAA,MAClD,CAAC,EAAE,IAAI,cAAkC;AAEzC,2CAAa;AAAA,QACX,GAAG;AAAA,QACH,YAAY,EAAE,QAAQ,EAAE,gBAAgB,GAAG,EAAE;AAAA,MAC/C,CAAC,EAAE,IAAI,cAAkC;AAAA,IAM3C,CAAC;AAAA,EACH,CAAC;AAED,WAAS,gBAAgB,MAAM;AAC7B,UAAM,eAAe,EAAE,gBAAgB,QAAQ,UAAU,GAAG;AAE5D,OAAG,oBAAoB,MAAM;AAC3B,2CAAa,EAAE,GAAG,cAAc,cAAc,CAAC,EAAE,CAAC,EAAE,cAAkC;AAAA,IACxF,CAAC;AAED,OAAG,4CAA4C,MAAM;AACnD,2CAAa;AAAA,QACX,GAAG;AAAA,QACH,cAAc,EAAE,GAAG,OAAO;AAAA,MAC5B,CAAC,EAAE,IAAI,cAAkC;AAEzC,2CAAa;AAAA,QACX,GAAG;AAAA,QACH,cAAc,EAAE,QAAQ,EAAE,OAAO,OAAO,EAAE;AAAA,MAC5C,CAAC,EAAE,IAAI,cAAkC;AAAA,IAC3C,CAAC;AAED,OAAG,6CAA6C,MAAM;AACpD,2CAAa;AAAA,QACX,GAAG;AAAA,QACH,cAAc;AAAA,MAChB,CAAC,EAAE,cAAkC;AAErC,2CAAa;AAAA,QACX,GAAG;AAAA,QACH,cAAc;AAAA,MAChB,CAAC,EAAE,cAAkC;AAErC,2CAAa;AAAA,QACX,GAAG;AAAA,QACH,cAAc;AAAA,MAChB,CAAC,EAAE,cAAkC;AAErC,2CAAa;AAAA,QACX,GAAG;AAAA,QACH,cAAc;AAAA,MAChB,CAAC,EAAE,cAAkC;AAErC,2CAAa;AAAA,QACX,GAAG;AAAA,QACH,cAAc;AAAA,MAChB,CAAC,EAAE,cAAkC;AAErC,2CAAa;AAAA,QACX,GAAG;AAAA,QACH,cAAc;AAAA,MAChB,CAAC,EAAE,cAAkC;AAErC,2CAAa;AAAA,QACX,GAAG;AAAA,QACH,cAAc;AAAA,MAChB,CAAC,EAAE,cAAkC;AAErC,2CAAa;AAAA,QACX,GAAG;AAAA,QACH,cAAc;AAAA,MAChB,CAAC,EAAE,cAAkC;AAErC,2CAAa;AAAA,QACX,GAAG;AAAA,QACH,cAAc;AAAA,MAChB,CAAC,EAAE,cAAkC;AAErC,2CAAa;AAAA,QACX,GAAG;AAAA,QACH,cAAc;AAAA,MAChB,CAAC,EAAE,cAAkC;AAErC,2CAAa;AAAA,QACX,GAAG;AAAA,QACH,cAAc;AAAA,MAChB,CAAC,EAAE,cAAkC;AAErC,2CAAa;AAAA,QACX,GAAG;AAAA,QACH,cAAc;AAAA,MAChB,CAAC,EAAE,cAAkC;AAErC,2CAAa;AAAA,QACX,GAAG;AAAA,QACH,cAAc;AAAA,MAChB,CAAC,EAAE,cAAkC;AAErC,2CAAa;AAAA,QACX,GAAG;AAAA,QACH,cAAc;AAAA,MAChB,CAAC,EAAE,cAAkC;AAErC,2CAAa;AAAA,QACX,GAAG;AAAA,QACH,cAAc;AAAA,MAChB,CAAC,EAAE,cAAkC;AAAA,IACvC,CAAC;AAED,OAAG,6CAA6C,MAAM;AACpD,2CAAa;AAAA,QACX,GAAG;AAAA,QACH,cAAc,EAAE,GAAG,2BAAM,GAAG,0BAAK;AAAA,MACnC,CAAC,EAAE,cAAkC;AAAA,IACvC,CAAC;AAAA,EACH,CAAC;AAED,WAAS,YAAY,MAAM;AACzB,OAAG,yBAAyB,MAAM;AAChC,2CAAa,EAAE,gBAAgB,OAAO,CAAC,EAAE,IAAI,cAAkC;AAAA,IACjF,CAAC;AAAA,EACH,CAAC;AACH,CAAC;", "names": []}