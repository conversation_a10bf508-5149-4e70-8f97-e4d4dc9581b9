"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var withUser_exports = {};
__export(withUser_exports, {
  WithUser: () => WithUser,
  withUser: () => withUser
});
module.exports = __toCommonJS(withUser_exports);
var import_react = __toESM(require("react"));
var import_UserContext = require("../contexts/UserContext");
var import_errors = require("../errors");
const withUser = (Component, displayName) => {
  displayName = displayName || Component.displayName || Component.name || "Component";
  Component.displayName = displayName;
  const HOC = (props) => {
    const user = (0, import_UserContext.useUserContext)();
    if (!user) {
      return null;
    }
    return /* @__PURE__ */ import_react.default.createElement(
      Component,
      {
        ...props,
        user
      }
    );
  };
  HOC.displayName = `withUser(${displayName})`;
  return HOC;
};
const WithUser = ({ children }) => {
  const user = (0, import_UserContext.useUserContext)();
  if (typeof children !== "function") {
    throw new Error(import_errors.hocChildrenNotAFunctionError);
  }
  if (!user) {
    return null;
  }
  return /* @__PURE__ */ import_react.default.createElement(import_react.default.Fragment, null, children(user));
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  WithUser,
  withUser
});
//# sourceMappingURL=withUser.js.map