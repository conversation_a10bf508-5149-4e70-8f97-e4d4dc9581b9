{"version": 3, "sources": ["../../../../src/components/__tests__/SignInWithMetamaskButton.test.tsx"], "sourcesContent": ["import { render, screen, waitFor } from '@testing-library/react';\nimport userEvent from '@testing-library/user-event';\nimport React from 'react';\n\nimport { SignInWithMetamaskButton } from '../SignInWithMetamaskButton';\n\nconst mockAuthenticatewithMetamask = jest.fn();\nconst originalError = console.error;\n\nconst mockClerk = {\n  authenticateWithMetamask: mockAuthenticatewithMetamask,\n} as any;\n\njest.mock('../withClerk', () => {\n  return {\n    withClerk: (Component: any) => (props: any) =>\n      (\n        <Component\n          {...props}\n          clerk={mockClerk}\n        />\n      ),\n  };\n});\n\ndescribe('<SignInWithMetamaskButton/>', () => {\n  beforeAll(() => {\n    console.error = jest.fn();\n  });\n\n  afterAll(() => {\n    console.error = originalError;\n  });\n\n  beforeEach(() => {\n    mockAuthenticatewithMetamask.mockReset();\n  });\n\n  it('calls clerk.authenticateWithMetamask when clicked', async () => {\n    render(<SignInWithMetamaskButton />);\n    const btn = screen.getByText('Sign in with Metamask');\n    userEvent.click(btn);\n    await waitFor(() => {\n      expect(mockAuthenticatewithMetamask).toHaveBeenCalled();\n    });\n  });\n\n  it('uses text passed as children', () => {\n    render(<SignInWithMetamaskButton>text</SignInWithMetamaskButton>);\n    screen.getByText('text');\n  });\n\n  it('throws if multiple children provided', () => {\n    expect(() => {\n      render(\n        <SignInWithMetamaskButton>\n          <button>1</button>\n          <button>2</button>\n        </SignInWithMetamaskButton>,\n      );\n    }).toThrow();\n  });\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,mBAAwC;AACxC,wBAAsB;AACtB,IAAAA,gBAAkB;AAElB,sCAAyC;AAEzC,MAAM,+BAA+B,KAAK,GAAG;AAC7C,MAAM,gBAAgB,QAAQ;AAE9B,MAAM,YAAY;AAAA,EAChB,0BAA0B;AAC5B;AAEA,KAAK,KAAK,gBAAgB,MAAM;AAC9B,SAAO;AAAA,IACL,WAAW,CAAC,cAAmB,CAAC,UAE5B,8BAAAC,QAAA;AAAA,MAAC;AAAA;AAAA,QACE,GAAG;AAAA,QACJ,OAAO;AAAA;AAAA,IACT;AAAA,EAEN;AACF,CAAC;AAED,SAAS,+BAA+B,MAAM;AAC5C,YAAU,MAAM;AACd,YAAQ,QAAQ,KAAK,GAAG;AAAA,EAC1B,CAAC;AAED,WAAS,MAAM;AACb,YAAQ,QAAQ;AAAA,EAClB,CAAC;AAED,aAAW,MAAM;AACf,iCAA6B,UAAU;AAAA,EACzC,CAAC;AAED,KAAG,qDAAqD,YAAY;AAClE,6BAAO,8BAAAA,QAAA,cAAC,8DAAyB,CAAE;AACnC,UAAM,MAAM,oBAAO,UAAU,uBAAuB;AACpD,sBAAAC,QAAU,MAAM,GAAG;AACnB,cAAM,sBAAQ,MAAM;AAClB,aAAO,4BAA4B,EAAE,iBAAiB;AAAA,IACxD,CAAC;AAAA,EACH,CAAC;AAED,KAAG,gCAAgC,MAAM;AACvC,6BAAO,8BAAAD,QAAA,cAAC,gEAAyB,MAAI,CAA2B;AAChE,wBAAO,UAAU,MAAM;AAAA,EACzB,CAAC;AAED,KAAG,wCAAwC,MAAM;AAC/C,WAAO,MAAM;AACX;AAAA,QACE,8BAAAA,QAAA,cAAC,gEACC,8BAAAA,QAAA,cAAC,gBAAO,GAAC,GACT,8BAAAA,QAAA,cAAC,gBAAO,GAAC,CACX;AAAA,MACF;AAAA,IACF,CAAC,EAAE,QAAQ;AAAA,EACb,CAAC;AACH,CAAC;", "names": ["import_react", "React", "userEvent"]}