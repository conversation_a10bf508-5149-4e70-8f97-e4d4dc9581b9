"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var import_react = require("@testing-library/react");
var import_user_event = __toESM(require("@testing-library/user-event"));
var import_react2 = __toESM(require("react"));
var import_SignInWithMetamaskButton = require("../SignInWithMetamaskButton");
const mockAuthenticatewithMetamask = jest.fn();
const originalError = console.error;
const mockClerk = {
  authenticateWithMetamask: mockAuthenticatewithMetamask
};
jest.mock("../withClerk", () => {
  return {
    withClerk: (Component) => (props) => /* @__PURE__ */ import_react2.default.createElement(
      Component,
      {
        ...props,
        clerk: mockClerk
      }
    )
  };
});
describe("<SignInWithMetamaskButton/>", () => {
  beforeAll(() => {
    console.error = jest.fn();
  });
  afterAll(() => {
    console.error = originalError;
  });
  beforeEach(() => {
    mockAuthenticatewithMetamask.mockReset();
  });
  it("calls clerk.authenticateWithMetamask when clicked", async () => {
    (0, import_react.render)(/* @__PURE__ */ import_react2.default.createElement(import_SignInWithMetamaskButton.SignInWithMetamaskButton, null));
    const btn = import_react.screen.getByText("Sign in with Metamask");
    import_user_event.default.click(btn);
    await (0, import_react.waitFor)(() => {
      expect(mockAuthenticatewithMetamask).toHaveBeenCalled();
    });
  });
  it("uses text passed as children", () => {
    (0, import_react.render)(/* @__PURE__ */ import_react2.default.createElement(import_SignInWithMetamaskButton.SignInWithMetamaskButton, null, "text"));
    import_react.screen.getByText("text");
  });
  it("throws if multiple children provided", () => {
    expect(() => {
      (0, import_react.render)(
        /* @__PURE__ */ import_react2.default.createElement(import_SignInWithMetamaskButton.SignInWithMetamaskButton, null, /* @__PURE__ */ import_react2.default.createElement("button", null, "1"), /* @__PURE__ */ import_react2.default.createElement("button", null, "2"))
      );
    }).toThrow();
  });
});
//# sourceMappingURL=SignInWithMetamaskButton.test.js.map