{"version": 3, "sources": ["../../../../src/components/__tests__/SignInButton.test.tsx"], "sourcesContent": ["import { render, screen, waitFor } from '@testing-library/react';\nimport userEvent from '@testing-library/user-event';\nimport React from 'react';\n\nimport { SignInButton } from '../SignInButton';\n\nconst mockRedirectToSignIn = jest.fn();\nconst originalError = console.error;\n\nconst mockClerk = {\n  redirectToSignIn: mockRedirectToSignIn,\n} as any;\n\njest.mock('../withClerk', () => {\n  return {\n    withClerk: (Component: any) => (props: any) => {\n      return (\n        <Component\n          {...props}\n          clerk={mockClerk}\n        />\n      );\n    },\n  };\n});\n\nconst url = 'https://www.clerk.com';\n\ndescribe('<SignInButton/>', () => {\n  beforeAll(() => {\n    console.error = jest.fn();\n  });\n\n  afterAll(() => {\n    console.error = originalError;\n  });\n\n  beforeEach(() => {\n    mockRedirectToSignIn.mockReset();\n  });\n\n  it('calls clerk.redirectToSignIn when clicked', async () => {\n    render(<SignInButton />);\n    const btn = screen.getByText('Sign in');\n    userEvent.click(btn);\n    await waitFor(() => {\n      expect(mockRedirectToSignIn).toHaveBeenCalled();\n    });\n  });\n\n  it('handles redirectUrl prop', async () => {\n    render(<SignInButton redirectUrl={url} />);\n    const btn = screen.getByText('Sign in');\n    userEvent.click(btn);\n    await waitFor(() => {\n      expect(mockRedirectToSignIn).toHaveBeenCalledWith({ redirectUrl: url });\n    });\n  });\n\n  it('handles afterSignInUrl prop', async () => {\n    render(<SignInButton afterSignInUrl={url} />);\n    const btn = screen.getByText('Sign in');\n    userEvent.click(btn);\n    await waitFor(() => {\n      expect(mockRedirectToSignIn).toHaveBeenCalledWith({\n        afterSignInUrl: url,\n      });\n    });\n  });\n\n  it('handles afterSignUpUrl prop', async () => {\n    render(<SignInButton afterSignUpUrl={url} />);\n    const btn = screen.getByText('Sign in');\n    userEvent.click(btn);\n    await waitFor(() => {\n      expect(mockRedirectToSignIn).toHaveBeenCalledWith({\n        afterSignUpUrl: url,\n      });\n    });\n  });\n\n  it('renders passed button and calls both click handlers', async () => {\n    const handler = jest.fn();\n    render(\n      <SignInButton>\n        <button onClick={handler}>custom button</button>\n      </SignInButton>,\n    );\n    const btn = screen.getByText('custom button');\n    userEvent.click(btn);\n    await waitFor(() => {\n      expect(handler).toHaveBeenCalled();\n      expect(mockRedirectToSignIn).toHaveBeenCalled();\n    });\n  });\n\n  it('uses text passed as children', async () => {\n    render(<SignInButton>text</SignInButton>);\n    screen.getByText('text');\n  });\n\n  it('throws if multiple children provided', async () => {\n    expect(() => {\n      render(\n        <SignInButton>\n          <button>1</button>\n          <button>2</button>\n        </SignInButton>,\n      );\n    }).toThrow();\n  });\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,mBAAwC;AACxC,wBAAsB;AACtB,IAAAA,gBAAkB;AAElB,0BAA6B;AAE7B,MAAM,uBAAuB,KAAK,GAAG;AACrC,MAAM,gBAAgB,QAAQ;AAE9B,MAAM,YAAY;AAAA,EAChB,kBAAkB;AACpB;AAEA,KAAK,KAAK,gBAAgB,MAAM;AAC9B,SAAO;AAAA,IACL,WAAW,CAAC,cAAmB,CAAC,UAAe;AAC7C,aACE,8BAAAC,QAAA;AAAA,QAAC;AAAA;AAAA,UACE,GAAG;AAAA,UACJ,OAAO;AAAA;AAAA,MACT;AAAA,IAEJ;AAAA,EACF;AACF,CAAC;AAED,MAAM,MAAM;AAEZ,SAAS,mBAAmB,MAAM;AAChC,YAAU,MAAM;AACd,YAAQ,QAAQ,KAAK,GAAG;AAAA,EAC1B,CAAC;AAED,WAAS,MAAM;AACb,YAAQ,QAAQ;AAAA,EAClB,CAAC;AAED,aAAW,MAAM;AACf,yBAAqB,UAAU;AAAA,EACjC,CAAC;AAED,KAAG,6CAA6C,YAAY;AAC1D,6BAAO,8BAAAA,QAAA,cAAC,sCAAa,CAAE;AACvB,UAAM,MAAM,oBAAO,UAAU,SAAS;AACtC,sBAAAC,QAAU,MAAM,GAAG;AACnB,cAAM,sBAAQ,MAAM;AAClB,aAAO,oBAAoB,EAAE,iBAAiB;AAAA,IAChD,CAAC;AAAA,EACH,CAAC;AAED,KAAG,4BAA4B,YAAY;AACzC,6BAAO,8BAAAD,QAAA,cAAC,oCAAa,aAAa,KAAK,CAAE;AACzC,UAAM,MAAM,oBAAO,UAAU,SAAS;AACtC,sBAAAC,QAAU,MAAM,GAAG;AACnB,cAAM,sBAAQ,MAAM;AAClB,aAAO,oBAAoB,EAAE,qBAAqB,EAAE,aAAa,IAAI,CAAC;AAAA,IACxE,CAAC;AAAA,EACH,CAAC;AAED,KAAG,+BAA+B,YAAY;AAC5C,6BAAO,8BAAAD,QAAA,cAAC,oCAAa,gBAAgB,KAAK,CAAE;AAC5C,UAAM,MAAM,oBAAO,UAAU,SAAS;AACtC,sBAAAC,QAAU,MAAM,GAAG;AACnB,cAAM,sBAAQ,MAAM;AAClB,aAAO,oBAAoB,EAAE,qBAAqB;AAAA,QAChD,gBAAgB;AAAA,MAClB,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AAED,KAAG,+BAA+B,YAAY;AAC5C,6BAAO,8BAAAD,QAAA,cAAC,oCAAa,gBAAgB,KAAK,CAAE;AAC5C,UAAM,MAAM,oBAAO,UAAU,SAAS;AACtC,sBAAAC,QAAU,MAAM,GAAG;AACnB,cAAM,sBAAQ,MAAM;AAClB,aAAO,oBAAoB,EAAE,qBAAqB;AAAA,QAChD,gBAAgB;AAAA,MAClB,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AAED,KAAG,uDAAuD,YAAY;AACpE,UAAM,UAAU,KAAK,GAAG;AACxB;AAAA,MACE,8BAAAD,QAAA,cAAC,wCACC,8BAAAA,QAAA,cAAC,YAAO,SAAS,WAAS,eAAa,CACzC;AAAA,IACF;AACA,UAAM,MAAM,oBAAO,UAAU,eAAe;AAC5C,sBAAAC,QAAU,MAAM,GAAG;AACnB,cAAM,sBAAQ,MAAM;AAClB,aAAO,OAAO,EAAE,iBAAiB;AACjC,aAAO,oBAAoB,EAAE,iBAAiB;AAAA,IAChD,CAAC;AAAA,EACH,CAAC;AAED,KAAG,gCAAgC,YAAY;AAC7C,6BAAO,8BAAAD,QAAA,cAAC,wCAAa,MAAI,CAAe;AACxC,wBAAO,UAAU,MAAM;AAAA,EACzB,CAAC;AAED,KAAG,wCAAwC,YAAY;AACrD,WAAO,MAAM;AACX;AAAA,QACE,8BAAAA,QAAA,cAAC,wCACC,8BAAAA,QAAA,cAAC,gBAAO,GAAC,GACT,8BAAAA,QAAA,cAAC,gBAAO,GAAC,CACX;AAAA,MACF;AAAA,IACF,CAAC,EAAE,QAAQ;AAAA,EACb,CAAC;AACH,CAAC;", "names": ["import_react", "React", "userEvent"]}