"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var import_react = require("@testing-library/react");
var import_user_event = __toESM(require("@testing-library/user-event"));
var import_react2 = __toESM(require("react"));
var import_SignUpButton = require("../SignUpButton");
const mockRedirectToSignUp = jest.fn();
const originalError = console.error;
const mockClerk = {
  redirectToSignUp: mockRedirectToSignUp
};
jest.mock("../withClerk", () => {
  return {
    withClerk: (Component) => (props) => {
      return /* @__PURE__ */ import_react2.default.createElement(
        Component,
        {
          ...props,
          clerk: mockClerk
        }
      );
    }
  };
});
const url = "https://www.clerk.com";
describe("<SignUpButton/>", () => {
  beforeAll(() => {
    console.error = jest.fn();
  });
  afterAll(() => {
    console.error = originalError;
  });
  beforeEach(() => {
    mockRedirectToSignUp.mockReset();
  });
  it("calls clerk.redirectToSignUp when clicked", async () => {
    (0, import_react.render)(/* @__PURE__ */ import_react2.default.createElement(import_SignUpButton.SignUpButton, null));
    const btn = import_react.screen.getByText("Sign up");
    import_user_event.default.click(btn);
    await (0, import_react.waitFor)(() => {
      expect(mockRedirectToSignUp).toHaveBeenCalled();
    });
  });
  it("handles redirectUrl prop", async () => {
    (0, import_react.render)(/* @__PURE__ */ import_react2.default.createElement(import_SignUpButton.SignUpButton, { redirectUrl: url }));
    const btn = import_react.screen.getByText("Sign up");
    import_user_event.default.click(btn);
    await (0, import_react.waitFor)(() => {
      expect(mockRedirectToSignUp).toHaveBeenCalledWith({ redirectUrl: url });
    });
  });
  it("handles afterSignUpUrl prop", async () => {
    (0, import_react.render)(/* @__PURE__ */ import_react2.default.createElement(import_SignUpButton.SignUpButton, { afterSignUpUrl: url }));
    const btn = import_react.screen.getByText("Sign up");
    import_user_event.default.click(btn);
    await (0, import_react.waitFor)(() => {
      expect(mockRedirectToSignUp).toHaveBeenCalledWith({
        afterSignUpUrl: url
      });
    });
  });
  it("handles afterSignUpUrl prop", async () => {
    (0, import_react.render)(/* @__PURE__ */ import_react2.default.createElement(import_SignUpButton.SignUpButton, { afterSignUpUrl: url }));
    const btn = import_react.screen.getByText("Sign up");
    import_user_event.default.click(btn);
    await (0, import_react.waitFor)(() => {
      expect(mockRedirectToSignUp).toHaveBeenCalledWith({
        afterSignUpUrl: url
      });
    });
  });
  it("renders passed button and calls both click handlers", async () => {
    const handler = jest.fn();
    (0, import_react.render)(
      /* @__PURE__ */ import_react2.default.createElement(import_SignUpButton.SignUpButton, null, /* @__PURE__ */ import_react2.default.createElement("button", { onClick: handler }, "custom button"))
    );
    const btn = import_react.screen.getByText("custom button");
    import_user_event.default.click(btn);
    await (0, import_react.waitFor)(() => {
      expect(handler).toHaveBeenCalled();
      expect(mockRedirectToSignUp).toHaveBeenCalled();
    });
  });
  it("uses text passed as children", async () => {
    (0, import_react.render)(/* @__PURE__ */ import_react2.default.createElement(import_SignUpButton.SignUpButton, null, "text"));
    import_react.screen.getByText("text");
  });
  it("throws if multiple children provided", async () => {
    expect(() => {
      (0, import_react.render)(
        /* @__PURE__ */ import_react2.default.createElement(import_SignUpButton.SignUpButton, null, /* @__PURE__ */ import_react2.default.createElement("button", null, "1"), /* @__PURE__ */ import_react2.default.createElement("button", null, "2"))
      );
    }).toThrow();
  });
});
//# sourceMappingURL=SignUpButton.test.js.map