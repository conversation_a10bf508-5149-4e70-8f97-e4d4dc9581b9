"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var withSession_exports = {};
__export(withSession_exports, {
  WithSession: () => WithSession,
  withSession: () => withSession
});
module.exports = __toCommonJS(withSession_exports);
var import_react = __toESM(require("react"));
var import_SessionContext = require("../contexts/SessionContext");
var import_errors = require("../errors");
const withSession = (Component, displayName) => {
  displayName = displayName || Component.displayName || Component.name || "Component";
  Component.displayName = displayName;
  const HOC = (props) => {
    const session = (0, import_SessionContext.useSessionContext)();
    if (!session) {
      return null;
    }
    return /* @__PURE__ */ import_react.default.createElement(
      Component,
      {
        ...props,
        session
      }
    );
  };
  HOC.displayName = `withSession(${displayName})`;
  return HOC;
};
const WithSession = ({ children }) => {
  const session = (0, import_SessionContext.useSessionContext)();
  if (typeof children !== "function") {
    throw new Error(import_errors.hocChildrenNotAFunctionError);
  }
  if (!session) {
    return null;
  }
  return /* @__PURE__ */ import_react.default.createElement(import_react.default.Fragment, null, children(session));
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  WithSession,
  withSession
});
//# sourceMappingURL=withSession.js.map