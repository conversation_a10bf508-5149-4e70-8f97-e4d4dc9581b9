"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var controlComponents_exports = {};
__export(controlComponents_exports, {
  AuthenticateWithRedirectCallback: () => AuthenticateWithRedirectCallback,
  ClerkLoaded: () => ClerkLoaded,
  ClerkLoading: () => ClerkLoading,
  MultisessionAppSupport: () => MultisessionAppSupport,
  Protect: () => Protect,
  RedirectToCreateOrganization: () => RedirectToCreateOrganization,
  RedirectToOrganizationProfile: () => RedirectToOrganizationProfile,
  RedirectToSignIn: () => RedirectToSignIn,
  RedirectToSignUp: () => RedirectToSignUp,
  RedirectToUserProfile: () => RedirectToUserProfile,
  SignedIn: () => SignedIn,
  SignedOut: () => SignedOut
});
module.exports = __toCommonJS(controlComponents_exports);
var import_react = __toESM(require("react"));
var import_AuthContext = require("../contexts/AuthContext");
var import_IsomorphicClerkContext = require("../contexts/IsomorphicClerkContext");
var import_SessionContext = require("../contexts/SessionContext");
var import_StructureContext = require("../contexts/StructureContext");
var import_hooks = require("../hooks");
var import_withClerk = require("./withClerk");
const SignedIn = ({ children }) => {
  const { userId } = (0, import_AuthContext.useAuthContext)();
  if (userId) {
    return /* @__PURE__ */ import_react.default.createElement(import_react.default.Fragment, null, children);
  }
  return null;
};
const SignedOut = ({ children }) => {
  const { userId } = (0, import_AuthContext.useAuthContext)();
  if (userId === null) {
    return /* @__PURE__ */ import_react.default.createElement(import_react.default.Fragment, null, children);
  }
  return null;
};
const ClerkLoaded = ({ children }) => {
  const isomorphicClerk = (0, import_IsomorphicClerkContext.useIsomorphicClerkContext)();
  if (!isomorphicClerk.loaded) {
    return null;
  }
  return /* @__PURE__ */ import_react.default.createElement(import_StructureContext.LoadedGuarantee, null, children);
};
const ClerkLoading = ({ children }) => {
  const isomorphicClerk = (0, import_IsomorphicClerkContext.useIsomorphicClerkContext)();
  if (isomorphicClerk.loaded) {
    return null;
  }
  return /* @__PURE__ */ import_react.default.createElement(import_react.default.Fragment, null, children);
};
const Protect = ({ children, fallback, ...restAuthorizedParams }) => {
  const { isLoaded, has, userId } = (0, import_hooks.useAuth)();
  if (!isLoaded) {
    return null;
  }
  const unauthorized = /* @__PURE__ */ import_react.default.createElement(import_react.default.Fragment, null, fallback != null ? fallback : null);
  const authorized = /* @__PURE__ */ import_react.default.createElement(import_react.default.Fragment, null, children);
  if (!userId) {
    return unauthorized;
  }
  if (typeof restAuthorizedParams.condition === "function") {
    if (restAuthorizedParams.condition(has)) {
      return authorized;
    }
    return unauthorized;
  }
  if (restAuthorizedParams.role || restAuthorizedParams.permission) {
    if (has(restAuthorizedParams)) {
      return authorized;
    }
    return unauthorized;
  }
  return authorized;
};
const RedirectToSignIn = (0, import_withClerk.withClerk)(({ clerk, ...props }) => {
  const { client, session } = clerk;
  const { __unstable__environment } = clerk;
  const hasActiveSessions = client.activeSessions && client.activeSessions.length > 0;
  import_react.default.useEffect(() => {
    if (session === null && hasActiveSessions && __unstable__environment) {
      const { afterSignOutOneUrl } = __unstable__environment.displayConfig;
      void clerk.navigate(afterSignOutOneUrl);
    } else {
      void clerk.redirectToSignIn(props);
    }
  }, []);
  return null;
}, "RedirectToSignIn");
const RedirectToSignUp = (0, import_withClerk.withClerk)(({ clerk, ...props }) => {
  import_react.default.useEffect(() => {
    void clerk.redirectToSignUp(props);
  }, []);
  return null;
}, "RedirectToSignUp");
const RedirectToUserProfile = (0, import_withClerk.withClerk)(({ clerk }) => {
  import_react.default.useEffect(() => {
    clerk.redirectToUserProfile();
  }, []);
  return null;
}, "RedirectToUserProfile");
const RedirectToOrganizationProfile = (0, import_withClerk.withClerk)(({ clerk }) => {
  import_react.default.useEffect(() => {
    clerk.redirectToOrganizationProfile();
  }, []);
  return null;
}, "RedirectToOrganizationProfile");
const RedirectToCreateOrganization = (0, import_withClerk.withClerk)(({ clerk }) => {
  import_react.default.useEffect(() => {
    clerk.redirectToCreateOrganization();
  }, []);
  return null;
}, "RedirectToCreateOrganization");
const AuthenticateWithRedirectCallback = (0, import_withClerk.withClerk)(
  ({ clerk, ...handleRedirectCallbackParams }) => {
    import_react.default.useEffect(() => {
      void clerk.handleRedirectCallback(handleRedirectCallbackParams);
    }, []);
    return null;
  },
  "AuthenticateWithRedirectCallback"
);
const MultisessionAppSupport = ({ children }) => {
  const session = (0, import_SessionContext.useSessionContext)();
  return /* @__PURE__ */ import_react.default.createElement(import_react.default.Fragment, { key: session ? session.id : "no-users" }, children);
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  AuthenticateWithRedirectCallback,
  ClerkLoaded,
  ClerkLoading,
  MultisessionAppSupport,
  Protect,
  RedirectToCreateOrganization,
  RedirectToOrganizationProfile,
  RedirectToSignIn,
  RedirectToSignUp,
  RedirectToUserProfile,
  SignedIn,
  SignedOut
});
//# sourceMappingURL=controlComponents.js.map