"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, "default"), secondTarget && __copyProps(secondTarget, mod, "default"));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var components_exports = {};
__export(components_exports, {
  AuthenticateWithRedirectCallback: () => import_controlComponents.AuthenticateWithRedirectCallback,
  ClerkLoaded: () => import_controlComponents.ClerkLoaded,
  ClerkLoading: () => import_controlComponents.ClerkLoading,
  CreateOrganization: () => import_uiComponents.CreateOrganization,
  GoogleOneTap: () => import_uiComponents.GoogleOneTap,
  MultisessionAppSupport: () => import_controlComponents.MultisessionAppSupport,
  OrganizationList: () => import_uiComponents.OrganizationList,
  OrganizationProfile: () => import_uiComponents.OrganizationProfile,
  OrganizationSwitcher: () => import_uiComponents.OrganizationSwitcher,
  Protect: () => import_controlComponents.Protect,
  RedirectToCreateOrganization: () => import_controlComponents.RedirectToCreateOrganization,
  RedirectToOrganizationProfile: () => import_controlComponents.RedirectToOrganizationProfile,
  RedirectToSignIn: () => import_controlComponents.RedirectToSignIn,
  RedirectToSignUp: () => import_controlComponents.RedirectToSignUp,
  RedirectToUserProfile: () => import_controlComponents.RedirectToUserProfile,
  SignIn: () => import_uiComponents.SignIn,
  SignUp: () => import_uiComponents.SignUp,
  SignedIn: () => import_controlComponents.SignedIn,
  SignedOut: () => import_controlComponents.SignedOut,
  UserButton: () => import_uiComponents.UserButton,
  UserProfile: () => import_uiComponents.UserProfile
});
module.exports = __toCommonJS(components_exports);
var import_uiComponents = require("./uiComponents");
var import_controlComponents = require("./controlComponents");
__reExport(components_exports, require("./withClerk"), module.exports);
__reExport(components_exports, require("./withUser"), module.exports);
__reExport(components_exports, require("./withSession"), module.exports);
__reExport(components_exports, require("./SignInButton"), module.exports);
__reExport(components_exports, require("./SignUpButton"), module.exports);
__reExport(components_exports, require("./SignOutButton"), module.exports);
__reExport(components_exports, require("./SignInWithMetamaskButton"), module.exports);
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  AuthenticateWithRedirectCallback,
  ClerkLoaded,
  ClerkLoading,
  CreateOrganization,
  GoogleOneTap,
  MultisessionAppSupport,
  OrganizationList,
  OrganizationProfile,
  OrganizationSwitcher,
  Protect,
  RedirectToCreateOrganization,
  RedirectToOrganizationProfile,
  RedirectToSignIn,
  RedirectToSignUp,
  RedirectToUserProfile,
  SignIn,
  SignUp,
  SignedIn,
  SignedOut,
  UserButton,
  UserProfile,
  ...require("./withClerk"),
  ...require("./withUser"),
  ...require("./withSession"),
  ...require("./SignInButton"),
  ...require("./SignUpButton"),
  ...require("./SignOutButton"),
  ...require("./SignInWithMetamaskButton")
});
//# sourceMappingURL=index.js.map