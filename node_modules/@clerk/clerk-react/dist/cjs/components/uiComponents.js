"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var uiComponents_exports = {};
__export(uiComponents_exports, {
  CreateOrganization: () => CreateOrganization,
  GoogleOneTap: () => GoogleOneTap,
  OrganizationList: () => OrganizationList,
  OrganizationProfile: () => OrganizationProfile,
  OrganizationProfileLink: () => OrganizationProfileLink,
  OrganizationProfilePage: () => OrganizationProfilePage,
  OrganizationSwitcher: () => OrganizationSwitcher,
  SignIn: () => SignIn,
  SignUp: () => SignUp,
  UserButton: () => UserButton,
  UserProfile: () => UserProfile,
  UserProfileLink: () => UserProfileLink,
  UserProfilePage: () => UserProfilePage
});
module.exports = __toCommonJS(uiComponents_exports);
var import_shared = require("@clerk/shared");
var import_react = __toESM(require("react"));
var import_errors = require("../errors");
var import_utils = require("../utils");
var import_withClerk = require("./withClerk");
const isMountProps = (props) => {
  return "mount" in props;
};
const isOpenProps = (props) => {
  return "open" in props;
};
class Portal extends import_react.default.PureComponent {
  constructor() {
    super(...arguments);
    this.portalRef = import_react.default.createRef();
  }
  componentDidUpdate(prevProps) {
    var _a, _b, _c, _d;
    if (!isMountProps(prevProps) || !isMountProps(this.props)) {
      return;
    }
    if (prevProps.props.appearance !== this.props.props.appearance || ((_b = (_a = prevProps.props) == null ? void 0 : _a.customPages) == null ? void 0 : _b.length) !== ((_d = (_c = this.props.props) == null ? void 0 : _c.customPages) == null ? void 0 : _d.length)) {
      this.props.updateProps({ node: this.portalRef.current, props: this.props.props });
    }
  }
  componentDidMount() {
    if (this.portalRef.current) {
      if (isMountProps(this.props)) {
        this.props.mount(this.portalRef.current, this.props.props);
      }
      if (isOpenProps(this.props)) {
        this.props.open(this.props.props);
      }
    }
  }
  componentWillUnmount() {
    if (this.portalRef.current) {
      if (isMountProps(this.props)) {
        this.props.unmount(this.portalRef.current);
      }
      if (isOpenProps(this.props)) {
        this.props.close();
      }
    }
  }
  render() {
    var _a, _b;
    return /* @__PURE__ */ import_react.default.createElement(import_react.default.Fragment, null, /* @__PURE__ */ import_react.default.createElement("div", { ref: this.portalRef }), isMountProps(this.props) && ((_b = (_a = this.props) == null ? void 0 : _a.customPagesPortals) == null ? void 0 : _b.map((portal, index) => (0, import_react.createElement)(portal, { key: index }))));
  }
}
const SignIn = (0, import_withClerk.withClerk)(({ clerk, ...props }) => {
  return /* @__PURE__ */ import_react.default.createElement(
    Portal,
    {
      mount: clerk.mountSignIn,
      unmount: clerk.unmountSignIn,
      updateProps: clerk.__unstable__updateProps,
      props
    }
  );
}, "SignIn");
const SignUp = (0, import_withClerk.withClerk)(({ clerk, ...props }) => {
  return /* @__PURE__ */ import_react.default.createElement(
    Portal,
    {
      mount: clerk.mountSignUp,
      unmount: clerk.unmountSignUp,
      updateProps: clerk.__unstable__updateProps,
      props
    }
  );
}, "SignUp");
function UserProfilePage({ children }) {
  (0, import_shared.logErrorInDevMode)(import_errors.userProfilePageRenderedError);
  return /* @__PURE__ */ import_react.default.createElement(import_react.default.Fragment, null, children);
}
function UserProfileLink({ children }) {
  (0, import_shared.logErrorInDevMode)(import_errors.userProfileLinkRenderedError);
  return /* @__PURE__ */ import_react.default.createElement(import_react.default.Fragment, null, children);
}
const _UserProfile = (0, import_withClerk.withClerk)(
  ({ clerk, ...props }) => {
    const { customPages, customPagesPortals } = (0, import_utils.useUserProfileCustomPages)(props.children);
    return /* @__PURE__ */ import_react.default.createElement(
      Portal,
      {
        mount: clerk.mountUserProfile,
        unmount: clerk.unmountUserProfile,
        updateProps: clerk.__unstable__updateProps,
        props: { ...props, customPages },
        customPagesPortals
      }
    );
  },
  "UserProfile"
);
const UserProfile = Object.assign(_UserProfile, {
  Page: UserProfilePage,
  Link: UserProfileLink
});
const _UserButton = (0, import_withClerk.withClerk)(
  ({ clerk, ...props }) => {
    const { customPages, customPagesPortals } = (0, import_utils.useUserProfileCustomPages)(props.children);
    const userProfileProps = Object.assign(props.userProfileProps || {}, { customPages });
    return /* @__PURE__ */ import_react.default.createElement(
      Portal,
      {
        mount: clerk.mountUserButton,
        unmount: clerk.unmountUserButton,
        updateProps: clerk.__unstable__updateProps,
        props: { ...props, userProfileProps },
        customPagesPortals
      }
    );
  },
  "UserButton"
);
const UserButton = Object.assign(_UserButton, {
  UserProfilePage,
  UserProfileLink
});
function OrganizationProfilePage({ children }) {
  (0, import_shared.logErrorInDevMode)(import_errors.organizationProfilePageRenderedError);
  return /* @__PURE__ */ import_react.default.createElement(import_react.default.Fragment, null, children);
}
function OrganizationProfileLink({ children }) {
  (0, import_shared.logErrorInDevMode)(import_errors.organizationProfileLinkRenderedError);
  return /* @__PURE__ */ import_react.default.createElement(import_react.default.Fragment, null, children);
}
const _OrganizationProfile = (0, import_withClerk.withClerk)(
  ({ clerk, ...props }) => {
    const { customPages, customPagesPortals } = (0, import_utils.useOrganizationProfileCustomPages)(props.children);
    return /* @__PURE__ */ import_react.default.createElement(
      Portal,
      {
        mount: clerk.mountOrganizationProfile,
        unmount: clerk.unmountOrganizationProfile,
        updateProps: clerk.__unstable__updateProps,
        props: { ...props, customPages },
        customPagesPortals
      }
    );
  },
  "OrganizationProfile"
);
const OrganizationProfile = Object.assign(_OrganizationProfile, {
  Page: OrganizationProfilePage,
  Link: OrganizationProfileLink
});
const CreateOrganization = (0, import_withClerk.withClerk)(({ clerk, ...props }) => {
  return /* @__PURE__ */ import_react.default.createElement(
    Portal,
    {
      mount: clerk.mountCreateOrganization,
      unmount: clerk.unmountCreateOrganization,
      updateProps: clerk.__unstable__updateProps,
      props
    }
  );
}, "CreateOrganization");
const _OrganizationSwitcher = (0, import_withClerk.withClerk)(
  ({ clerk, ...props }) => {
    const { customPages, customPagesPortals } = (0, import_utils.useOrganizationProfileCustomPages)(props.children);
    const organizationProfileProps = Object.assign(props.organizationProfileProps || {}, { customPages });
    return /* @__PURE__ */ import_react.default.createElement(
      Portal,
      {
        mount: clerk.mountOrganizationSwitcher,
        unmount: clerk.unmountOrganizationSwitcher,
        updateProps: clerk.__unstable__updateProps,
        props: { ...props, organizationProfileProps },
        customPagesPortals
      }
    );
  },
  "OrganizationSwitcher"
);
const OrganizationSwitcher = Object.assign(_OrganizationSwitcher, {
  OrganizationProfilePage,
  OrganizationProfileLink
});
const OrganizationList = (0, import_withClerk.withClerk)(({ clerk, ...props }) => {
  return /* @__PURE__ */ import_react.default.createElement(
    Portal,
    {
      mount: clerk.mountOrganizationList,
      unmount: clerk.unmountOrganizationList,
      updateProps: clerk.__unstable__updateProps,
      props
    }
  );
}, "OrganizationList");
const GoogleOneTap = (0, import_withClerk.withClerk)(({ clerk, ...props }) => {
  return /* @__PURE__ */ import_react.default.createElement(
    Portal,
    {
      open: clerk.openGoogleOneTap,
      close: clerk.closeGoogleOneTap,
      props
    }
  );
}, "GoogleOneTap");
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  CreateOrganization,
  GoogleOneTap,
  OrganizationList,
  OrganizationProfile,
  OrganizationProfileLink,
  OrganizationProfilePage,
  OrganizationSwitcher,
  SignIn,
  SignUp,
  UserButton,
  UserProfile,
  UserProfileLink,
  UserProfilePage
});
//# sourceMappingURL=uiComponents.js.map