{"version": 3, "sources": ["../../../src/components/controlComponents.tsx"], "sourcesContent": ["import type {\n  CheckAuthorizationWithCustomPermissions,\n  HandleOAuthCallbackParams,\n  OrganizationCustomPermissionKey,\n  OrganizationCustomRoleKey,\n} from '@clerk/types';\nimport React from 'react';\n\nimport { useAuthContext } from '../contexts/AuthContext';\nimport { useIsomorphicClerkContext } from '../contexts/IsomorphicClerkContext';\nimport { useSessionContext } from '../contexts/SessionContext';\nimport { LoadedGuarantee } from '../contexts/StructureContext';\nimport { useAuth } from '../hooks';\nimport type { RedirectToSignInProps, RedirectToSignUpProps, WithClerkProp } from '../types';\nimport { withClerk } from './withClerk';\n\nexport const SignedIn = ({ children }: React.PropsWithChildren<unknown>): JSX.Element | null => {\n  const { userId } = useAuthContext();\n  if (userId) {\n    return <>{children}</>;\n  }\n  return null;\n};\n\nexport const SignedOut = ({ children }: React.PropsWithChildren<unknown>): JSX.Element | null => {\n  const { userId } = useAuthContext();\n  if (userId === null) {\n    return <>{children}</>;\n  }\n  return null;\n};\n\nexport const ClerkLoaded = ({ children }: React.PropsWithChildren<unknown>): JSX.Element | null => {\n  const isomorphicClerk = useIsomorphicClerkContext();\n  if (!isomorphicClerk.loaded) {\n    return null;\n  }\n  return <LoadedGuarantee>{children}</LoadedGuarantee>;\n};\n\nexport const ClerkLoading = ({ children }: React.PropsWithChildren<unknown>): JSX.Element | null => {\n  const isomorphicClerk = useIsomorphicClerkContext();\n  if (isomorphicClerk.loaded) {\n    return null;\n  }\n  return <>{children}</>;\n};\n\ntype ProtectProps = React.PropsWithChildren<\n  (\n    | {\n        condition?: never;\n        role: OrganizationCustomRoleKey;\n        permission?: never;\n      }\n    | {\n        condition?: never;\n        role?: never;\n        permission: OrganizationCustomPermissionKey;\n      }\n    | {\n        condition: (has: CheckAuthorizationWithCustomPermissions) => boolean;\n        role?: never;\n        permission?: never;\n      }\n    | {\n        condition?: never;\n        role?: never;\n        permission?: never;\n      }\n  ) & {\n    fallback?: React.ReactNode;\n  }\n>;\n\n/**\n * Use `<Protect/>` in order to prevent unauthenticated or unauthorized users from accessing the children passed to the component.\n *\n * Examples:\n * ```\n * <Protect permission=\"a_permission_key\" />\n * <Protect role=\"a_role_key\" />\n * <Protect condition={(has) => has({permission:\"a_permission_key\"})} />\n * <Protect condition={(has) => has({role:\"a_role_key\"})} />\n * <Protect fallback={<p>Unauthorized</p>} />\n * ```\n */\nexport const Protect = ({ children, fallback, ...restAuthorizedParams }: ProtectProps) => {\n  const { isLoaded, has, userId } = useAuth();\n\n  /**\n   * Avoid flickering children or fallback while clerk is loading sessionId or userId\n   */\n  if (!isLoaded) {\n    return null;\n  }\n\n  /**\n   * Fallback to UI provided by user or `null` if authorization checks failed\n   */\n  const unauthorized = <>{fallback ?? null}</>;\n\n  const authorized = <>{children}</>;\n\n  if (!userId) {\n    return unauthorized;\n  }\n\n  /**\n   * Check against the results of `has` called inside the callback\n   */\n  if (typeof restAuthorizedParams.condition === 'function') {\n    if (restAuthorizedParams.condition(has)) {\n      return authorized;\n    }\n    return unauthorized;\n  }\n\n  if (restAuthorizedParams.role || restAuthorizedParams.permission) {\n    if (has(restAuthorizedParams)) {\n      return authorized;\n    }\n    return unauthorized;\n  }\n\n  /**\n   * If neither of the authorization params are passed behave as the `<SignedIn/>`.\n   * If fallback is present render that instead of rendering nothing.\n   */\n  return authorized;\n};\n\nexport const RedirectToSignIn = withClerk(({ clerk, ...props }: WithClerkProp<RedirectToSignInProps>) => {\n  const { client, session } = clerk;\n  // TODO: Remove temp use of __unstable__environment\n  const { __unstable__environment } = clerk as any;\n\n  const hasActiveSessions = client.activeSessions && client.activeSessions.length > 0;\n\n  React.useEffect(() => {\n    if (session === null && hasActiveSessions && __unstable__environment) {\n      const { afterSignOutOneUrl } = __unstable__environment.displayConfig;\n      void clerk.navigate(afterSignOutOneUrl);\n    } else {\n      void clerk.redirectToSignIn(props);\n    }\n  }, []);\n\n  return null;\n}, 'RedirectToSignIn');\n\nexport const RedirectToSignUp = withClerk(({ clerk, ...props }: WithClerkProp<RedirectToSignUpProps>) => {\n  React.useEffect(() => {\n    void clerk.redirectToSignUp(props);\n  }, []);\n\n  return null;\n}, 'RedirectToSignUp');\n\nexport const RedirectToUserProfile = withClerk(({ clerk }) => {\n  React.useEffect(() => {\n    clerk.redirectToUserProfile();\n  }, []);\n\n  return null;\n}, 'RedirectToUserProfile');\n\nexport const RedirectToOrganizationProfile = withClerk(({ clerk }) => {\n  React.useEffect(() => {\n    clerk.redirectToOrganizationProfile();\n  }, []);\n\n  return null;\n}, 'RedirectToOrganizationProfile');\n\nexport const RedirectToCreateOrganization = withClerk(({ clerk }) => {\n  React.useEffect(() => {\n    clerk.redirectToCreateOrganization();\n  }, []);\n\n  return null;\n}, 'RedirectToCreateOrganization');\n\nexport const AuthenticateWithRedirectCallback = withClerk(\n  ({ clerk, ...handleRedirectCallbackParams }: WithClerkProp<HandleOAuthCallbackParams>) => {\n    React.useEffect(() => {\n      void clerk.handleRedirectCallback(handleRedirectCallbackParams);\n    }, []);\n\n    return null;\n  },\n  'AuthenticateWithRedirectCallback',\n);\n\nexport const MultisessionAppSupport = ({ children }: React.PropsWithChildren<unknown>): JSX.Element => {\n  const session = useSessionContext();\n  return <React.Fragment key={session ? session.id : 'no-users'}>{children}</React.Fragment>;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,mBAAkB;AAElB,yBAA+B;AAC/B,oCAA0C;AAC1C,4BAAkC;AAClC,8BAAgC;AAChC,mBAAwB;AAExB,uBAA0B;AAEnB,MAAM,WAAW,CAAC,EAAE,SAAS,MAA4D;AAC9F,QAAM,EAAE,OAAO,QAAI,mCAAe;AAClC,MAAI,QAAQ;AACV,WAAO,6BAAAA,QAAA,2BAAAA,QAAA,gBAAG,QAAS;AAAA,EACrB;AACA,SAAO;AACT;AAEO,MAAM,YAAY,CAAC,EAAE,SAAS,MAA4D;AAC/F,QAAM,EAAE,OAAO,QAAI,mCAAe;AAClC,MAAI,WAAW,MAAM;AACnB,WAAO,6BAAAA,QAAA,2BAAAA,QAAA,gBAAG,QAAS;AAAA,EACrB;AACA,SAAO;AACT;AAEO,MAAM,cAAc,CAAC,EAAE,SAAS,MAA4D;AACjG,QAAM,sBAAkB,yDAA0B;AAClD,MAAI,CAAC,gBAAgB,QAAQ;AAC3B,WAAO;AAAA,EACT;AACA,SAAO,6BAAAA,QAAA,cAAC,+CAAiB,QAAS;AACpC;AAEO,MAAM,eAAe,CAAC,EAAE,SAAS,MAA4D;AAClG,QAAM,sBAAkB,yDAA0B;AAClD,MAAI,gBAAgB,QAAQ;AAC1B,WAAO;AAAA,EACT;AACA,SAAO,6BAAAA,QAAA,2BAAAA,QAAA,gBAAG,QAAS;AACrB;AAyCO,MAAM,UAAU,CAAC,EAAE,UAAU,UAAU,GAAG,qBAAqB,MAAoB;AACxF,QAAM,EAAE,UAAU,KAAK,OAAO,QAAI,sBAAQ;AAK1C,MAAI,CAAC,UAAU;AACb,WAAO;AAAA,EACT;AAKA,QAAM,eAAe,6BAAAA,QAAA,2BAAAA,QAAA,gBAAG,8BAAY,IAAK;AAEzC,QAAM,aAAa,6BAAAA,QAAA,2BAAAA,QAAA,gBAAG,QAAS;AAE/B,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AAKA,MAAI,OAAO,qBAAqB,cAAc,YAAY;AACxD,QAAI,qBAAqB,UAAU,GAAG,GAAG;AACvC,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAEA,MAAI,qBAAqB,QAAQ,qBAAqB,YAAY;AAChE,QAAI,IAAI,oBAAoB,GAAG;AAC7B,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAMA,SAAO;AACT;AAEO,MAAM,uBAAmB,4BAAU,CAAC,EAAE,OAAO,GAAG,MAAM,MAA4C;AACvG,QAAM,EAAE,QAAQ,QAAQ,IAAI;AAE5B,QAAM,EAAE,wBAAwB,IAAI;AAEpC,QAAM,oBAAoB,OAAO,kBAAkB,OAAO,eAAe,SAAS;AAElF,eAAAA,QAAM,UAAU,MAAM;AACpB,QAAI,YAAY,QAAQ,qBAAqB,yBAAyB;AACpE,YAAM,EAAE,mBAAmB,IAAI,wBAAwB;AACvD,WAAK,MAAM,SAAS,kBAAkB;AAAA,IACxC,OAAO;AACL,WAAK,MAAM,iBAAiB,KAAK;AAAA,IACnC;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,SAAO;AACT,GAAG,kBAAkB;AAEd,MAAM,uBAAmB,4BAAU,CAAC,EAAE,OAAO,GAAG,MAAM,MAA4C;AACvG,eAAAA,QAAM,UAAU,MAAM;AACpB,SAAK,MAAM,iBAAiB,KAAK;AAAA,EACnC,GAAG,CAAC,CAAC;AAEL,SAAO;AACT,GAAG,kBAAkB;AAEd,MAAM,4BAAwB,4BAAU,CAAC,EAAE,MAAM,MAAM;AAC5D,eAAAA,QAAM,UAAU,MAAM;AACpB,UAAM,sBAAsB;AAAA,EAC9B,GAAG,CAAC,CAAC;AAEL,SAAO;AACT,GAAG,uBAAuB;AAEnB,MAAM,oCAAgC,4BAAU,CAAC,EAAE,MAAM,MAAM;AACpE,eAAAA,QAAM,UAAU,MAAM;AACpB,UAAM,8BAA8B;AAAA,EACtC,GAAG,CAAC,CAAC;AAEL,SAAO;AACT,GAAG,+BAA+B;AAE3B,MAAM,mCAA+B,4BAAU,CAAC,EAAE,MAAM,MAAM;AACnE,eAAAA,QAAM,UAAU,MAAM;AACpB,UAAM,6BAA6B;AAAA,EACrC,GAAG,CAAC,CAAC;AAEL,SAAO;AACT,GAAG,8BAA8B;AAE1B,MAAM,uCAAmC;AAAA,EAC9C,CAAC,EAAE,OAAO,GAAG,6BAA6B,MAAgD;AACxF,iBAAAA,QAAM,UAAU,MAAM;AACpB,WAAK,MAAM,uBAAuB,4BAA4B;AAAA,IAChE,GAAG,CAAC,CAAC;AAEL,WAAO;AAAA,EACT;AAAA,EACA;AACF;AAEO,MAAM,yBAAyB,CAAC,EAAE,SAAS,MAAqD;AACrG,QAAM,cAAU,yCAAkB;AAClC,SAAO,6BAAAA,QAAA,cAAC,aAAAA,QAAM,UAAN,EAAe,KAAK,UAAU,QAAQ,KAAK,cAAa,QAAS;AAC3E;", "names": ["React"]}