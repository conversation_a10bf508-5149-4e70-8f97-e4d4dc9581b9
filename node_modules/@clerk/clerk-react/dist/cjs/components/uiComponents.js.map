{"version": 3, "sources": ["../../../src/components/uiComponents.tsx"], "sourcesContent": ["import { logErrorInDevMode } from '@clerk/shared';\nimport type {\n  CreateOrganizationProps,\n  GoogleOneTapProps,\n  OrganizationListProps,\n  OrganizationProfileProps,\n  OrganizationSwitcherProps,\n  SignInProps,\n  SignUpProps,\n  UserButtonProps,\n  UserProfileProps,\n} from '@clerk/types';\nimport type { PropsWithChildren } from 'react';\nimport React, { createElement } from 'react';\n\nimport {\n  organizationProfileLinkRenderedError,\n  organizationProfilePageRenderedError,\n  userProfileLinkRenderedError,\n  userProfilePageRenderedError,\n} from '../errors';\nimport type {\n  MountProps,\n  OpenProps,\n  OrganizationProfileLinkProps,\n  OrganizationProfilePageProps,\n  UserProfileLinkProps,\n  UserProfilePageProps,\n  WithClerkProp,\n} from '../types';\nimport { useOrganizationProfileCustomPages, useUserProfileCustomPages } from '../utils';\nimport { withClerk } from './withClerk';\n\ntype UserProfileExportType = typeof _UserProfile & {\n  Page: typeof UserProfilePage;\n  Link: typeof UserProfileLink;\n};\n\ntype UserButtonExportType = typeof _UserButton & {\n  UserProfilePage: typeof UserProfilePage;\n  UserProfileLink: typeof UserProfileLink;\n};\n\ntype UserButtonPropsWithoutCustomPages = Omit<UserButtonProps, 'userProfileProps'> & {\n  userProfileProps?: Pick<UserProfileProps, 'additionalOAuthScopes' | 'appearance'>;\n};\n\ntype OrganizationProfileExportType = typeof _OrganizationProfile & {\n  Page: typeof OrganizationProfilePage;\n  Link: typeof OrganizationProfileLink;\n};\n\ntype OrganizationSwitcherExportType = typeof _OrganizationSwitcher & {\n  OrganizationProfilePage: typeof OrganizationProfilePage;\n  OrganizationProfileLink: typeof OrganizationProfileLink;\n};\n\ntype OrganizationSwitcherPropsWithoutCustomPages = Omit<OrganizationSwitcherProps, 'organizationProfileProps'> & {\n  organizationProfileProps?: Pick<OrganizationProfileProps, 'appearance'>;\n};\n\n// README: <Portal/> should be a class pure component in order for mount and unmount\n// lifecycle props to be invoked correctly. Replacing the class component with a\n// functional component wrapped with a React.memo is not identical to the original\n// class implementation due to React intricacies such as the useEffect’s cleanup\n// seems to run AFTER unmount, while componentWillUnmount runs BEFORE.\n\n// More information can be found at https://clerkinc.slack.com/archives/C015S0BGH8R/p1624891993016300\n\n// The function Portal implementation is commented out for future reference.\n\n// const Portal = React.memo(({ props, mount, unmount }: MountProps) => {\n//   const portalRef = React.createRef<HTMLDivElement>();\n\n//   useEffect(() => {\n//     if (portalRef.current) {\n//       mount(portalRef.current, props);\n//     }\n//     return () => {\n//       if (portalRef.current) {\n//         unmount(portalRef.current);\n//       }\n//     };\n//   }, []);\n\n//   return <div ref={portalRef} />;\n// });\n\n// Portal.displayName = 'ClerkPortal';\n\nconst isMountProps = (props: any): props is MountProps => {\n  return 'mount' in props;\n};\n\nconst isOpenProps = (props: any): props is OpenProps => {\n  return 'open' in props;\n};\n\nclass Portal extends React.PureComponent<MountProps | OpenProps> {\n  private portalRef = React.createRef<HTMLDivElement>();\n\n  componentDidUpdate(prevProps: Readonly<MountProps | OpenProps>) {\n    if (!isMountProps(prevProps) || !isMountProps(this.props)) {\n      return;\n    }\n\n    if (\n      prevProps.props.appearance !== this.props.props.appearance ||\n      prevProps.props?.customPages?.length !== this.props.props?.customPages?.length\n    ) {\n      this.props.updateProps({ node: this.portalRef.current, props: this.props.props });\n    }\n  }\n\n  componentDidMount() {\n    if (this.portalRef.current) {\n      if (isMountProps(this.props)) {\n        this.props.mount(this.portalRef.current, this.props.props);\n      }\n\n      if (isOpenProps(this.props)) {\n        this.props.open(this.props.props);\n      }\n    }\n  }\n\n  componentWillUnmount() {\n    if (this.portalRef.current) {\n      if (isMountProps(this.props)) {\n        this.props.unmount(this.portalRef.current);\n      }\n      if (isOpenProps(this.props)) {\n        this.props.close();\n      }\n    }\n  }\n\n  render() {\n    return (\n      <>\n        <div ref={this.portalRef} />\n        {isMountProps(this.props) &&\n          this.props?.customPagesPortals?.map((portal, index) => createElement(portal, { key: index }))}\n      </>\n    );\n  }\n}\n\nexport const SignIn = withClerk(({ clerk, ...props }: WithClerkProp<SignInProps>) => {\n  return (\n    <Portal\n      mount={clerk.mountSignIn}\n      unmount={clerk.unmountSignIn}\n      updateProps={(clerk as any).__unstable__updateProps}\n      props={props}\n    />\n  );\n}, 'SignIn');\n\nexport const SignUp = withClerk(({ clerk, ...props }: WithClerkProp<SignUpProps>) => {\n  return (\n    <Portal\n      mount={clerk.mountSignUp}\n      unmount={clerk.unmountSignUp}\n      updateProps={(clerk as any).__unstable__updateProps}\n      props={props}\n    />\n  );\n}, 'SignUp');\n\nexport function UserProfilePage({ children }: PropsWithChildren<UserProfilePageProps>) {\n  logErrorInDevMode(userProfilePageRenderedError);\n  return <>{children}</>;\n}\n\nexport function UserProfileLink({ children }: PropsWithChildren<UserProfileLinkProps>) {\n  logErrorInDevMode(userProfileLinkRenderedError);\n  return <>{children}</>;\n}\n\nconst _UserProfile = withClerk(\n  ({ clerk, ...props }: WithClerkProp<PropsWithChildren<Omit<UserProfileProps, 'customPages'>>>) => {\n    const { customPages, customPagesPortals } = useUserProfileCustomPages(props.children);\n    return (\n      <Portal\n        mount={clerk.mountUserProfile}\n        unmount={clerk.unmountUserProfile}\n        updateProps={(clerk as any).__unstable__updateProps}\n        props={{ ...props, customPages }}\n        customPagesPortals={customPagesPortals}\n      />\n    );\n  },\n  'UserProfile',\n);\n\nexport const UserProfile: UserProfileExportType = Object.assign(_UserProfile, {\n  Page: UserProfilePage,\n  Link: UserProfileLink,\n});\n\nconst _UserButton = withClerk(\n  ({ clerk, ...props }: WithClerkProp<PropsWithChildren<UserButtonPropsWithoutCustomPages>>) => {\n    const { customPages, customPagesPortals } = useUserProfileCustomPages(props.children);\n    const userProfileProps = Object.assign(props.userProfileProps || {}, { customPages });\n    return (\n      <Portal\n        mount={clerk.mountUserButton}\n        unmount={clerk.unmountUserButton}\n        updateProps={(clerk as any).__unstable__updateProps}\n        props={{ ...props, userProfileProps }}\n        customPagesPortals={customPagesPortals}\n      />\n    );\n  },\n  'UserButton',\n);\n\nexport const UserButton: UserButtonExportType = Object.assign(_UserButton, {\n  UserProfilePage,\n  UserProfileLink,\n});\n\nexport function OrganizationProfilePage({ children }: PropsWithChildren<OrganizationProfilePageProps>) {\n  logErrorInDevMode(organizationProfilePageRenderedError);\n  return <>{children}</>;\n}\n\nexport function OrganizationProfileLink({ children }: PropsWithChildren<OrganizationProfileLinkProps>) {\n  logErrorInDevMode(organizationProfileLinkRenderedError);\n  return <>{children}</>;\n}\n\nconst _OrganizationProfile = withClerk(\n  ({ clerk, ...props }: WithClerkProp<PropsWithChildren<Omit<OrganizationProfileProps, 'customPages'>>>) => {\n    const { customPages, customPagesPortals } = useOrganizationProfileCustomPages(props.children);\n    return (\n      <Portal\n        mount={clerk.mountOrganizationProfile}\n        unmount={clerk.unmountOrganizationProfile}\n        updateProps={(clerk as any).__unstable__updateProps}\n        props={{ ...props, customPages }}\n        customPagesPortals={customPagesPortals}\n      />\n    );\n  },\n  'OrganizationProfile',\n);\n\nexport const OrganizationProfile: OrganizationProfileExportType = Object.assign(_OrganizationProfile, {\n  Page: OrganizationProfilePage,\n  Link: OrganizationProfileLink,\n});\n\nexport const CreateOrganization = withClerk(({ clerk, ...props }: WithClerkProp<CreateOrganizationProps>) => {\n  return (\n    <Portal\n      mount={clerk.mountCreateOrganization}\n      unmount={clerk.unmountCreateOrganization}\n      updateProps={(clerk as any).__unstable__updateProps}\n      props={props}\n    />\n  );\n}, 'CreateOrganization');\n\nconst _OrganizationSwitcher = withClerk(\n  ({ clerk, ...props }: WithClerkProp<PropsWithChildren<OrganizationSwitcherPropsWithoutCustomPages>>) => {\n    const { customPages, customPagesPortals } = useOrganizationProfileCustomPages(props.children);\n    const organizationProfileProps = Object.assign(props.organizationProfileProps || {}, { customPages });\n    return (\n      <Portal\n        mount={clerk.mountOrganizationSwitcher}\n        unmount={clerk.unmountOrganizationSwitcher}\n        updateProps={(clerk as any).__unstable__updateProps}\n        props={{ ...props, organizationProfileProps }}\n        customPagesPortals={customPagesPortals}\n      />\n    );\n  },\n  'OrganizationSwitcher',\n);\n\nexport const OrganizationSwitcher: OrganizationSwitcherExportType = Object.assign(_OrganizationSwitcher, {\n  OrganizationProfilePage,\n  OrganizationProfileLink,\n});\n\nexport const OrganizationList = withClerk(({ clerk, ...props }: WithClerkProp<OrganizationListProps>) => {\n  return (\n    <Portal\n      mount={clerk.mountOrganizationList}\n      unmount={clerk.unmountOrganizationList}\n      updateProps={(clerk as any).__unstable__updateProps}\n      props={props}\n    />\n  );\n}, 'OrganizationList');\n\nexport const GoogleOneTap = withClerk(({ clerk, ...props }: WithClerkProp<GoogleOneTapProps>) => {\n  return (\n    <Portal\n      open={clerk.openGoogleOneTap}\n      close={clerk.closeGoogleOneTap}\n      props={props}\n    />\n  );\n}, 'GoogleOneTap');\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAAkC;AAalC,mBAAqC;AAErC,oBAKO;AAUP,mBAA6E;AAC7E,uBAA0B;AA2D1B,MAAM,eAAe,CAAC,UAAoC;AACxD,SAAO,WAAW;AACpB;AAEA,MAAM,cAAc,CAAC,UAAmC;AACtD,SAAO,UAAU;AACnB;AAEA,MAAM,eAAe,aAAAA,QAAM,cAAsC;AAAA,EAAjE;AAAA;AACE,SAAQ,YAAY,aAAAA,QAAM,UAA0B;AAAA;AAAA,EAEpD,mBAAmB,WAA6C;AArGlE;AAsGI,QAAI,CAAC,aAAa,SAAS,KAAK,CAAC,aAAa,KAAK,KAAK,GAAG;AACzD;AAAA,IACF;AAEA,QACE,UAAU,MAAM,eAAe,KAAK,MAAM,MAAM,gBAChD,qBAAU,UAAV,mBAAiB,gBAAjB,mBAA8B,cAAW,gBAAK,MAAM,UAAX,mBAAkB,gBAAlB,mBAA+B,SACxE;AACA,WAAK,MAAM,YAAY,EAAE,MAAM,KAAK,UAAU,SAAS,OAAO,KAAK,MAAM,MAAM,CAAC;AAAA,IAClF;AAAA,EACF;AAAA,EAEA,oBAAoB;AAClB,QAAI,KAAK,UAAU,SAAS;AAC1B,UAAI,aAAa,KAAK,KAAK,GAAG;AAC5B,aAAK,MAAM,MAAM,KAAK,UAAU,SAAS,KAAK,MAAM,KAAK;AAAA,MAC3D;AAEA,UAAI,YAAY,KAAK,KAAK,GAAG;AAC3B,aAAK,MAAM,KAAK,KAAK,MAAM,KAAK;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AAAA,EAEA,uBAAuB;AACrB,QAAI,KAAK,UAAU,SAAS;AAC1B,UAAI,aAAa,KAAK,KAAK,GAAG;AAC5B,aAAK,MAAM,QAAQ,KAAK,UAAU,OAAO;AAAA,MAC3C;AACA,UAAI,YAAY,KAAK,KAAK,GAAG;AAC3B,aAAK,MAAM,MAAM;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AAAA,EAEA,SAAS;AAzIX;AA0II,WACE,6BAAAA,QAAA,2BAAAA,QAAA,gBACE,6BAAAA,QAAA,cAAC,SAAI,KAAK,KAAK,WAAW,GACzB,aAAa,KAAK,KAAK,OACtB,gBAAK,UAAL,mBAAY,uBAAZ,mBAAgC,IAAI,CAAC,QAAQ,cAAU,4BAAc,QAAQ,EAAE,KAAK,MAAM,CAAC,GAC/F;AAAA,EAEJ;AACF;AAEO,MAAM,aAAS,4BAAU,CAAC,EAAE,OAAO,GAAG,MAAM,MAAkC;AACnF,SACE,6BAAAA,QAAA;AAAA,IAAC;AAAA;AAAA,MACC,OAAO,MAAM;AAAA,MACb,SAAS,MAAM;AAAA,MACf,aAAc,MAAc;AAAA,MAC5B;AAAA;AAAA,EACF;AAEJ,GAAG,QAAQ;AAEJ,MAAM,aAAS,4BAAU,CAAC,EAAE,OAAO,GAAG,MAAM,MAAkC;AACnF,SACE,6BAAAA,QAAA;AAAA,IAAC;AAAA;AAAA,MACC,OAAO,MAAM;AAAA,MACb,SAAS,MAAM;AAAA,MACf,aAAc,MAAc;AAAA,MAC5B;AAAA;AAAA,EACF;AAEJ,GAAG,QAAQ;AAEJ,SAAS,gBAAgB,EAAE,SAAS,GAA4C;AACrF,uCAAkB,0CAA4B;AAC9C,SAAO,6BAAAA,QAAA,2BAAAA,QAAA,gBAAG,QAAS;AACrB;AAEO,SAAS,gBAAgB,EAAE,SAAS,GAA4C;AACrF,uCAAkB,0CAA4B;AAC9C,SAAO,6BAAAA,QAAA,2BAAAA,QAAA,gBAAG,QAAS;AACrB;AAEA,MAAM,mBAAe;AAAA,EACnB,CAAC,EAAE,OAAO,GAAG,MAAM,MAA+E;AAChG,UAAM,EAAE,aAAa,mBAAmB,QAAI,wCAA0B,MAAM,QAAQ;AACpF,WACE,6BAAAA,QAAA;AAAA,MAAC;AAAA;AAAA,QACC,OAAO,MAAM;AAAA,QACb,SAAS,MAAM;AAAA,QACf,aAAc,MAAc;AAAA,QAC5B,OAAO,EAAE,GAAG,OAAO,YAAY;AAAA,QAC/B;AAAA;AAAA,IACF;AAAA,EAEJ;AAAA,EACA;AACF;AAEO,MAAM,cAAqC,OAAO,OAAO,cAAc;AAAA,EAC5E,MAAM;AAAA,EACN,MAAM;AACR,CAAC;AAED,MAAM,kBAAc;AAAA,EAClB,CAAC,EAAE,OAAO,GAAG,MAAM,MAA2E;AAC5F,UAAM,EAAE,aAAa,mBAAmB,QAAI,wCAA0B,MAAM,QAAQ;AACpF,UAAM,mBAAmB,OAAO,OAAO,MAAM,oBAAoB,CAAC,GAAG,EAAE,YAAY,CAAC;AACpF,WACE,6BAAAA,QAAA;AAAA,MAAC;AAAA;AAAA,QACC,OAAO,MAAM;AAAA,QACb,SAAS,MAAM;AAAA,QACf,aAAc,MAAc;AAAA,QAC5B,OAAO,EAAE,GAAG,OAAO,iBAAiB;AAAA,QACpC;AAAA;AAAA,IACF;AAAA,EAEJ;AAAA,EACA;AACF;AAEO,MAAM,aAAmC,OAAO,OAAO,aAAa;AAAA,EACzE;AAAA,EACA;AACF,CAAC;AAEM,SAAS,wBAAwB,EAAE,SAAS,GAAoD;AACrG,uCAAkB,kDAAoC;AACtD,SAAO,6BAAAA,QAAA,2BAAAA,QAAA,gBAAG,QAAS;AACrB;AAEO,SAAS,wBAAwB,EAAE,SAAS,GAAoD;AACrG,uCAAkB,kDAAoC;AACtD,SAAO,6BAAAA,QAAA,2BAAAA,QAAA,gBAAG,QAAS;AACrB;AAEA,MAAM,2BAAuB;AAAA,EAC3B,CAAC,EAAE,OAAO,GAAG,MAAM,MAAuF;AACxG,UAAM,EAAE,aAAa,mBAAmB,QAAI,gDAAkC,MAAM,QAAQ;AAC5F,WACE,6BAAAA,QAAA;AAAA,MAAC;AAAA;AAAA,QACC,OAAO,MAAM;AAAA,QACb,SAAS,MAAM;AAAA,QACf,aAAc,MAAc;AAAA,QAC5B,OAAO,EAAE,GAAG,OAAO,YAAY;AAAA,QAC/B;AAAA;AAAA,IACF;AAAA,EAEJ;AAAA,EACA;AACF;AAEO,MAAM,sBAAqD,OAAO,OAAO,sBAAsB;AAAA,EACpG,MAAM;AAAA,EACN,MAAM;AACR,CAAC;AAEM,MAAM,yBAAqB,4BAAU,CAAC,EAAE,OAAO,GAAG,MAAM,MAA8C;AAC3G,SACE,6BAAAA,QAAA;AAAA,IAAC;AAAA;AAAA,MACC,OAAO,MAAM;AAAA,MACb,SAAS,MAAM;AAAA,MACf,aAAc,MAAc;AAAA,MAC5B;AAAA;AAAA,EACF;AAEJ,GAAG,oBAAoB;AAEvB,MAAM,4BAAwB;AAAA,EAC5B,CAAC,EAAE,OAAO,GAAG,MAAM,MAAqF;AACtG,UAAM,EAAE,aAAa,mBAAmB,QAAI,gDAAkC,MAAM,QAAQ;AAC5F,UAAM,2BAA2B,OAAO,OAAO,MAAM,4BAA4B,CAAC,GAAG,EAAE,YAAY,CAAC;AACpG,WACE,6BAAAA,QAAA;AAAA,MAAC;AAAA;AAAA,QACC,OAAO,MAAM;AAAA,QACb,SAAS,MAAM;AAAA,QACf,aAAc,MAAc;AAAA,QAC5B,OAAO,EAAE,GAAG,OAAO,yBAAyB;AAAA,QAC5C;AAAA;AAAA,IACF;AAAA,EAEJ;AAAA,EACA;AACF;AAEO,MAAM,uBAAuD,OAAO,OAAO,uBAAuB;AAAA,EACvG;AAAA,EACA;AACF,CAAC;AAEM,MAAM,uBAAmB,4BAAU,CAAC,EAAE,OAAO,GAAG,MAAM,MAA4C;AACvG,SACE,6BAAAA,QAAA;AAAA,IAAC;AAAA;AAAA,MACC,OAAO,MAAM;AAAA,MACb,SAAS,MAAM;AAAA,MACf,aAAc,MAAc;AAAA,MAC5B;AAAA;AAAA,EACF;AAEJ,GAAG,kBAAkB;AAEd,MAAM,mBAAe,4BAAU,CAAC,EAAE,OAAO,GAAG,MAAM,MAAwC;AAC/F,SACE,6BAAAA,QAAA;AAAA,IAAC;AAAA;AAAA,MACC,MAAM,MAAM;AAAA,MACZ,OAAO,MAAM;AAAA,MACb;AAAA;AAAA,EACF;AAEJ,GAAG,cAAc;", "names": ["React"]}